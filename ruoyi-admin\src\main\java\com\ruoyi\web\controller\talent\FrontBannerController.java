package com.ruoyi.web.controller.talent;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Banner;
import com.ruoyi.system.service.IBannerService;

/**
 * 前台轮播图 API Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/talent/banner")
public class FrontBannerController extends BaseController
{
    @Autowired
    private IBannerService bannerService;

    /**
     * 获取有效的轮播图列表
     */
    @GetMapping("/list")
    public AjaxResult getActiveBanners()
    {
        List<Banner> list = bannerService.selectActiveBannerList();
        return success(list);
    }
} 