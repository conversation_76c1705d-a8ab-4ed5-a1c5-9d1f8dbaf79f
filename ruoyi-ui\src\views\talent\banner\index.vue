<template>
  <div class="app-container">
    <h2>轮播图管理</h2>
    <div v-if="loadError" class="error-message">
      <h3>页面加载出错</h3>
      <p>{{ errorMessage }}</p>
    </div>
    
    <div v-else>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="queryParams.title"
            placeholder="请输入标题"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['talent:banner:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['talent:banner:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['talent:banner:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['talent:banner:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-bell"
            size="mini"
            @click="testConsoleLog"
          >测试日志</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-s-promotion"
            size="mini"
            @click="testDialog"
          >测试对话框</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="bannerList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="80" />
        <el-table-column label="标题" align="center" prop="title" :show-overflow-tooltip="true" />
        <el-table-column label="图片" align="center" prop="imageUrl" width="100">
          <template slot-scope="scope">
            <el-image 
              v-if="scope && scope.row && scope.row.imageUrl"
              style="width: 80px; height: 50px"
              :src="getImageUrl(scope.row.imageUrl)"
              :preview-src-list="[getImageUrl(scope.row.imageUrl)]">
            </el-image>
            <span v-else>暂无图片</span>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="orderNum" width="80" />
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope && scope.row" :type="scope.row.status === '0' ? 'success' : 'danger'">
              {{ scope.row.status === '0' ? '正常' : '停用' }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <div v-if="scope && scope.row">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['talent:banner:edit']"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['talent:banner:remove']"
              >删除</el-button>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- Vue3 ElementPlus 对话框 -->
      <el-dialog
        v-model="open"
        :title="title"
        width="700px"
        :close-on-click-modal="false"
        :show-close="true"
        destroy-on-close
        @closed="handleDialogClosed"
        @open="handleDialogOpened"
        draggable>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" />
          </el-form-item>
          <el-form-item label="图片" prop="imageUrl">
            <el-upload
              :action="uploadImgUrl"
              :headers="headers"
              list-type="picture-card"
              :file-list="fileList"
              :before-upload="handleBeforeUpload"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-remove="handleRemove"
              :on-preview="handlePicturePreview"
              :limit="1"
              :on-exceed="handleExceed">
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  图片格式：jpg、png、jpeg、gif，且不超过2MB
                </div>
              </template>
            </el-upload>
            <el-dialog v-model="previewVisible" title="图片预览">
              <img :src="previewImage" style="width: 100%;" />
            </el-dialog>
          </el-form-item>
          <el-form-item label="链接地址" prop="linkUrl">
            <el-input v-model="form.linkUrl" placeholder="请输入链接地址，不填则无跳转" />
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <el-input type="textarea" v-model="form.content" :rows="4" placeholder="请输入内容"/>
          </el-form-item>
          <el-form-item label="显示顺序" prop="orderNum">
            <el-input-number v-model="form.orderNum" :min="0" :max="999" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="0">正常</el-radio>
              <el-radio label="1">停用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="open = false">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listBanner, getBanner, delBanner, addBanner, updateBanner } from "@/api/talent/banner";
import { getDicts } from "@/api/system/dict/data";
import { Plus } from '@element-plus/icons-vue'; // 导入Element Plus的图标
import { getToken } from "@/utils/auth";

export default {
  name: "Banner",
  components: {
    Plus // 注册图标组件
  },
  data() {
    return {
      // Debug variables
      loadError: false,
      errorMessage: "",
      
      // 上传相关变量
      uploadImgUrl: import.meta.env.VITE_APP_BASE_API + "/common/upload", // 上传图片服务务器地址
      headers: { Authorization: "Bearer " + getToken() },
      
      // 静态状态选项（备用）
      statusOptions: [
        { value: "0", label: "正常" },
        { value: "1", label: "停用" }
      ],
      
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 轮播图表格数据
      bannerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        status: null
      },
      // 表单参数
      form: {
        id: null,
        title: "",
        imageUrl: "",
        linkUrl: "",
        content: "",
        orderNum: 0,
        status: "0",
        remark: "",
        isBlobImage: false
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        imageUrl: [
          { required: true, message: "图片路径不能为空", trigger: "change" }
        ],
        orderNum: [
          { required: true, message: "显示顺序不能为空", trigger: "blur" }
        ]
      },
      // 文件列表
      fileList: [],
      // 预览相关变量
      previewVisible: false,
      previewImage: ""
    };
  },
  created() {
    try {
      // 尝试手动加载字典数据
      this.loadDictData();
      
      // 加载轮播图列表
      this.getList();
    } catch (err) {
      this.loadError = true;
      this.errorMessage = "组件初始化错误: " + (err.message || JSON.stringify(err));
      console.error("组件初始化错误:", err);
    }
  },
  methods: {
    /** 手动加载字典数据 */
    loadDictData() {
      getDicts("sys_normal_disable").then(response => {
        if (response && response.data) {
          this.statusOptions = response.data.map(item => ({
            value: item.dictValue,
            label: item.dictLabel
          }));
        }
      }).catch(err => {
        console.warn("Failed to load dictionary: sys_normal_disable, using default values", err);
        // 已经有默认值，不需要处理错误
      });
    },
    /** 查询轮播图列表 */
    getList() {
      this.loading = true;
      listBanner(this.queryParams).then(response => {
        if (response && response.rows) {
          this.bannerList = response.rows;
          this.total = response.total || 0;
        } else {
          this.bannerList = [];
          this.total = 0;
          console.warn("API response missing rows data:", response);
        }
        this.loading = false;
      }).catch(err => {
        this.loadError = true;
        this.errorMessage = "API 调用失败: " + (err.message || JSON.stringify(err));
        console.error("API call error:", err);
        this.loading = false;
        this.bannerList = [];
        this.total = 0;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      console.log("重置表单");
      this.form = {
        id: null,
        title: "",
        imageUrl: "",
        linkUrl: "",
        content: "",
        orderNum: 0,
        status: "0",
        remark: "",
        isBlobImage: false
      };
      
      this.fileList = [];
      
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields();
        console.log("表单字段重置完成");
      } else {
        console.warn("表单引用未找到，无法重置字段");
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      if (this.$refs.queryForm) {
        this.$refs.queryForm.resetFields();
      }
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      console.log("handleAdd方法被调用");
      try {
        // 重置表单
        this.reset();
        console.log("表单已重置");
        
        // 设置标题和初始化表单数据
        this.title = "添加轮播图";
        
        // 打开对话框
        this.open = true;
        console.log("对话框已打开，状态为:", this.open);
      } catch (err) {
        console.error("打开新增对话框时出错:", err);
        this.loadError = true;
        this.errorMessage = "打开新增对话框出错: " + (err.message || JSON.stringify(err));
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getBanner(id).then(response => {
        this.form = response.data;
        
        // 处理图片显示
        if (this.form.imageUrl) {
          const baseUrl = import.meta.env.VITE_APP_BASE_API;
          let fileUrl = this.form.imageUrl;
          
          // 检查是否需要添加基础URL
          if (fileUrl.indexOf('http') !== 0 && fileUrl.indexOf(baseUrl) !== 0) {
            fileUrl = baseUrl + fileUrl;
          }
          
          // 设置文件列表
          this.fileList = [{
            name: this.form.imageUrl.substring(this.form.imageUrl.lastIndexOf('/') + 1),
            url: fileUrl
          }];
        }
        
        this.open = true;
        this.title = "修改轮播图";
      });
    },
    /** 提交按钮 */
    submitForm() {
      console.log("提交表单按钮被点击");
      
      if (!this.$refs.formRef) {
        console.error("表单引用未找到");
        alert("表单引用未找到，无法提交");
        return;
      }
      
      this.$refs.formRef.validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '提交中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          
          try {
            if (this.form.id) {
              // 修改
              updateBanner(this.form).then(response => {
                this.$message.success("修改成功");
                this.open = false;
                this.getList();
                loading.close();
              }).catch(err => {
                console.error("修改失败:", err);
                loading.close();
              });
            } else {
              // 新增
              addBanner(this.form).then(response => {
                this.$message.success("新增成功");
                this.open = false;
                this.getList();
                loading.close();
              }).catch(err => {
                console.error("新增失败:", err);
                loading.close();
              });
            }
          } catch (err) {
            console.error("提交表单时发生错误:", err);
            this.$message.error("提交失败，请检查控制台错误信息");
            loading.close();
          }
        } else {
          console.log("表单验证失败");
          return false;
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除轮播图编号为"' + ids + '"的数据项?').then(function() {
        return delBanner(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('talent/banner/export', {
        ...this.queryParams
      }, `轮播图_${new Date().getTime()}.xlsx`)
    },
    
    // 处理对话框打开事件
    handleDialogOpened() {
      console.log("对话框打开事件触发了");
      
      // 如果是编辑模式且图片URL存在
      if (this.form.id && this.form.imageUrl) {
        console.log("正在处理编辑模式的图片:", this.form.imageUrl);
        // 如果是blob URL，需要特殊处理
        if (this.form.imageUrl.startsWith('blob:')) {
          console.log("检测到blob URL，将尝试加载图片");
          // 在这里可以考虑添加图片预览处理逻辑
        }
      }
    },
    
    // 处理对话框关闭事件
    handleDialogClosed() {
      console.log("对话框关闭事件触发了");
      this.reset();
    },
    
    // 处理图片变更
    handleImageChange(file) {
      const isImage = file.raw.type.indexOf("image/") !== -1;
      if (!isImage) {
        this.$message.error("只能上传图片文件!");
        return;
      }
      if (file.size / 1024 / 1024 > 2) {
        this.$message.error("图片大小不能超过 2MB!");
        return;
      }
      this.form.imageUrl = URL.createObjectURL(file.raw);
    },
    
    // 测试console.log是否显示
    testConsoleLog() {
      console.log("=== 测试控制台日志 ===");
      console.log("这是一条测试消息");
      console.log("如果你能看到这个，说明控制台日志正常工作");
      console.log("========================");
      alert("测试日志已输出，请查看浏览器控制台");
    },
    
    // 测试对话框
    testDialog() {
      console.log("测试对话框");
      
      // 重置并设置表单数据
      this.form = {
        id: null,
        title: "测试标题",
        imageUrl: "",
        linkUrl: "",
        content: "这是一个测试内容",
        orderNum: 1,
        status: "0",
        remark: "测试备注",
        isBlobImage: false
      };
      
      // 设置标题
      this.title = "测试对话框";
      
      // 直接设置对话框可见
      this.open = true;
      console.log("对话框已设置为可见:", this.open);
    },
    
    // 检查是否为blob URL
    isBlobUrl(url) {
      return url && typeof url === 'string' && url.startsWith('blob:');
    },
    // 处理文件上传前的逻辑
    handleBeforeUpload(file) {
      const isImage = file.type.indexOf("image/") !== -1;
      if (!isImage) {
        this.$message.error("只能上传图片文件!");
        return false;
      }
      if (file.size / 1024 / 1024 > 2) {
        this.$message.error("图片大小不能超过 2MB!");
        return false;
      }
      return true;
    },
    // 处理文件上传成功后的逻辑
    handleUploadSuccess(response, file) {
      if (response.code === 200) {
        this.form.imageUrl = response.fileName;
        this.form.isBlobImage = false;
        this.$message.success("上传成功");
      } else {
        this.$message.error(response.msg || "上传失败");
        // 移除上传失败的文件
        this.fileList = [];
      }
    },
    // 处理文件上传错误后的逻辑
    handleUploadError(err) {
      this.$message.error("上传失败，请检查控制台错误信息");
    },
    // 处理文件移除后的逻辑
    handleRemove(file) {
      this.form.imageUrl = "";
      this.form.isBlobImage = false;
    },
    // 处理图片预览逻辑
    handlePicturePreview(file) {
      this.previewImage = file.url;
      this.previewVisible = true;
    },
    // 处理文件超出限制后的逻辑
    handleExceed(files) {
      this.$message.error("只能上传一张图片!");
    },
    // 获取完整的图片URL
    getImageUrl(url) {
      if (!url) return "";
      
      const baseUrl = import.meta.env.VITE_APP_BASE_API;
      
      // 如果已经是完整URL则直接返回
      if (url.indexOf('http') === 0) {
        return url;
      }
      
      // 如果已经包含基础URL则直接返回
      if (url.indexOf(baseUrl) === 0) {
        return url;
      }
      
      // 否则添加基础URL
      return baseUrl + url;
    }
  }
};
</script>

<style scoped>
.error-message {
  padding: 20px;
  background-color: #fff3f3;
  border: 1px solid #ffa3a3;
  border-radius: 4px;
  margin-bottom: 20px;
}

.el-dialog .el-form {
  padding: 10px 20px;
}

.dialog-footer {
  text-align: right;
  padding-top: 10px;
}

.el-form-item__content .el-upload-list--picture-card .el-upload-list__item,
.el-form-item__content .el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.blob-image-warning {
  margin-bottom: 10px;
}

.blob-image-warning .el-alert {
  margin-bottom: 10px;
}

.blob-image-warning .el-alert .el-alert__description {
  margin-top: 10px;
}

.mt10 {
  margin-top: 10px;
}
</style> 