<!-- Generated by SpecStory -->

# Upload avatar error troubleshooting (2025-07-17 08:23Z)

_**User**_

request.js? [sm]:74 请求失败: {errMsg: "uploadFile:fail"}
fail @ request.js? [sm]:74
(anonymous) @ uni-h5.es.js? [sm]:2712
(anonymous) @ uni-h5.es.js? [sm]:2809
invokeCallback @ uni-h5.es.js? [sm]:2735
invokeFail @ uni-h5.es.js? [sm]:2992
reject @ uni-h5.es.js? [sm]:3068
xhr.onerror @ uni-h5.es.js? [sm]:20777
error (async)
upload @ uni-h5.es.js? [sm]:20775
Promise.then (async)
(anonymous) @ uni-h5.es.js? [sm]:20807
(anonymous) @ uni-h5.es.js? [sm]:3066
invokeApi @ uni-h5.es.js? [sm]:2917
(anonymous) @ uni-h5.es.js? [sm]:2933
(anonymous) @ request.js? [sm]:42
报错了
(anonymous) @ request.js? [sm]:41
upload @ request.js? [sm]:100
uploadAvatar @ talent-market.js? [sm]:88
chooseAvatar @ resume-edit.vue:456
async function (async)
chooseAvatar @ resume-edit.vue:439
callWithErrorHandling @ vue.runtime.esm.js? [sm]:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js? [sm]:1425
invoker @ vue.runtime.esm.js? [sm]:10263
talent-market.js? [sm]:105 上传头像失败: {errMsg: "uploadFile:fail"}errMsg: "uploadFile:fail"__proto__: Object
uploadAvatar @ talent-market.js? [sm]:105
async function (async)
uploadAvatar @ talent-market.js? [sm]:88
chooseAvatar @ resume-edit.vue:456
async function (async)
chooseAvatar @ resume-edit.vue:439
callWithErrorHandling @ vue.runtime.esm.js? [sm]:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js? [sm]:1425
invoker @ vue.runtime.esm.js? [sm]:10263
resume-edit.vue:458 上传结果: {code: 500, message: "上传失败"}
resume-edit.vue:477 头像上传失败: Error: 上传失败
    at chooseAvatar (resume-edit.vue:473)
chooseAvatar @ resume-edit.vue:477
async function (async)
chooseAvatar @ resume-edit.vue:439
callWithErrorHandling @ vue.runtime.esm.js? [sm]:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js? [sm]:1425
invoker @ vue.runtime.esm.js? [sm]:10263
uni-h5.es.js? [sm]:22197 请注意 showLoading 与 hideLoading 必须配对使用
hidePopup @ uni-h5.es.js? [sm]:22197
(anonymous) @ uni-h5.es.js? [sm]:22181
(anonymous) @ uni-h5.es.js? [sm]:3066
invokeApi @ uni-h5.es.js? [sm]:2917
(anonymous) @ uni-h5.es.js? [sm]:2939
(anonymous) @ uni-h5.es.js? [sm]:2938
chooseAvatar @ resume-edit.vue:483
async function (async)
chooseAvatar @ resume-edit.vue:439
callWithErrorHandling @ vue.runtime.esm.js? [sm]:1418
callWithAsyncErrorHandling @ vue.runtime.esm.js? [sm]:1425
invoker @ vue.runtime.esm.js? [sm]:10263
uni-h5.es.js? [sm]:20797 POST http://localhost:8085/dev-api/talent/resume/avatar net::ERR_CONNECTION_REFUSED

---

_**Assistant**_

你的报错信息和日志显示：

- `uploadFile:fail`，即 uni-app 的 `uni.uploadFile` 请求失败。
- 具体的网络错误是：`net::ERR_CONNECTION_REFUSED`，请求的地址是 `http://localhost:8085/dev-api/talent/resume/avatar`。
- 后端返回了 500，message: "上传失败"。

### 问题分析

1. **前端请求已发出，但后端未响应**  
   `ERR_CONNECTION_REFUSED` 说明前端请求的接口地址没有服务监听，或者端口/路径错误，或者本地服务没启动。

2. **接口地址问题**  
   你请求的是 `http://localhost:8085/dev-api/talent/resume/avatar`，请确认：
   - 你的后端服务是否在本地 8085 端口启动？
   - 路径 `/dev-api/talent/resume/avatar` 是否正确？
   - 前端的 `config.baseUrl` 配置是否正确？

3. **跨域/代理问题**  
   如果你用的是本地开发环境，通常会用 `vue.config.js` 或 `uni.dev.js` 配置代理，把 `/dev-api` 代理到后端。请确认代理配置是否生效。

4. **后端接口实现问题**  
   如果后端接口有 bug 或没实现，也会导致 500 错误。

---

## 排查建议

### 1. 检查后端服务

- 确认后端服务已启动，并监听在 `localhost:8085`。
- 用浏览器或 Postman 访问 `http://localhost:8085/dev-api/talent/resume/avatar`，看是否能访问（哪怕是 405，也说明服务起来了）。
- 检查后端日志，是否有收到请求、报错信息。

### 2. 检查前端接口地址

- 检查 `config.baseUrl` 的值，确保它是 `http://localhost:8085/dev-api`。
- 检查 `uploadAvatar` 方法，确认它的 URL 拼接逻辑没问题。

### 3. 检查代理配置

- 如果你用的是 `vue.config.js` 代理，确认如下配置是否存在并生效：

  ```js
  devServer: {
    proxy: {
      '/dev-api': {
        target: 'http://localhost:8085',
        changeOrigin: true,
        pathRewrite: { '^/dev-api': '' }
      }
    }
  }
  ```

- 如果用 HBuilderX，检查 `manifest.json` 或 `uni.dev.js` 的代理设置。

### 4. 检查上传代码

- 检查 `uploadAvatar` 的实现，确认 `uni.uploadFile` 的 `url`、`filePath`、`name` 参数都正确。
- 检查 token 是否正确传递。

---

## 你可以这样自查

1. **后端没启动？**  
   启动后端服务，确保 8085 端口监听。

2. **接口路径错？**  
   用 Postman 直接上传一个文件试试，确认接口能用。

3. **前端代理没生效？**  
   直接用绝对路径试试，或者修正代理配置。

4. **后端报错？**  
   看后端日志，修复后端 bug。

---

## 结论

你的问题本质是**前端请求的上传接口没有服务响应**，要么是后端没启动，要么是端口/路径错，要么是代理没生效。  
请按上面步骤逐一排查，优先确认后端服务和接口路径。

如果你需要进一步排查 `uploadAvatar` 的实现或代理配置，可以把相关代码贴出来，我帮你详细看！

---

_**User**_

服务端报错：
16:46:14.070 [http-nio-8080-exec-34] ERROR c.r.f.w.e.GlobalExceptionHandler - [handleRuntimeException,100] - 请求地址'/talent/resume/avatar',发生未知异常.
org.springframework.web.multipart.MultipartException: Current request is not a multipart request
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValueInternal(RequestParamMethodArgumentResolver.java:210)
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValue(RequestParamMethodArgumentResolver.java:193)
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:114)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.ruoyi.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.ruoyi.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:832)
16:46:14.072 [http-nio-8080-exec-34] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,208] - Resolved [org.springframework.web.multipart.MultipartException: Current request is not a multipart request]


---

_**Assistant**_



---

