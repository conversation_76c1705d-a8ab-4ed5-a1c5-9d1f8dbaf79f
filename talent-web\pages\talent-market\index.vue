<template>
  <div class="talent-market">
    <!-- 简化的加载状态 -->
    <div v-if="isPageLoading" class="loading-container">
      <div class="loading-content">
        <div class="loading-icon">⏳</div>
        <div class="loading-text">{{ loadingText || '加载中...' }}</div>
      </div>
    </div>

    <!-- 实际内容 -->
    <div v-else class="content-container">
      <!-- 顶部欢迎区域 -->
      <!-- <section class="welcome-section">
        <div class="welcome-content">
          <div class="welcome-text">
            <h1 class="welcome-title">人才市场</h1>
            <p class="welcome-subtitle">发现更好的职业机会</p>
          </div>
          <div class="welcome-stats">
            <div class="mini-stat">
              <span class="mini-stat-number">{{ totalJobs }}</span>
              <span class="mini-stat-label">在招职位</span>
            </div>
            <div class="mini-stat">
              <span class="mini-stat-number">{{ totalCompanies }}</span>
              <span class="mini-stat-label">招聘单位</span>
            </div>
          </div>
        </div>
      </section> -->

      <!-- 轮播图 -->
      <section class="banner-section">
        <swiper class="banner-swiper"
                :indicator-dots="true"
                :autoplay="true"
                indicator-active-color="#007AFF"
                indicator-color="rgba(255,255,255,0.5)"
                :circular="true"
                :interval="4000"
                :duration="500">
          <swiper-item v-for="(item, index) in bannerList" :key="index">
            <view class="swiper-item" @click="handleBannerClick(item)">
              <image class="banner-image" :src="item.imageUrl" mode="aspectFill"></image>
              <view class="banner-overlay">
                <view class="banner-title" v-if="item.title">{{ item.title }}</view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </section>

      <!-- 快捷搜索 -->
      <section class="search-section">
        <div class="search-container">
          <div class="search-bar">
            <div class="search-input-wrapper">
              <uni-icons type="search" size="18" color="#999"></uni-icons>
              <input
                type="text"
                v-model="searchKeyword"
                placeholder="搜索职位、公司..."
                @keyup.enter="handleSearch"
              >
            </div>
            <button class="search-btn" @click="handleSearch">
              <uni-icons type="search" size="16" color="#fff"></uni-icons>
            </button>
          </div>
        </div>
      </section>

      <!-- 数据统计卡片 -->
      <section class="stats-section">
        <div class="stats-grid">
          <div class="stat-card primary">
            <div class="stat-icon">
              <uni-icons type="staff" size="24" color="#007AFF"></uni-icons>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ totalJobs }}</div>
              <div class="stat-label">在招职位</div>
              <div class="stat-badge" v-if="todayNewJobs > 0">+{{ todayNewJobs }}</div>
            </div>
          </div>

          <div class="stat-card success">
            <div class="stat-icon">
              <uni-icons type="home" size="24" color="#52c41a"></uni-icons>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ totalCompanies }}</div>
              <div class="stat-label">招聘单位</div>
            </div>
          </div>

          <div class="stat-card warning">
            <div class="stat-icon">
              <uni-icons type="eye" size="24" color="#fa8c16"></uni-icons>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ formatNumber(totalViews) }}</div>
              <div class="stat-label">总浏览量</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 招聘单位 -->
      <section class="companies-section">
        <div class="section-header">
          <div class="section-title">
            <uni-icons type="home-filled" size="20" color="#007AFF"></uni-icons>
            <span class="title-text">热门单位</span>
          </div>
          <div class="section-more" @click="navigateToJobList">
            <span>查看全部</span>
            <uni-icons type="right" size="14" color="#007AFF"></uni-icons>
          </div>
        </div>

        <div class="companies-grid">
          <div v-if="companies.length === 0" class="empty-state">
            <uni-icons type="info-filled" size="48" color="#ddd"></uni-icons>
            <p class="empty-text">暂无招聘单位</p>
          </div>
          <div
            v-else
            v-for="company in companies"
            :key="company.id"
            class="company-card"
            @click="selectCompany(company)">
            <div class="company-header">
              <div class="company-avatar">
                <uni-icons type="home" size="20" color="#007AFF"></uni-icons>
              </div>
              <div class="company-info">
                <div class="company-name">{{ company.name }}</div>
                <div class="company-location">
                  <uni-icons type="location" size="12" color="#999"></uni-icons>
                  <span>{{ company.location }}</span>
                </div>
              </div>
            </div>
            <div class="company-footer">
              <div class="job-count">
                <uni-icons type="list" size="12" color="#52c41a"></uni-icons>
                <span>{{ company.jobCount }}个职位</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, onUnmounted } from 'vue'
import { getJobList, getJobDashboard, getCompanyList, applyJob, getDefaultResume, getUnreadMessageCount, getBannerList } from '@/utils/talent-market'
import { getUserInfo } from "@/utils/wx.js";
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import config from '@/config';
import authStore from '@/stores/auth.js'

const {
	proxy,
	appContext: {
		config: {
			globalProperties: global
		}
	}
} = getCurrentInstance();
// 响应式数据
const currentDate = ref(new Date().toLocaleDateString('zh-CN'))
const totalJobs = ref(0)
const todayNewJobs = ref(0)
const todayApplications = ref(0)
const totalDeliveries = ref(0)
const totalCompanies = ref(0)
const totalViews = ref(0)
const searchKeyword = ref('')
const jobs = ref([])
const companies = ref([])
const selectedCompanyId = ref(null)

// 加载状态
const isPageLoading = ref(true)
const loadingText = ref('正在加载数据...')

// 加载提示文本数组
const loadingTips = [
  '正在加载数据...',
  '获取最新职位信息...',
  '加载招聘单位...',
  '马上就好...'
]

// Banner轮播图数据
const bannerList = ref([])

// 筛选标签
const filterTags = ref([
  { id: 1, name: '招聘单位', active: true }
])

// 默认简历数据
const defaultResume = ref(null)

// 未读消息数
const unreadCount = ref(1)

// 添加分页相关变量
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const isLoading = ref(false)

// 搜索相关变量
const searchParams = ref({
  keyword: '',
  pageNum: 1,
  pageSize: 10
})

// 添加搜索框固定状态
const isSearchFixed = ref(false)

// 添加招聘单位弹窗引用
const companyPopup = ref(null)

// 添加滚动监听
const handleScroll = () => {
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  isSearchFixed.value = scrollTop > 200 // 当滚动超过200px时固定搜索框
}

// 获取单位列表
const fetchCompanies = async () => {
  try {
    const token = uni.getStorageSync('token')
    if (!token) return

    // 添加分页参数，确保能获取更多记录
    const result = await getCompanyList(token, {
      pageNum: 1,
      pageSize: 20
    })
    if (result && result.code === 200) {
      companies.value = result.rows || []
      // 如果有单位数据，默认选中第一个
      if (companies.value.length > 0) {
        selectedCompanyId.value = companies.value[0].id
      }
    }
  } catch (error) {
    console.error('获取单位列表失败:', error)
    uni.showToast({
      title: '获取单位列表失败',
      icon: 'none'
    })
  }
}

// 获取轮播图数据
const fetchBanners = async () => {
  try {
    const token = uni.getStorageSync('token')
    if (!token) return
    
    // 请求轮播图列表
    const result = await getBannerList(token)
    
    if (result && result.code === 200) {
      // 确定数据来源，可能在data或rows属性中
      const bannerData = result.rows || result.data || [];
      
      // 从配置中获取基础URL，并确保没有尾部斜杠
      const baseUrl = config.baseUrl.replace(/\/$/, '');
      
      // 处理图片路径
      bannerList.value = bannerData.map(item => {
        // 如果图片路径不是完整URL，拼接服务器地址
        if (item.imageUrl && !item.imageUrl.startsWith('http')) {
          item.imageUrl = baseUrl + item.imageUrl
        }
        return {
          id: item.id,
          imageUrl: item.imageUrl,
          title: item.title,
          link: item.linkUrl || '',
          content: item.content || ''
        }
      })
      
      // 如果没有配置轮播图，使用默认值
      if (!bannerList.value.length) {
        bannerList.value = [
          {
            imageUrl: '/static/banner/banner1.jpg',
            title: '人才招聘平台',
            link: ''
          },
          {
            imageUrl: '/static/banner/banner2.jpg',
            title: '优质职位推荐',
            link: ''
          },
          {
            imageUrl: '/static/banner/banner3.jpg',
            title: '人才培养计划',
            link: ''
          }
        ]
      }
    }
  } catch (error) {
    console.error('获取轮播图失败', error)
    // 出错时使用默认值
    bannerList.value = [
      {
        imageUrl: '/static/banner/banner1.jpg',
        title: '人才招聘平台',
        link: ''
      },
      {
        imageUrl: '/static/banner/banner2.jpg',
        title: '优质职位推荐',
        link: ''
      },
      {
        imageUrl: '/static/banner/banner3.jpg',
        title: '人才培养计划',
        link: ''
      }
    ]
  }
}

// 加载文本轮换
let loadingTextTimer = null
const startLoadingTextRotation = () => {
  let index = 0
  loadingTextTimer = setInterval(() => {
    index = (index + 1) % loadingTips.length
    loadingText.value = loadingTips[index]
  }, 1500)
}

const stopLoadingTextRotation = () => {
  if (loadingTextTimer) {
    clearInterval(loadingTextTimer)
    loadingTextTimer = null
  }
}

// 修改初始化方法
const initData = async () => {
  try {
    isPageLoading.value = true
    startLoadingTextRotation()

    const token = uni.getStorageSync('token')
    if (!token) return

    // 模拟最小加载时间，确保用户能看到加载动画
    const minLoadingTime = new Promise(resolve => setTimeout(resolve, 2000))

    // 获取统计信息
    loadingText.value = '获取统计信息...'
    const dashboardResult = await getJobDashboard(token)
    if (dashboardResult && dashboardResult.data) {
      const data = dashboardResult.data
      totalJobs.value = data.totalJobs || 0
      todayNewJobs.value = data.todayNewJobs || 0
      totalDeliveries.value = data.totalDeliveries || 0
      totalCompanies.value = data.totalCompanies || 0
      todayApplications.value = data.todayApplications || 0
      totalViews.value = data.totalViews || 0
    }

    // 获取单位列表
    loadingText.value = '加载招聘单位...'
    await fetchCompanies()

    // 获取其他数据
    loadingText.value = '准备精彩内容...'
    await Promise.all([
      fetchDefaultResume(),
      fetchUnreadMessageCount(),
      minLoadingTime // 确保最小加载时间
    ])

    loadingText.value = '加载完成！'

    // 延迟一点时间显示完成状态
    setTimeout(() => {
      isPageLoading.value = false
      stopLoadingTextRotation()
    }, 200)

  } catch (error) {
    console.error('初始化数据失败:', error)
    isPageLoading.value = false
    stopLoadingTextRotation()
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

// 修改搜索方法
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: '请输入搜索关键词',
      icon: 'none'
    })
    return
  }
  
  // 跳转到职位列表页面，携带搜索关键词参数
  uni.navigateTo({
    url: `/pages/talent-market/talent-list?keyword=${encodeURIComponent(searchKeyword.value)}`
  })
}

// 修改加载更多方法
const handleLoadMore = async () => {
  if (!hasMore.value || isLoading.value) return
  
  isLoading.value = true
  
  try {
    const token = uni.getStorageSync('token')
    if (!token) return
    
    const result = await getJobList(token, {
      keyword: searchKeyword.value,
      pageNum: pageNum.value + 1,
      pageSize: pageSize.value
    })
    
    if (result && result.code === 200) {
      const newJobs = result.rows || []
      
      if (newJobs.length > 0) {
        jobs.value = [...jobs.value, ...newJobs]
        pageNum.value++
        hasMore.value = result.total > jobs.value.length
      } else {
        hasMore.value = false
        uni.showToast({
          title: '没有更多数据了',
          icon: 'none'
        })
      }
    }
  } catch (error) {
    console.error('加载更多失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

// 修改下拉刷新方法
const handleRefresh = async () => {
  try {
    const token = uni.getStorageSync('token')
    if (!token) {
      uni.stopPullDownRefresh()
      return
    }

    // 重置分页状态
    pageNum.value = 1
    hasMore.value = true
    
    // 获取统计信息
    const dashboardResult = await getJobDashboard(token)
    if (dashboardResult && dashboardResult.data) {
      const data = dashboardResult.data
      totalJobs.value = data.totalJobs || 0
      todayNewJobs.value = data.todayNewJobs || 0
      totalDeliveries.value = data.totalDeliveries || 0
      todayApplications.value = data.todayApplications || 0
      totalViews.value = data.totalViews || 0
    }

    // 刷新单位列表
    await fetchCompanies()
    
    // 刷新轮播图数据
    await fetchBanners()
    
    // 获取其他数据
    await Promise.all([
      fetchDefaultResume(),
      fetchUnreadMessageCount()
    ])
    
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('刷新失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  } finally {
    uni.stopPullDownRefresh()
  }
}

// 获取默认简历
const fetchDefaultResume = async () => {
  try {
    const token = uni.getStorageSync('token')
    const result = await getDefaultResume(token)
    if (result && result.data) {
      defaultResume.value = result.data
    }
  } catch (error) {
    console.error('获取默认简历失败:', error)
  }
}

// 跳转到简历页面
const navigateToResume = () => {
  uni.navigateTo({
    url: '/pages/talent-market/resume'
  })
}

// 跳转到消息页面
const navigateToMessage = () => {
  uni.navigateTo({
    url: '/pages/talent-market/message'
  })
}

// 修改重置搜索方法
const resetSearch = () => {
  searchKeyword.value = ''
  handleSearch()
}

// 修改筛选标签点击事件
const toggleFilter = (tag) => {
  if (tag.id === 1) { // 招聘单位标签
    showCompanyPopup()
  } else {
    filterTags.value.forEach(t => t.active = t.id === tag.id)
  }
}

// 显示招聘单位弹窗
const showCompanyPopup = () => {
  companyPopup.value.open()
}

// 关闭招聘单位弹窗
const closeCompanyPopup = () => {
  companyPopup.value.close()
}

// 选择招聘单位
const selectCompany = async (company) => {
  selectedCompanyId.value = company.id
  // 跳转到职位列表页面，并传递公司ID
  uni.navigateTo({
    url: `/pages/talent-market/talent-list?companyId=${company.id}`
  })
}

const viewJobDetail = (jobId) => {
  uni.navigateTo({
    url: `/pages/talent-market/detail?id=${jobId}`
  })
}

const handleApply = async (jobId) => {
  try {
    await applyForJob(jobId)
    uni.showToast({
      title: '投递成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '投递失败',
      icon: 'error'
    })
  }
}

// 获取未读消息数
const fetchUnreadMessageCount = async () => {
  try {
    const token = uni.getStorageSync('token')
    const result = await getUnreadMessageCount(token)
    if (result && result.code === 200) {
      unreadCount.value = result.data
    }
  } catch (error) {
    console.error('获取未读消息数失败:', error)
  }
}

// 添加时间格式化函数
const formatTime = (timestamp) => {
  if (!timestamp) return '';

  const now = new Date();
  const date = new Date(timestamp);
  const diff = now - date;

  // 小于1小时，显示"xx分钟前"
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `${minutes}分钟前`;
  }

  // 小于24小时，显示"xx小时前"
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  }

  // 小于7天，显示"xx天前"
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000);
    return `${days}天前`;
  }

  // 超过7天，显示具体日期
  return date.toLocaleDateString('zh-CN');
}

// 添加数字格式化函数
const formatNumber = (num) => {
  if (!num) return '0';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  }
  return num.toString();
}

// 使用生命周期函数
onPullDownRefresh(() => {
  handleRefresh()
})

// 生命周期
onMounted(async () => {
  try {
    // 使用认证状态管理初始化
    const token = await authStore.initialize();
    
    if (!token) {
      console.log('未检测到token，跳转到登录页');
      // 记录当前页面路径，登录后可以跳回来
      const currentPage = encodeURIComponent(window.location.href);
      uni.setStorageSync('redirectPage', currentPage);
      
      // 跳转到登录页
      uni.redirectTo({
        url: '/pages/login/login',
        success: () => {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          })
        }
      })
      return;
    }

    // 无需再次获取用户信息，authStore已经处理
    
    // 添加滚动监听
    window.addEventListener('scroll', handleScroll);
    
    try {
      // 获取轮播图数据
      await fetchBanners();
      
      // 初始化其他数据
      await initData();
    } catch (err) {
      console.error("数据加载失败:", err);
      uni.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    }

  } catch (error) {
    console.error('初始化数据失败:', error);
    // 如果是token过期导致的错误，也跳转到登录页
    if (error.code === 401 || error.message?.includes('token')) {
      // 清除认证状态
      authStore.clear();
      
      // 记录当前页面路径
      const currentPage = encodeURIComponent(window.location.href);
      uni.setStorageSync('redirectPage', currentPage);
      
      uni.redirectTo({
        url: '/pages/login/login',
        success: () => {
          uni.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          })
        }
      })
      return;
    }
    
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
})

// 添加上拉加载更多事件监听
onReachBottom(() => {
  handleLoadMore()
})

// 在组件卸载时移除滚动监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  // 清理加载文本轮换定时器
  stopLoadingTextRotation()
})

// 修改跳转到职位列表页面的方法
const navigateToJobList = () => {
  uni.navigateTo({
    url: '/pages/talent-market/talent-list'
  })
}

// Banner点击处理
const handleBannerClick = (banner) => {
  if (banner.link) {
    // 处理外部链接或内部导航
    if (banner.link.startsWith('http')) {
      // 外部链接使用webview打开
      uni.navigateTo({
        url: '/pages/webview/webview?url=' + encodeURIComponent(banner.link)
      })
    } else {
      // 内部页面直接跳转
      uni.navigateTo({
        url: banner.link
      })
    }
  } else if (banner.content) {
    // 如果有内容但没有链接，显示内容详情
    uni.navigateTo({
      url: '/pages/talent-market/news-detail?id=' + banner.id
    })
  }
}
</script>

<style scoped>
/* 简化的加载状态样式 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f8f9fa;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  padding: 20px;
}

.loading-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}












.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: #666;
  background: linear-gradient(135deg, #409EFF, #36CFC9);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
  animation: textPulse 2s infinite ease-in-out;
}

@keyframes textPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* 内容容器 */
.content-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, #007AFF 0%, #00C6FF 100%);
  padding: 24px 16px;
  margin: 0 -16px 16px -16px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  font-size: 28px;
  font-weight: 700;
  color: #fff;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.welcome-stats {
  display: flex;
  gap: 16px;
}

.mini-stat {
  text-align: center;
  color: #fff;
}

.mini-stat-number {
  display: block;
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
}

.mini-stat-label {
  font-size: 12px;
  opacity: 0.8;
  margin-top: 4px;
  display: block;
}

/* 轮播图区域 */
.banner-section {
  margin: 0 16px 16px 16px;
}

.banner-swiper {
  height: 180px;
  border-radius: 12px;
  overflow: hidden;
}

.swiper-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
  padding: 16px;
}

.banner-title {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 搜索区域 */
.search-section {
  margin: 0 16px 20px 16px;
}

.search-container {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-bar {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.search-input-wrapper input {
  flex: 1;
  border: none;
  background: none;
  font-size: 14px;
  color: #333;
  outline: none;
}

.search-input-wrapper input::placeholder {
  color: #999;
}

.search-btn {
  padding: 12px 16px;
  background: #007AFF;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
}

.talent-market {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 80px;
}

/* 统计区域 */
.stats-section {
  margin: 0 16px 20px 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  text-align: center;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
}

.stat-card.primary::before {
  background: #007AFF;
}

.stat-card.success::before {
  background: #52c41a;
}

.stat-card.warning::before {
  background: #fa8c16;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin: 0 auto 12px auto;
}

.stat-card.primary .stat-icon {
  background: rgba(0, 122, 255, 0.1);
}

.stat-card.success .stat-icon {
  background: rgba(82, 196, 26, 0.1);
}

.stat-card.warning .stat-icon {
  background: rgba(250, 140, 22, 0.1);
}

.stat-content {
  position: relative;
  text-align: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  line-height: 1;
}

.stat-badge {
  position: absolute;
  top: -8px;
  right: 0;
  background: #ff4d4f;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}





/* 搜索区域样式 */
.search-section {
  margin: 10px 0;
  transition: all 0.3s ease;
  background: #f8f9fa;
  z-index: 10;
}

.search-section.fixed {
  position: fixed;
  top: 44px;
  left: 0;
  right: 0;
  padding: 16px;
  margin: 0;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.search-bar {
  display: flex;
  gap: 8px;
}

.search-bar input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  font-size: 14px;
  background: #fff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

.search-bar input:focus {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.search-bar button {
  padding: 0 20px;
  background: linear-gradient(135deg, #409EFF, #3a8ee6);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  height: 44px;
  min-width: 60px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.search-bar button:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.2);
}

.filter-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  padding: 8px 16px;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 20px;
  font-size: 13px;
  color: #666;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

.tag.active {
  background: linear-gradient(135deg, #409EFF, #3a8ee6);
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

/* 职位卡片样式 */
.job-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.job-card {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 16px;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-right: 10px;
  padding-left: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.job-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #409EFF, #3a8ee6);
  border-radius: 2px;
}

.job-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.job-header {
  margin-bottom: 10px;
}

.job-header h4 {
  font-size: 18px;
  color: #1a1a1a;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(135deg, #1a1a1a, #333);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.job-info {
  display: flex;
  gap: 10px;
  color: #666;
  font-size: 14px;
  margin-bottom: 16px;
}

.job-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.job-tags .tag {
  background: linear-gradient(135deg, #f0f2f5 0%, #e6e8eb 100%);
  color: #666;
  font-size: 12px;
  padding: 6px 12px;
  box-shadow: none;
}

.job-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #999;
  font-size: 12px;
  position: relative;
}

.views {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: auto;
  position: relative;
  padding-right: 8px;
}

.views::before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 12px;
  background: linear-gradient(to bottom, transparent, #e8e8e8, transparent);
}

.views i {
  font-size: 14px;
  color: #999;
}

.applications {
  display: flex;
  align-items: center;
  gap: 4px;
}

.applications i {
  font-size: 14px;
  color: #999;
}

.publish-time {
  color: #999;
}

/* 底部简历浮动框样式 */
.resume-float {
  position: fixed;
  left: 50%;
  bottom: 20px;
  transform: translateX(-50%);
  background: #409EFF;
  padding: 12px 32px;
  border-radius: 24px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  transition: all 0.3s ease;
}

.resume-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resume-icon {
  font-size: 20px;
  color: #fff;
}

.resume-text {
  font-size: 14px;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.resume-status {
  font-size: 12px;
  opacity: 0.9;
}

.resume-float:active {
  transform: translateX(-50%) scale(0.95);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 消息入口样式 */
.message-entry {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.message-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: #ff4d4f;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(255, 77, 79, 0.4);
}

/* 加载状态样式 */
.loading-more, .no-more {
  text-align: center;
  padding: 16px 0;
  color: #999;
  font-size: 14px;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 空状态样式 */
.no-data {
  text-align: center;
  padding: 40px 0;
  color: #999;
  font-size: 14px;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 16px;
  margin-top: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

/* 占位元素样式 */
.search-placeholder {
  height: 100px;
  margin-bottom: 16px;
}

/* 招聘单位区域 */
.companies-section {
  margin: 0 16px 20px 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.section-more {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #007AFF;
}

.companies-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.company-card {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.company-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.company-avatar {
  width: 40px;
  height: 40px;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.company-info {
  flex: 1;
  min-width: 0;
}

.company-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.company-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.company-footer {
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.job-count {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #52c41a;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-text {
  margin: 12px 0 0 0;
  font-size: 14px;
}

/* 招聘单位区域标题样式 */
.company-section {
  margin-top: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 0 16px;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  background: linear-gradient(90deg, #409EFF 0%, #36CFC9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  padding-left: 12px;
}

.title-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: linear-gradient(180deg, #409EFF 0%, #36CFC9 100%);
  border-radius: 2px;
}

.title-count {
  font-size: 14px;
  color: #409EFF;
  padding: 4px 12px;
}

.title-count:active {
  transform: scale(0.98);
  background: #b3e0ff;
  color: #fff;
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.2);
}

.title-count .yzb {
  font-size: 14px;
  display: inline-block;
  margin-left: 2px;
  transition: all 0.3s ease;
}

.title-count:active .yzb {
  color: #fff;
  transform: translateX(2px);
}

.arrow-icon {
  font-size: 14px;
  font-weight: bold;
  display: inline-block;
  margin-left: 2px;
  transform: scaleY(1.2);
  transition: all 0.3s ease;
}

.title-count:active .arrow-icon {
  color: #fff;
  transform: scaleY(1.2) translateX(2px);
}
</style>
