package com.ruoyi.web.controller.talent;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Job;
import com.ruoyi.system.domain.JobView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.system.service.IJobService;
import com.ruoyi.system.service.impl.JobServiceImpl;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.IpUtils;
import com.ruoyi.system.service.IResumeService;
import com.ruoyi.system.service.ICompanyService;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.system.domain.dto.JobImportDTO;
import com.ruoyi.common.core.page.TableDataInfo;

@RestController
@RequestMapping("/talent/job")
public class JobController extends BaseController {
    @Autowired
    private IJobService jobService;

    @Autowired
    private IResumeService resumeService;
    
    @Autowired
    private ICompanyService companyService;

    /**
     * 管理端获取职位列表
     */
    @PreAuthorize("@ss.hasPermi('talent:job:list')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam(required = false) String keyword,
                        @RequestParam(required = false) String company,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(defaultValue = "1") Integer pageNum,
                            @RequestParam(defaultValue = "10") Integer pageSize) {
        Job job = new Job();
        if (keyword != null && !keyword.isEmpty()) {
            job.setKeyword(keyword);
        }
        if (company != null && !company.isEmpty()) {
            job.setCompany(company);
        }
        if (companyId != null) {
            job.setCompanyId(companyId);
        }
        startPage();
        List<Job> list = jobService.selectJobList(job);
        return getDataTable(list);
    }

    /**
     * 用户端获取职位列表（公开接口）
     */
    @GetMapping("/publicList")
    public TableDataInfo publicList(@RequestParam(required = false) String keyword,
                                  @RequestParam(required = false) Long companyId,
                                  @RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        Job job = new Job();
        if (keyword != null && !keyword.isEmpty()) {
            job.setKeyword(keyword);
        }
        if (companyId != null) {
            job.setCompanyId(companyId);
        }
        // 只查询状态为正常的职位
        job.setStatus("0");
        startPage();
        List<Job> list = jobService.selectPublicJobList(job);
        return getDataTable(list);
    }

    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id, HttpServletRequest request) {
        // 创建JobView对象记录浏览信息
        JobView jobView = new JobView();
        jobView.setJobId(id);

        // 获取当前用户ID
        try {
            Long userId = SecurityUtils.getUserId();
            jobView.setUserId(userId);
        } catch (Exception e) {
            // 未登录用户，不设置userId
        }

        // 获取IP地址
        String ipAddress = IpUtils.getIpAddr(request);
        jobView.setIpAddress(ipAddress);

        // 获取User-Agent
        String userAgent = request.getHeader("User-Agent");
        jobView.setUserAgent(userAgent);

        // 保存浏览记录并增加浏览量
        jobService.recordJobView(jobView);
        
        return AjaxResult.success(jobService.selectJobById(id));
    }

    @PreAuthorize("@ss.hasPermi('talent:job:add')")
    @Log(title = "添加职位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Job job) {
        // 根据companyId获取公司名称，并设置到job对象中
        if (job.getCompanyId() != null) {
            com.ruoyi.system.domain.Company company = companyService.selectCompanyById(job.getCompanyId());
            if (company != null) {
                job.setCompany(company.getName());
            }
        }
        
        // 设置默认值
        if (job.getViews() == null) {
            job.setViews(0);
        }
        if (job.getApplications() == null) {
            job.setApplications(0);
        }
        
        job.setCreateBy(getUsername());
        return toAjax(jobService.insertJob(job));
    }

    @PreAuthorize("@ss.hasPermi('talent:job:edit')")
    @Log(title = "修改职位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Job job) {
        // 根据companyId获取公司名称，并设置到job对象中
        if (job.getCompanyId() != null) {
            com.ruoyi.system.domain.Company company = companyService.selectCompanyById(job.getCompanyId());
            if (company != null) {
                job.setCompany(company.getName());
            }
        }
        
        job.setUpdateBy(getUsername());
        return toAjax(jobService.updateJob(job));
    }

    @PreAuthorize("@ss.hasPermi('talent:job:remove')")
    @Log(title = "删除职位", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(jobService.deleteJobByIds(ids));
    }

    @GetMapping("/dashboard")
    public AjaxResult getDashboard() {
        Map<String, Object> data = new HashMap<>();
        data.put("totalJobs", jobService.countTotalJobs());
        data.put("todayNewJobs", jobService.countTodayNewJobs());
        data.put("totalDeliveries", resumeService.getTotalDeliveryCount());
        data.put("totalCompanies", companyService.countTotalCompanies());
        data.put("todayApplications", resumeService.getTodayDeliveryCount());
        data.put("totalViews", jobService.countTotalViews());
        return AjaxResult.success(data);
    }

    @PostMapping("/view/{id}")
    public AjaxResult view(@PathVariable("id") Long id, HttpServletRequest request) {
        JobView jobView = new JobView();
        jobView.setJobId(id);

        // 获取当前用户ID
        try {
            Long userId = SecurityUtils.getUserId();
            jobView.setUserId(userId);
        } catch (Exception e) {
            // 未登录用户，不设置userId
        }

        // 获取IP地址
        String ipAddress = IpUtils.getIpAddr(request);
        jobView.setIpAddress(ipAddress);

        // 获取User-Agent
        String userAgent = request.getHeader("User-Agent");
        jobView.setUserAgent(userAgent);

        // 保存浏览记录并增加浏览量
        jobService.recordJobView(jobView);

        return AjaxResult.success();
    }

    @Log(title = "职位管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('talent:job:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<JobImportDTO> util = new ExcelUtil<>(JobImportDTO.class);
        List<JobImportDTO> jobList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = jobService.importJobs(jobList, updateSupport, operName);
        return success(message);
    }

    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<JobImportDTO> util = new ExcelUtil<>(JobImportDTO.class);
        util.importTemplateExcel(response, "职位数据");
    }

    @PreAuthorize("@ss.hasPermi('talent:job:export')")
    @Log(title = "职位管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Job job) throws IOException {
        List<Job> list = jobService.selectJobList(job);
        ExcelUtil<Job> util = new ExcelUtil<>(Job.class);
        util.exportExcel(response, list, "职位数据");
    }

    /**
     * 查询职位投递情况
     */
    @GetMapping("/delivery/list")
    public TableDataInfo listDelivery(@RequestParam Long jobId) {
        startPage();
        List<Map<String, Object>> list = jobService.selectDeliveryList(jobId);
        return getDataTable(list);
    }

    /**
     * 获取职位总数
     */
    @GetMapping("/count-total")
    public AjaxResult countTotal() {
        int count = jobService.countTotalJobs();
        return success(count);
    }
}
