<template>
  <view class="message-page">
    <!-- 顶部网格导航 -->
    <view class="top">
      <view class="bg"></view>
      <view class="v-grid">
        <view class="grid-container">
          <view 
            v-for="(tab, index) in tabs" 
            :key="index" 
            class="grid-item-box"
            @click="switchTab(tab.type)"
          >
            <text class="yzb" :class="getTabIcon(tab.type)" :style="{ color: getTabColor(tab.type) }"></text>
            <text class="text">{{ tab.name }}</text>
            <view v-if="tab.unread > 0" class="grid-dot">
              <uni-badge :text="tab.unread" :type="getBadgeType(tab.type)" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="message-list">
      <view v-if="Object.keys(groupedMessages).length === 0" class="empty-message">
        暂无消息
      </view>
      
      <template v-else>
        <!-- 消息分组：今天 -->
        <view 
          v-for="(group, date) in groupedMessages" 
          :key="date" 
          class="message-group"
        >
          <view class="date-divider">{{ formatDate(date) }}</view>
          <view 
            v-for="msg in group" 
            :key="msg.id" 
            class="message-item"
            :class="{ unread: !msg.isRead }"
            @click="showMessageDetail(msg)"
          >
            <view class="message-icon">
              <uni-icons :type="getMessageIcon(msg.type)" size="24" :color="getMessageColor(msg.type)"></uni-icons>
            </view>
            <view class="message-content">
              <view class="message-title">{{ msg.title }}</view>
              <view class="message-desc">{{ msg.content }}</view>
              <view class="message-time">{{ formatTime(msg.createTime) }}</view>
            </view>
            <view v-if="!msg.isRead" class="unread-dot"></view>
          </view>
        </view>
      </template>

      <!-- 加载状态 -->
      <uni-load-more :status="loadMoreStatus" />
    </view>

    <!-- 消息详情弹窗 -->
    <uni-popup ref="messagePopup" type="center">
      <view class="message-detail-popup">
        <view class="popup-header">
          <text class="popup-title">{{ currentMessage?.title }}</text>
          <text class="popup-time">{{ formatTime(currentMessage?.createTime) }}</text>
        </view>
        <view class="popup-content">
          {{ currentMessage?.content }}
        </view>
        <view class="popup-footer">
          <button class="popup-btn" @click="closeMessageDetail">关闭</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { getMessageList, readMessage as markAsRead } from '@/utils/talent-market'
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'
import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import authStore from '@/stores/auth.js'
// 消息类型标签
const tabs = ref([
  
  { type: 'SYSTEM', name: '系统通知', unread: 0 },
  { type: 'RESUME', name: '我的投递', unread: 0 },
  { type: 'interview', name: '谁看过我', unread: 0 }
])

const currentTab = ref('all')
const messages = ref([])
const page = ref(1)
const loadMoreStatus = ref('more')
const isLoading = ref(false)

// 当前查看的消息
const currentMessage = ref(null)
const messagePopup = ref(null)

// 按日期分组消息
const groupedMessages = computed(() => {
  console.log('开始计算 groupedMessages, messages:', messages.value)
  const groups = {}
  if (!Array.isArray(messages.value)) {
    console.warn('messages.value 不是数组:', messages.value)
    return {}
  }
  
  messages.value.forEach(msg => {
    try {
      const date = new Date(msg.createTime).toLocaleDateString()
      if (!groups[date]) {
        groups[date] = []
      }
      groups[date].push(msg)
    } catch (error) {
      console.error('处理消息时出错:', msg, error)
    }
  })
  
  console.log('分组后的消息:', groups)
  return groups
})

// 格式化日期
const formatDate = (date) => {
  const today = new Date().toLocaleDateString()
  const yesterday = new Date(Date.now() - 86400000).toLocaleDateString()
  
  if (date === today) return '今天'
  if (date === yesterday) return '昨天'
  return date
}

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 切换消息类型
const switchTab = (type) => {
  currentTab.value = type
  page.value = 1
  messages.value = []
  loadMoreStatus.value = 'more'
  fetchMessages()
}

// 获取消息图标
const getMessageIcon = (type) => {
  console.log('消息类型:', type) // 调试用

  const icons = {
    system: 'notification-filled',    // 系统通知使用铃铛图标
    interview: 'calendar-filled',     // 面试通知使用日历图标
    resume: 'bars',                  // 简历状态使用列表图标
    application: 'paperplane',        // 投递状态使用发送图标
    SYSTEM: 'notification-filled',
    INTERVIEW: 'calendar-filled',
    RESUME: 'bars',
    APPLICATION: 'paperplane',
    pending: 'paperplane',           // 待处理使用发送图标
    reviewing: 'eye',                // 审核中使用眼睛图标
    interviewing: 'calendar-filled', // 面试中使用日历图标
    accepted: 'checkmarkempty'       // 已录用使用对勾图标
  }
  
  const iconType = icons[type] || 'chat'  // 默认使用聊天图标
  console.log('使用图标:', iconType, '消息完整信息:', type)
  return iconType
}

// 获取消息列表
const fetchMessages = async () => {
  // 如果正在加载中，直接返回
  if (isLoading.value) {
    return
  }
  
  try {
    isLoading.value = true
     // 使用认证状态管理初始化
    const token = await authStore.initialize();
    const result = await getMessageList(token, {
      type: currentTab.value === 'all' ? undefined : currentTab.value,
      page: page.value,
      pageSize: 10
    })

    console.log('API返回数据:', result)

    // 从 result.data 中获取实际的响应数据
    const response = result
    
    if (response && response.code === 200 && Array.isArray(response.data)) {
      const messageList = response.data.map(msg => {
        console.log('处理单条消息:', msg)
        return {
          id: msg.id,
          title: msg.title || '',
          content: msg.content || '',
          createTime: msg.createTime || new Date().toISOString(),
          type: (msg.type || 'SYSTEM').toLowerCase(),
          isRead: msg.status !== 'UNREAD',
          relatedId: msg.relatedId
        }
      })

      console.log('处理后的消息列表:', messageList)
      
      // 如果是第一页，直接替换消息列表
      if (page.value === 1) {
        messages.value = messageList
      } else {
        // 如果是加载更多，检查是否有重复消息
        const existingIds = new Set(messages.value.map(msg => msg.id))
        const newMessages = messageList.filter(msg => !existingIds.has(msg.id))
        messages.value = [...messages.value, ...newMessages]
      }
      
      // 更新加载状态
      loadMoreStatus.value = messageList.length < 20 ? 'noMore' : 'more'
      
      // 获取所有未读消息数
      const allUnreadResult = await getMessageList(token, {
        page: 1,
        pageSize: 1000 // 设置一个较大的值以获取所有消息
      })
      
      if (allUnreadResult && allUnreadResult.code === 200 && Array.isArray(allUnreadResult.data)) {
        const allMessages = allUnreadResult.data
        // 计算每种类型的未读消息数
        const unreadCounts = {
          all: allMessages.filter(msg => msg.status === 'UNREAD').length,
          system: allMessages.filter(msg => msg.type === 'SYSTEM' && msg.status === 'UNREAD').length,
          interview: allMessages.filter(msg => msg.type === 'INTERVIEW' && msg.status === 'UNREAD').length,
          resume: allMessages.filter(msg => msg.type === 'RESUME' && msg.status === 'UNREAD').length
        }
        
        // 更新标签未读数
        tabs.value = tabs.value.map(tab => {
          tab.unread = unreadCounts[tab.type] || 0
          return tab
        })

        // 更新 tabbar 红点
        const totalUnread = unreadCounts.all
        if (totalUnread > 0) {
          uni.setTabBarBadge({
            index: 1, // 消息页面的索引
            text: totalUnread > 99 ? '99+' : totalUnread.toString()
          })
        } else {
          uni.removeTabBarBadge({
            index: 1
          })
        }
      }
    } else {
      console.warn('API 返回数据格式不正确:', response)
      loadMoreStatus.value = 'noMore'
    }
  } catch (error) {
    console.error('获取消息列表失败:', error)
    uni.showToast({
      title: '获取消息失败',
      icon: 'none'
    })
    loadMoreStatus.value = 'noMore'
  } finally {
    isLoading.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (loadMoreStatus.value === 'more' && !isLoading.value) {
    page.value++
    fetchMessages()
  }
}

// 下拉刷新
const onRefresh = async () => {
  try {
    page.value = 1
    loadMoreStatus.value = 'more'
    await fetchMessages()
    
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('刷新失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  } finally {
    uni.stopPullDownRefresh()
  }
}

// 阅读消息
const readMessage = async (msg) => {
  if (!msg.isRead) {
    try {
      const token = uni.getStorageSync('token')
      const result = await markAsRead(token, msg.id)
      if (result && result.code === 200) {
        // 更新本地消息状态
        msg.isRead = true
        msg.status = 'READ'
        
        // 重新获取所有未读消息数
        const allUnreadResult = await getMessageList(token, {
          page: 1,
          pageSize: 1000
        })
        
        if (allUnreadResult && allUnreadResult.code === 200 && Array.isArray(allUnreadResult.data)) {
          const allMessages = allUnreadResult.data
          const unreadCounts = {
            all: allMessages.filter(msg => msg.status === 'UNREAD').length,
            system: allMessages.filter(msg => msg.type === 'SYSTEM' && msg.status === 'UNREAD').length,
            interview: allMessages.filter(msg => msg.type === 'INTERVIEW' && msg.status === 'UNREAD').length,
            resume: allMessages.filter(msg => msg.type === 'RESUME' && msg.status === 'UNREAD').length
          }
          
          // 更新标签未读数
          tabs.value = tabs.value.map(tab => {
            tab.unread = unreadCounts[tab.type] || 0
            return tab
          })

          // 更新 tabbar 红点
          const totalUnread = unreadCounts.all
          if (totalUnread > 0) {
            uni.setTabBarBadge({
              index: 1,
              text: totalUnread > 99 ? '99+' : totalUnread.toString()
            })
          } else {
            uni.removeTabBarBadge({
              index: 1
            })
          }
        }
      } else {
        console.error('标记已读失败:', result)
        uni.showToast({
          title: '标记已读失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('标记已读失败:', error)
      uni.showToast({
        title: '标记已读失败',
        icon: 'none'
      })
    }
  }
  
  // 处理消息点击事件，根据消息类型跳转到相应页面
  if (msg.type === 'interview') {
    uni.navigateTo({
      url: `/pages/talent-market/interview-detail?id=${msg.relatedId}`
    })
  } else if (msg.type === 'resume') {
    uni.navigateTo({
      url: `/pages/talent-market/resume?id=${msg.relatedId}`
    })
  }
}

// 显示消息详情
const showMessageDetail = async (msg) => {
  currentMessage.value = msg
  messagePopup.value.open()
  
  // 如果消息未读，标记为已读
  if (!msg.isRead) {
    await readMessage(msg)
  }
}

// 关闭消息详情
const closeMessageDetail = () => {
  messagePopup.value.close()
}

// 使用生命周期函数
onPullDownRefresh(() => {
  onRefresh()
})

// 添加上拉加载更多事件监听
onReachBottom(() => {
  loadMore()
})

onMounted(() => {
  console.log('组件挂载，开始获取消息')
  fetchMessages()
})

// 获取标签图标
const getTabIcon = (type) => {
  const icons = {
    // all: 'yzb-ic_xiaoxi_xitongxiaoxi',
    system: 'yzb-ic_xiaoxi_xitongxiaoxi',
    interview: 'yzb-fasong',
    resume: 'yzb-kanguowode'
  }
  return icons[type] || 'yzb-ic_xiaoxi_xitongxiaoxi'
}

// 获取标签颜色
const getTabColor = (type) => {
  const colors = {
    all: '#bb8dbb',
    system: '#bb8dbb',
    interview: '#6ab493',
    resume: '#cac87e'
  }
  return colors[type] || '#bb8dbb'
}

// 获取徽章类型
const getBadgeType = (type) => {
  const types = {
    all: 'warning',
    system: 'warning',
    interview: 'success',
    resume: 'error'
  }
  return types[type] || 'warning'
}

// 获取消息颜色
const getMessageColor = (type) => {
  const colors = {
    system: '#bb8dbb',
    interview: '#6ab493',
    resume: '#cac87e'
  }
  return colors[type] || '#bb8dbb'
}
</script>

<style lang="scss">
.message-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120px;
  display: flex;
  flex-direction: column;
}

.top {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .bg {
    background-color: #007AFF;
    height: 120upx;
    width: 100%;
  }
  
  .v-grid {
    padding: 0 0 15upx 0;
    margin-top: -100upx;
    background-color: #fff;
    width: 90%;
    border-radius: 24upx;
    box-shadow: 0 0 20upx rgba(0, 0, 0, 0.15);
    margin-bottom: 30upx;
  }

  .grid-container {
    display: flex;
    justify-content: space-around;
    padding: 20upx 0;
  }

  .grid-item-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10upx 0;
    position: relative;
    flex: 1;
  }

  .grid-dot {
    position: absolute;
    top: 5px;
    right: 15px;
  }

  .yzb {
    font-size: 85upx;
  }

  .text {
    font-size: 14px;
    color: #333;
    margin-top: 8px;
  }
}

.message-list {
  flex: 1;
  padding: 0 16px;
  margin-top: 20px;
}

.message-group {
  margin-bottom: 20px;
}

.date-divider {
  padding: 8px 16px;
  color: #8e8e93;
  font-size: 13px;
  background: #f8f9fa;
  font-weight: 500;
  border-radius: 8px;
  margin-bottom: 8px;
}

.message-item {
  display: flex;
  padding: 16px;
  background: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.message-item:active {
  background: #f8f9fa;
}

.message-item.unread {
  background: #f0f9ff;
}

.message-icon {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background: #e6f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-title {
  font-size: 16px;
  color: #1a1a1a;
  margin-bottom: 6px;
  font-weight: 500;
}

.message-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 6px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.unread-dot {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff3b30;
  box-shadow: 0 0 0 2px rgba(255, 59, 48, 0.2);
}

.empty-message {
  text-align: center;
  color: #8e8e93;
  padding: 60px 0;
  font-size: 14px;
  background: #fff;
  margin: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 消息详情弹窗样式 */
.message-detail-popup {
  width: 80vw;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.popup-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 18px;
  font-weight: 500;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8px;
}

.popup-time {
  font-size: 13px;
  color: #999;
}

.popup-content {
  padding: 20px;
  font-size: 15px;
  color: #333;
  line-height: 1.6;
  max-height: 50vh;
  overflow-y: auto;
}

.popup-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

.popup-btn {
  background: #007AFF;
  color: #fff;
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 15px;
}
</style>