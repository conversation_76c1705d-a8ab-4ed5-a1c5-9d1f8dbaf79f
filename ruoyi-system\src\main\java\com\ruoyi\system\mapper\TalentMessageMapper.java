package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.TalentMessage;
import java.util.List;

public interface TalentMessageMapper {
    List<TalentMessage> selectMessageList(TalentMessage message);
    
    int selectUnreadCount(Long userId);
    
    TalentMessage selectMessageById(Long id);
    
    int insertMessage(TalentMessage message);
    
    int updateMessage(TalentMessage message);
    
    int updateMessageRead(String id);
    
    int updateAllMessageRead(Long userId);
} 