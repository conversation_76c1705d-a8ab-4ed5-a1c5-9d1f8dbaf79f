<template>
  <div class="dashboard">
    <header>
      <h3>更新于2024年7月</h3>
    </header>
    
    <section class="overview">
      <div class="card">
        <h3>总投资额</h3>
        <p class="highlight">{{ totalInvestment }}亿</p>
      </div>
      <div class="card">
        <h3>项目数量</h3>
        <p class="highlight">{{ projectCount }}个</p>
      </div>
      <div class="card">
        <h3>完成率</h3>
        <p class="highlight">{{ completionRate }}%</p>
      </div>
    </section>

    <section class="charts">
      <div class="chart">
        <h3>投资分布</h3>
        <div ref="investmentDistributionChart" class="chart-container"></div>
      </div>
      <div class="chart">
        <h3>月度投资趋势</h3>
        <div ref="monthlyInvestmentTrendChart" class="chart-container"></div>
      </div>
    </section>

    <section class="project-list">
      <h3>重点项目</h3>
      <div class="project-grid">
        <div v-for="project in keyProjects" :key="project.id" class="project-card">
          <h4>{{ project.name }}</h4>
          <p>投资额：<span class="highlight">{{ project.investment }}亿</span></p>
          <p>进度：<span class="progress-bar" :style="{ width: project.progress + '%' }">{{ project.progress }}%</span></p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const totalInvestment = ref(0)
const projectCount = ref(0)
const completionRate = ref(0)
const keyProjects = ref([])
const investmentDistributionChart = ref(null)
const monthlyInvestmentTrendChart = ref(null)

onMounted(() => {
  fetchDashboardData()
  renderCharts()
})

const fetchDashboardData = async () => {
  // 模拟API调用
  totalInvestment.value = 100
  projectCount.value = 50
  completionRate.value = 75
  keyProjects.value = [
    { id: 1, name: '城市供水工程', investment: 20, progress: 80 },
    { id: 2, name: '污水处理厂扩建', investment: 15, progress: 60 },
    { id: 3, name: '水库建设项目', investment: 30, progress: 40 },
    { id: 4, name: '管网改造工程', investment: 10, progress: 90 }
  ]
}

const renderCharts = () => {
  const investmentDistributionInstance = echarts.init(investmentDistributionChart.value)
  const monthlyInvestmentTrendInstance = echarts.init(monthlyInvestmentTrendChart.value)

  const investmentDistributionOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}亿元 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '投资分布',
        type: 'pie',
        radius: '70%',
        data: [
          { value: 20, name: '城市供水工程' },
          { value: 15, name: '污水处理厂扩建' },
          { value: 30, name: '水库建设项目' },
          { value: 35, name: '其他' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  }

  const monthlyInvestmentTrendOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLabel: {
        fontSize: 8,
        interval: 1,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '投资额（亿元）',
      nameTextStyle: {
        fontSize: 8
      },
      axisLabel: {
        fontSize: 8
      }
    },
    grid: {
      left: '10%',
      right: '5%',
      bottom: '15%',
      top: '10%'
    },
    series: [
      {
        data: [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60],
        type: 'line',
        smooth: true,
        areaStyle: {}
      }
    ]
  }

  investmentDistributionInstance.setOption(investmentDistributionOption)
  monthlyInvestmentTrendInstance.setOption(monthlyInvestmentTrendOption)

  window.addEventListener('resize', () => {
    investmentDistributionInstance.resize()
    monthlyInvestmentTrendInstance.resize()
  })
}
</script>

<style scoped>
.dashboard {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  padding: 0.5rem;
  background-color: #f5f7fa;
  color: #333;
}

header {
  text-align: left;
  padding-top: 0.5rem;
  margin-bottom: 0.5rem; 
  color: blue;
}

h1 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.overview {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.card {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  flex: 1;
  margin: 0 0.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.card:first-child {
  margin-left: 0;
}

.card:last-child {
  margin-right: 0;
}

.highlight {
  color: #3498db;
  font-size: 1rem;
  font-weight: bold;
}

.charts {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.chart {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  margin-bottom: 1rem;
  height: 250px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  height: 100%;
}

.project-list h3 {
  margin-bottom: 0.5rem;
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

.project-card {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.project-card h4 {
  color: #2c3e50;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.progress-bar {
  display: inline-block;
  height: 14px;
  background-color: #2ecc71;
  color: white;
  text-align: right;
  padding-right: 3px;
  border-radius: 7px;
  font-size: 0.7rem;
  line-height: 14px;
}

h3 {
  font-size: 1rem;
  margin-bottom: 0.3rem;
}

p {
  font-size: 0.8rem;
  margin-bottom: 0.2rem;
}

@media (max-width: 480px) {
  .card {
    padding: 0.3rem;
  }

  .card h3 {
    font-size: 0.8rem;
  }

  .highlight {
    font-size: 0.9rem;
  }

  .chart {
    height: 200px;
  }

  .project-grid {
    grid-template-columns: 1fr;
  }

  h1 {
    font-size: 1.3rem;
  }

  h3 {
    font-size: 0.9rem;
  }

  p {
    font-size: 0.7rem;
  }
}
</style>