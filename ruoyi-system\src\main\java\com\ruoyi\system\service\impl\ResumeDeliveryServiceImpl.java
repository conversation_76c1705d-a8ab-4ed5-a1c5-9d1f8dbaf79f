package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.ResumeDelivery;
import com.ruoyi.system.mapper.ResumeDeliveryMapper;
import com.ruoyi.system.service.IResumeDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ResumeDeliveryServiceImpl implements IResumeDeliveryService {
    
    @Autowired
    private ResumeDeliveryMapper deliveryMapper;

    @Override
    public List<ResumeDelivery> selectDeliveryList(ResumeDelivery delivery) {
        return deliveryMapper.selectDeliveryList(delivery);
    }

    @Override
    public ResumeDelivery selectDeliveryById(String id) {
        return deliveryMapper.selectDeliveryById(id);
    }

    @Override
    public int updateDeliveryStatus(ResumeDelivery delivery) {
        return deliveryMapper.updateDelivery(delivery);
    }
}
