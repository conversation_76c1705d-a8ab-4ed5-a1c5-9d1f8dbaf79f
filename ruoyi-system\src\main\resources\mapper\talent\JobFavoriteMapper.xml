<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talent.mapper.JobFavoriteMapper">
    
    <resultMap type="JobFavorite" id="JobFavoriteResult">
        <id     property="id"           column="id"            />
        <result property="userId"       column="user_id"       />
        <result property="jobId"        column="job_id"        />
        <result property="jobTitle"     column="job_title"     />
        <result property="companyName"  column="company_name"  />
        <result property="experience"   column="experience"    />
        <result property="education"    column="education"     />
        <result property="jobType"      column="job_type"      />
        <result property="createTime"   column="create_time"   />
        <result property="updateTime"   column="update_time"   />
    </resultMap>

    <sql id="selectJobFavoriteVo">
        select f.id, f.user_id, f.job_id, j.title as job_title, 
        c.name as company_name, j.experience, j.education, 
        COALESCE(j.demand_type, '全职') as job_type,
        f.create_time, f.update_time
        from job_favorite f
        left join talent_job j on f.job_id = j.id
        left join company c on j.company_id = c.id
    </sql>

    <select id="selectJobFavoriteList" parameterType="JobFavorite" resultMap="JobFavoriteResult">
        <include refid="selectJobFavoriteVo"/>
        <where>
            <if test="userId != null">
                AND f.user_id = #{userId}
            </if>
            <if test="jobId != null">
                AND f.job_id = #{jobId}
            </if>
        </where>
        order by f.create_time desc
    </select>

    <select id="checkIsFavorite" resultMap="JobFavoriteResult">
        select f.id, f.user_id, f.job_id
        from job_favorite f
        where f.user_id = #{userId} and f.job_id = #{jobId}
        limit 1
    </select>

    <insert id="insertJobFavorite" parameterType="JobFavorite">
        insert into job_favorite (
            user_id,
            job_id,
            create_time,
            update_time
        ) values (
            #{userId},
            #{jobId},
            sysdate(),
            sysdate()
        )
    </insert>

    <delete id="deleteJobFavorite" parameterType="Long">
        delete from job_favorite where id = #{id}
    </delete>

    <delete id="deleteJobFavoriteByIds" parameterType="Long">
        delete from job_favorite where id in 
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 