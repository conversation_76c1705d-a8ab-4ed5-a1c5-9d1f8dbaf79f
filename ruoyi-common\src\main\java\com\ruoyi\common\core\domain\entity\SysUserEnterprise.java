package com.ruoyi.common.core.domain.entity;
//
//import org.apache.commons.lang3.builder.ToStringBuilder;
//import org.apache.commons.lang3.builder.ToStringStyle;
//
//import com.ruoyi.common.core.domain.BaseEntity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 用户企业微信对象 sys_user_enterprise
 *
 */
@Data
public class SysUserEnterprise extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long userId;

    /** 企业ID */
    private Long enterpriseId;
    /** 企业微信用户ID */
    @Excel(name = "企业微信用户ID")
    private String enterpriseUserId;
    /** 企业微信企业ID */
    @Excel(name = "企业微信企业ID")
    private String enterpriseCorpId;
    /** 企业微信企业名称 */
    @Excel(name = "企业微信企业名称")
    private String enterpriseName;

    /** 企业微信用户昵称 */
    @Excel(name = "企业微信用户昵称")
    private String enterpriseNickname;

    /** 企业微信部门ID */
    @Excel(name = "企业微信部门ID")
    private Long enterpriseDeptId;

    /** 企业微信部门名称 */
    @Excel(name = "企业微信部门名称")
    private String enterpriseDeptName;

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setEnterpriseId(Long enterpriseId)
    {
        this.enterpriseId = enterpriseId;
    }

    public Long getEnterpriseId()
    {
        return enterpriseId;
    }
    public void setEnterpriseNickname(String enterpriseNickname)
    {
        this.enterpriseNickname = enterpriseNickname;
    }

    public String getEnterpriseNickname()
    {
        return enterpriseNickname;
    }
    public void setEnterpriseDeptId(Long enterpriseDeptId)
    {
        this.enterpriseDeptId = enterpriseDeptId;
    }

    public Long getEnterpriseDeptId()
    {
        return enterpriseDeptId;
    }
    public void setEnterpriseDeptName(String enterpriseDeptName)
    {
        this.enterpriseDeptName = enterpriseDeptName;
    }

    public String getEnterpriseDeptName()
    {
        return enterpriseDeptName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("userId", getUserId())
                .append("enterpriseId", getEnterpriseId())
                .append("enterpriseUserId", getEnterpriseUserId())
                .append("enterpriseNickname", getEnterpriseNickname())
                .append("enterpriseDeptId", getEnterpriseDeptId())
                .append("enterpriseDeptName", getEnterpriseDeptName())
                .toString();
    }
}
