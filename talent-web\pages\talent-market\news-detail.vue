<template>
  <view class="news-detail">
    <view class="header">
      <text class="back-btn" @click="goBack">〈</text>
      <text class="title">图文详情</text>
    </view>
    <view class="news-container">
      <text class="news-title">{{ news.title }}</text>
      <text class="news-date">{{ formatDate(news.createTime) }}</text>
      
      <view class="cover-image" v-if="news.imageUrl">
        <image :src="news.imageUrl" mode="widthFix"></image>
      </view>
      
      <!-- 用于展示富文本内容 -->
      <rich-text class="news-content" :nodes="news.content"></rich-text>
    </view>
  </view>
</template>

<script>
import { ref, getCurrentInstance, onMounted } from 'vue'

export default {
  setup() {
    const {
      proxy,
      appContext: {
        config: {
          globalProperties: global
        }
      }
    } = getCurrentInstance();
    
    // 响应式数据
    const news = ref({
      id: null,
      title: '',
      imageUrl: '',
      content: '',
      createTime: null
    })
    
    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
    
    // 返回上一页
    const goBack = () => {
      uni.navigateBack();
    }
    
    // 获取新闻详情
    const fetchNewsDetail = async (id) => {
      try {
        const token = uni.getStorageSync('token')
        if (!token) return
        
        const { data } = await uni.request({
          url: global.$serverUrl + '/api/talent/banner/' + id,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token
          }
        })
        
        if (data && data.code === 200) {
          // 处理图片路径
          if (data.data.imageUrl && !data.data.imageUrl.startsWith('http')) {
            data.data.imageUrl = global.$serverUrl + data.data.imageUrl
          }
          
          news.value = data.data
        } else {
          uni.showToast({
            title: '获取内容失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取详情失败', error)
        uni.showToast({
          title: '获取内容失败',
          icon: 'none'
        })
      }
    }
    
    // 生命周期
    onMounted(() => {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const id = currentPage.$page?.options?.id
      
      if (id) {
        fetchNewsDetail(id)
      } else {
        uni.showToast({
          title: '内容不存在',
          icon: 'none'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    })
    
    return {
      news,
      formatDate,
      goBack
    }
  }
}
</script>

<style scoped>
.news-detail {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  position: relative;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background: #fff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
}

.back-btn {
  position: absolute;
  left: 30rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.news-container {
  padding: 30rpx;
  background: #fff;
  border-radius: 8rpx;
  margin: 0 20rpx;
}

.news-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}

.news-date {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 30rpx;
  display: block;
}

.cover-image {
  margin-bottom: 30rpx;
}

.cover-image image {
  width: 100%;
  border-radius: 8rpx;
}

.news-content {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
}
</style> 