<template>
  <div class="dashboard">
    <header>
      <h3>更新于2024年7月</h3>
    </header>
    
    <section class="overview">
      <div class="card">
        <h3>总收入</h3>
        <p class="highlight">{{ totalRevenue }}万</p>
      </div>
      <div class="card">
        <h3>净利润</h3>
        <p class="highlight">{{ netProfit }}万</p>
      </div>
      <div class="card">
        <h3>资产负债率</h3>
        <p class="highlight">{{ debtRatio }}%</p>
      </div>
    </section>

    <section class="charts">
      <div class="chart">
        <h3>收入构成</h3>
        <div ref="revenueCompositionChart" class="chart-container"></div>
      </div>
      <div class="chart">
        <h3>月度利润趋势</h3>
        <div ref="monthlyProfitTrendChart" class="chart-container"></div>
      </div>
    </section>

    <section class="financial-indicators">
      <h3>关键财务指标</h3>
      <div class="indicator-grid">
        <div v-for="indicator in financialIndicators" :key="indicator.name" class="indicator-card">
          <h4>{{ indicator.name }}</h4>
          <p>值：<span class="highlight">{{ indicator.value }}</span></p>
          <p>同比：<span :class="['trend', indicator.trend > 0 ? 'up' : 'down']">
            {{ indicator.trend > 0 ? '+' : '' }}{{ indicator.trend }}%
          </span></p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const totalRevenue = ref(0)
const netProfit = ref(0)
const debtRatio = ref(0)
const financialIndicators = ref([])
const revenueCompositionChart = ref(null)
const monthlyProfitTrendChart = ref(null)

onMounted(() => {
  fetchDashboardData()
  renderCharts()
})

const fetchDashboardData = async () => {
  // 模拟API调用
  totalRevenue.value = 10000
  netProfit.value = 2000
  debtRatio.value = 45
  financialIndicators.value = [
    { name: '流动比率', value: '2.5', trend: 5 },
    { name: '毛利率', value: '35%', trend: -2 },
    { name: '资产周转率', value: '1.2', trend: 3 },
    { name: '每股收益', value: '¥2.5', trend: 10 }
  ]
}

const renderCharts = () => {
  const revenueCompositionInstance = echarts.init(revenueCompositionChart.value)
  const monthlyProfitTrendInstance = echarts.init(monthlyProfitTrendChart.value)

  const revenueCompositionOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}万元 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '收入构成',
        type: 'pie',
        radius: '70%',
        data: [
          { value: 5000, name: '主营业务' },
          { value: 3000, name: '投资收益' },
          { value: 1500, name: '其他收入' },
          { value: 500, name: '政府补贴' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  }

  const monthlyProfitTrendOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLabel: {
        fontSize: 8,
        interval: 1,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '利润（万元）',
      nameTextStyle: {
        fontSize: 8
      },
      axisLabel: {
        fontSize: 8
      }
    },
    grid: {
      left: '10%',
      right: '5%',
      bottom: '15%',
      top: '10%'
    },
    series: [
      {
        data: [100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650],
        type: 'line',
        smooth: true,
        areaStyle: {}
      }
    ]
  }

  revenueCompositionInstance.setOption(revenueCompositionOption)
  monthlyProfitTrendInstance.setOption(monthlyProfitTrendOption)

  window.addEventListener('resize', () => {
    revenueCompositionInstance.resize()
    monthlyProfitTrendInstance.resize()
  })
}
</script>

<style scoped>
.dashboard {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  padding: 0.5rem;
  background-color: #f5f7fa;
  color: #333;
}

header {
  text-align: left;
  padding-top: 0.5rem;
  margin-bottom: 0.5rem; 
  color: blue;
}

h1 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.overview {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.card {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  flex: 1;
  margin: 0 0.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.card:first-child {
  margin-left: 0;
}

.card:last-child {
  margin-right: 0;
}

.highlight {
  color: #3498db;
  font-size: 1rem;
  font-weight: bold;
}

.charts {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.chart {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  margin-bottom: 1rem;
  height: 250px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  height: 100%;
}

.financial-indicators h3 {
  margin-bottom: 0.5rem;
}

.indicator-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

.indicator-card {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.indicator-card h4 {
  color: #2c3e50;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.trend {
  font-weight: bold;
}

.trend.up {
  color: #2ecc71;
}

.trend.down {
  color: #e74c3c;
}

h3 {
  font-size: 1rem;
  margin-bottom: 0.3rem;
}

p {
  font-size: 0.8rem;
  margin-bottom: 0.2rem;
}

@media (max-width: 480px) {
  .card {
    padding: 0.3rem;
  }

  .card h3 {
    font-size: 0.8rem;
  }

  .highlight {
    font-size: 0.9rem;
  }

  .chart {
    height: 200px;
  }

  .indicator-grid {
    grid-template-columns: 1fr 1fr;
  }

  h1 {
    font-size: 1.3rem;
  }

  h3 {
    font-size: 0.9rem;
  }

  p {
    font-size: 0.7rem;
  }
}
</style>