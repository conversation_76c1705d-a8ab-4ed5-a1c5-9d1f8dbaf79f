<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 标题 -->
      <div class="form-title">
        <h1>干部任免审批表</h1>
      </div>

      <!-- 基本信息区域 - 两列布局 -->
      <div class="basic-info-container">
        <!-- 左侧头像区域 -->
        <div class="avatar-container">
          <img v-if="resume.avatar" :src="resume.avatar" alt="头像" class="avatar-image">
          <div v-else class="avatar-placeholder">无头像</div>
        </div>
        
        <!-- 右侧基本信息 -->
        <div class="info-container">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名">{{ resume.name || '-' }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ resume.gender || '-' }}</el-descriptions-item>
            <el-descriptions-item label="出生年月">{{ parseTime(resume.birthday, '{y}.{m}') || '-' }}</el-descriptions-item>
            <el-descriptions-item label="年龄">{{ resume.age ? `${resume.age}岁` : '-' }}</el-descriptions-item>
            <el-descriptions-item label="民族">{{ resume.nation || '-' }}</el-descriptions-item>
            <el-descriptions-item label="籍贯">{{ resume.nativePlace || '-' }}</el-descriptions-item>
            <el-descriptions-item label="政治面貌">{{ resume.politicalStatus || '-' }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ resume.phone || '-' }}</el-descriptions-item>
            <el-descriptions-item label="电子邮箱">{{ resume.email || '-' }}</el-descriptions-item>
            <el-descriptions-item label="专业技术职务">{{ resume.technicalPosition || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 学历学位信息 -->
      <div class="section-title">学历学位</div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="学历">{{ getHighestEducation(resume.education) }}</el-descriptions-item>
        <el-descriptions-item label="学位">{{ getHighestDegree(resume.education) }}</el-descriptions-item>
        <el-descriptions-item label="毕业院校">{{ getHighestSchool(resume.education) }}</el-descriptions-item>
        <el-descriptions-item label="专业">{{ getHighestMajor(resume.education) }}</el-descriptions-item>
      </el-descriptions>

      <!-- 现任职务 -->
      <div class="section-title">现任职务</div>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="现任职务">{{ resume.currentPosition || currentPosition.currentPosition || '-' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 教育经历 -->
      <div class="section-title">教育经历</div>
      <div class="timeline-container">
        <div v-for="(edu, index) in resume.education" :key="`edu-${index}`" class="timeline-item">
          <div class="timeline-date">{{ parseTime(edu.startDate, '{y}.{m}') }}—{{ parseTime(edu.endDate, '{y}.{m}') }}</div>
          <div class="timeline-content">
            <div class="timeline-title">{{ edu.school }}</div>
            <div class="timeline-subtitle">{{ edu.major }} | {{ edu.degree }}</div>
          </div>
        </div>
        <div v-if="!resume.education || resume.education.length === 0" class="empty-data">暂无教育经历</div>
      </div>

      <!-- 工作经历 -->
      <div class="section-title">工作经历</div>
      <div class="timeline-container">
        <div v-for="(work, index) in resume.work" :key="`work-${index}`" class="timeline-item">
          <div class="timeline-date">{{ parseTime(work.startDate, '{y}.{m}') }}—{{ parseTime(work.endDate, '{y}.{m}') }}</div>
          <div class="timeline-content">
            <div class="timeline-title">{{ work.company }}</div>
            <div class="timeline-subtitle">{{ work.position }} | {{ work.department }}</div>
            <div class="timeline-description" v-if="work.description">{{ work.description }}</div>
          </div>
        </div>
        <div v-if="!resume.work || resume.work.length === 0" class="empty-data">暂无工作经历</div>
      </div>

      <!-- 项目经历 -->
      <div class="section-title">项目经历</div>
      <div class="timeline-container">
        <div v-for="(project, index) in resume.project" :key="`project-${index}`" class="timeline-item">
          <div class="timeline-date">{{ parseTime(project.startDate, '{y}.{m}') }}—{{ parseTime(project.endDate, '{y}.{m}') }}</div>
          <div class="timeline-content">
            <div class="timeline-title">{{ project.name }}</div>
            <div class="timeline-subtitle">{{ project.role }}</div>
            <div class="timeline-description" v-if="project.description">{{ project.description }}</div>
            <div class="timeline-achievement" v-if="project.achievement">
              <strong>成果：</strong>{{ project.achievement }}
            </div>
          </div>
      </div>
        <div v-if="!resume.project || resume.project.length === 0" class="empty-data">暂无项目经历</div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { getResume } from "@/api/talent/resume";

const route = useRoute();
const resume = ref({});
const loading = ref(true);

// 现任职务信息 - 从后台获取
const currentPosition = ref({
  currentPosition: '-',
  proposedPosition: '-',
  proposedRemoval: '-'
});

// 获取最高学历信息
const getHighestEducation = (education) => {
  if (!education || education.length === 0) return '-';
  const highest = education.find(edu => edu.isHighest) || education[0];
  return highest.degree || '-';
};

const getHighestDegree = (education) => {
  if (!education || education.length === 0) return '-';
  const highest = education.find(edu => edu.isHighest) || education[0];
  // 根据学历推断学位
  const degreeMap = {
    '专科': '专科',
    '本科': '学士',
    '硕士': '硕士',
    '博士': '博士'
  };
  return degreeMap[highest.degree] || '-';
};

const getHighestSchool = (education) => {
  if (!education || education.length === 0) return '-';
  const highest = education.find(edu => edu.isHighest) || education[0];
  return highest.school || '-';
};

const getHighestMajor = (education) => {
  if (!education || education.length === 0) return '-';
  const highest = education.find(edu => edu.isHighest) || education[0];
  return highest.major || '-';
};

// 获取简历详情
const getResumeData = async () => {
  try {
    const id = route.query.id;
    if (!id) {
      ElMessage.error('未找到简历ID');
      return;
    }
    loading.value = true;
    const response = await getResume(id);
    resume.value = response.data;
    
    // 设置现任职务信息
    if (resume.value.currentPosition) {
      currentPosition.value.currentPosition = resume.value.currentPosition;
    }
  } catch (error) {
    console.error("获取简历详情失败:", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getResumeData();
});
</script>

<style scoped>
.form-title {
  text-align: center;
  margin-bottom: 30px;
}

.form-title h1 {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin: 0;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin: 20px 0 10px 0;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.basic-info-container {
  display: flex;
  margin-bottom: 20px;
  gap: 20px;
}

.avatar-container {
  flex: 0 0 150px; /* 固定头像宽度 */
  height: 150px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}

.info-container {
  flex: 1;
}

.info-container :deep(.el-descriptions) {
  background-color: #f5f7fa;
}

.info-container :deep(.el-descriptions__header) {
  background-color: #f5f7fa;
}

.info-container :deep(.el-descriptions__body) {
  background-color: #f5f7fa;
}

.info-container :deep(.el-descriptions__cell) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: bold;
  text-align: center;
}

.timeline-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  background-color: #fafafa;
}

.timeline-item {
  display: flex;
  margin-bottom: 10px;
  line-height: 1.6;
}

.timeline-date {
  min-width: 150px;
  font-weight: bold;
  color: #303133;
  margin-right: 15px;
}

.timeline-content {
  flex: 1;
  color: #606266;
}

.timeline-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.timeline-subtitle {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.timeline-description {
  font-size: 14px;
  color: #606266;
  margin-top: 5px;
}

.timeline-achievement {
  font-size: 14px;
  color: #409eff;
  margin-top: 5px;
}

.empty-data {
  text-align: center;
  color: #909399;
  padding: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .basic-info-container {
    flex-direction: column;
    align-items: center;
  }

  .avatar-container {
    margin-bottom: 20px;
  }
}
</style>
