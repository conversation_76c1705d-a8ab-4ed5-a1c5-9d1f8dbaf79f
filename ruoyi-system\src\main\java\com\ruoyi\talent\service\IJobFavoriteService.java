package com.ruoyi.talent.service;

import com.ruoyi.talent.domain.JobFavorite;
import java.util.List;

/**
 * 职位收藏Service接口
 */
public interface IJobFavoriteService {
    /**
     * 查询用户收藏的职位列表
     */
    public List<JobFavorite> selectJobFavoriteList(JobFavorite jobFavorite);

    /**
     * 新增职位收藏
     */
    public int insertJobFavorite(JobFavorite jobFavorite);

    /**
     * 删除职位收藏
     */
    public int deleteJobFavorite(Long id);

    /**
     * 批量删除职位收藏
     */
    public int deleteJobFavoriteByIds(Long[] ids);

    /**
     * 检查是否已收藏
     */
    public boolean checkIsFavorite(Long userId, Long jobId);

    /**
     * 切换收藏状态
     */
    public boolean toggleFavorite(Long userId, Long jobId);
} 