<template>
  <view class="no-permission">
    <image class="no-permission-image" src="/static/no_permission.png" mode="aspectFit" />
    <text class="no-permission-text">无访问权限</text>
    <text class="no-permission-description">很抱歉，您没有权限访问此页面。</text>
    
  </view>
</template>

<script setup>
import { onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
onLoad((options) => {
	console.log('接收传值：',options)
	uni.setNavigationBarTitle({
			title:options.title,
	});
});
const goHome = () => {
  uni.showToast({
    title: '返回首页',
    icon: 'none'
  });
  // 实际应用中替换为跳转到首页的逻辑
  // uni.navigateTo({ url: '/pages/home/<USER>' });
};

const login = () => {
  uni.showToast({
    title: '跳转到登录页面',
    icon: 'none'
  });
  // 实际应用中替换为跳转到登录页面的逻辑
  // uni.navigateTo({ url: '/pages/login/login' });
};

onMounted(() => {
  // 可以在这里添加页面加载时的逻辑

});
</script>

<style lang="scss">
body{
	  background-color: #f0f2f5;
}
.no-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background-color: #f0f2f5;
}

.no-permission-image {
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 60rpx;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20rpx); }
}

.no-permission-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 30rpx;
}

.no-permission-description {
  font-size: 32rpx;
  color: #4a4a4a;
  margin-bottom: 80rpx;
  text-align: center;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 40rpx;
}

.button {
  padding: 20rpx 60rpx;
  border-radius: 50rpx;
  color: #fff;
  font-size: 32rpx;
  border: none;
}

.home-button {
  background-color: #1890ff;
}

.login-button {
  background-color: #52c41a;
}
</style>