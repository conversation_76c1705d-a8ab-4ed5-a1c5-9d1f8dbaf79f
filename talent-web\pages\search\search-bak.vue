<template>
  <div class="dashboard">
    <div class="tabs">
		<button :class="{ active: activeTab === 'hot-services' }" >员工服务</button>
      <!-- <button :class="{ active: activeTab === 'hot-services' }" @click="activeTab = 'hot-services'">热门服务</button> -->
      <!-- <button :class="{ active: activeTab === 'my-common' }" @click="activeTab = 'my-common'">我的常用</button> -->
    </div>
    <div class="content" v-if="activeTab === 'hot-services'">
      <div class="service-grid">
        <div class="service-item">
          <img src="/static/gzqk.png" />
          <span>工资情况</span>
        </div>
        <div class="service-item">
          <img src="/static/njqk.png"  />
          <span>年假查询</span>
        </div>
        <div class="service-item">
          <img src="/static/kqqk.png"  />
          <span>考勤情况</span>
        </div>
        <div class="service-item">
          <img src="/static/grqk.png"  />
          <span>个人信息</span>
        </div>
      </div>
    </div>
    <div class="content" v-else>
      <!-- 我的常用 tab 的内容 -->
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'hot-services'
    };
  }
};
</script>

<style scoped>
.dashboard {
  font-family: Arial, sans-serif;
/*  padding-left: 10px;
  padding-right: 10px; */
  background-color: #f0f0f0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  
}

.tabs {
  display: flex;
  justify-content: center;
}

.tabs button {
  /* padding: 10px 20px; */
  /* border: none; */
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background-color: #f0f0f0;
  cursor: pointer;
  line-height:1.8;
  font-size: 15px;
}
button::after{
	
    border:none;
/* 	border-left: 1px solid rgba(0, 0, 0, 0.2);
	border-right: 1px solid rgba(0, 0, 0, 0.2); */
	box-shadow: 0 0 10px rgb(0 0 0 / 10%);
/* 	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0; */
}

.tabs button.active {
  background-color: #fff;
  border-top: 3px solid #007BFF;
  color: #007BFF;
}


.content {
  background-color: #fff;
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); */
  width: 90%;
  padding-left: 10px;
  padding-right: 10px;
}
img{
	width: 30px;
	height: 30px;
}
.service-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.service-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  padding: 5px;
  border-radius: 8px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.service-item img {
  margin-right: 10px;
}

.service-item span {
  font-size: 16px;
  color: #333;
}
</style>
