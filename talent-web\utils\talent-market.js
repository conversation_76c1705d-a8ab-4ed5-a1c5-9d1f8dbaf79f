import { createHttp } from './request';
import config from '@/config';

// 创建默认的 HTTP 客户端实例
const defaultHttp = createHttp();

// API 方法
export const getJobList = (token, params) =>
    createApiMethod('talent/job/publicList', 'GET')(token, params);

// 改进的 API 方法工厂函数
const createApiMethod = (urlPattern, method = 'GET') => async(token, data = null, urlParams = {}) => {
    try {
        // 处理 URL 参数替换
        let url = urlPattern;
        if (urlParams) {
            Object.keys(urlParams).forEach(key => {
                url = url.replace(`:${key}`, urlParams[key]);
            });
        }

        // 根据 method 选择对应的 HTTP 方法
        switch (method.toUpperCase()) {
            case 'GET':
                return await defaultHttp.get(url, data, { 'Authorization': token });
            case 'POST':
                return await defaultHttp.post(url, data, { 'Authorization': token });
            case 'PUT':
                return await defaultHttp.put(url, data, { 'Authorization': token });
            case 'DELETE':
                return await defaultHttp.delete(url, { 'Authorization': token });
            default:
                return await defaultHttp.get(url, data, { 'Authorization': token });
        }
    } catch (error) {
        console.error(`${urlPattern} 请求失败:`, error);
        return null;
    }
};

// 查询职位详细
export const getJobDetail = (token, id) =>
    createApiMethod('talent/job/' + id, 'GET')(token);

export const applyJob = (token, jobId, resumeId) =>
    createApiMethod('talent/job/apply', 'POST')(token, { jobId, resumeId });
export const collectJob = (token, jobId, data) => createApiMethod('talent/job/collect/:id', 'POST')(token, data, { id: jobId });
export const getJobApplyStatus = (token, jobId) => createApiMethod('talent/job/apply/status/:id', 'GET')(token, null, { id: jobId });
export const getMyResumes = createApiMethod('talent/resume/list', 'GET');
export const increaseJobViews = (token, jobId) => createApiMethod('talent/job/view/:id', 'POST')(token, null, { id: jobId });
export const createOnlineResume = createApiMethod('talent/resume/create', 'POST');
export const getDefaultResume = createApiMethod('talent/resume/default', 'GET');
export const deleteResume = (token, resumeId) => createApiMethod('talent/resume/:id', 'DELETE')(token, null, { id: resumeId });
// 获取简历详情
export const getResumeDetail = async(token, resumeId) => {
    try {
        // 获取基本简历信息
        const url = `${config.baseUrl}talent/resume/${resumeId}`;

        const response = await uni.request({
            url,
            method: 'GET',
            header: {
                'Authorization': token
            }
        });

        // 正确处理uni.request的返回值
        const result = response.data || {};

        if (result.code !== 200) {
            throw new Error(result.msg || '获取简历详情失败');
        }

        const resumeData = result.data;

        // 从教育经历中提取最高学历信息
        if (resumeData.education && resumeData.education.length > 0) {
            // 查找标记为最高学历的记录
            const highestEdu = resumeData.education.find(edu => edu.isHighest === true);

            if (highestEdu) {
                // 如果找到了标记为最高学历的记录，使用它
                resumeData.highestDegree = highestEdu.degree || '';
                resumeData.highestSchool = highestEdu.school || '';
            } else {
                // 如果没有标记最高学历，使用第一条记录
                resumeData.highestDegree = resumeData.education[0].degree || '';
                resumeData.highestSchool = resumeData.education[0].school || '';
            }
        } else {
            // 如果没有教育经历，设置为空
            resumeData.highestDegree = '';
            resumeData.highestSchool = '';
        }

        return result;
    } catch (error) {
        console.error('获取简历详情失败:', error);
        throw error;
    }
};

// 获取简历家庭成员信息
export const getResumeFamily = async(token, resumeId) => {
    try {
        const url = `${config.baseUrl}talent/resume/${resumeId}/family`;

        const response = await uni.request({
            url,
            method: 'GET',
            header: {
                'Authorization': token
            }
        });

        // 正确处理uni.request的返回值
        return response.data || {};
    } catch (error) {
        console.error('获取家庭成员信息失败:', error);
        return null;
    }
};

// 获取简历奖惩情况
export const getResumeAwards = async(token, resumeId) => {
    try {
        const url = `${config.baseUrl}talent/resume/${resumeId}/awards`;

        const response = await uni.request({
            url,
            method: 'GET',
            header: {
                'Authorization': token
            }
        });

        // 正确处理uni.request的返回值
        return response.data || {};
    } catch (error) {
        console.error('获取奖惩情况失败:', error);
        return null;
    }
};

// 获取简历现任职务
export const getResumePosition = async(token, resumeId) => {
    try {
        const url = `${config.baseUrl}talent/resume/${resumeId}/position`;

        const response = await uni.request({
            url,
            method: 'GET',
            header: {
                'Authorization': token
            }
        });

        // 正确处理uni.request的返回值
        return response.data || {};
    } catch (error) {
        console.error('获取现任职务信息失败:', error);
        return null;
    }
};

// 保存简历
export const saveResume = async(token, resumeData, resumeId) => {
    try {
        const url = resumeId ?
            `${config.baseUrl}talent/resume/${resumeId}` :
            `${config.baseUrl}talent/resume`;

        const method = resumeId ? 'PUT' : 'POST';

        // 发送简历基本数据
        const response = await uni.request({
            url,
            method,
            header: {
                'Authorization': token,
                'Content-Type': 'application/json'
            },
            data: resumeData
        });

        // 正确处理uni.request的返回值
        const result = response.data || {};

        if (result.code !== 200) {
            throw new Error(result.msg || '保存简历失败');
        }

        return result;
    } catch (error) {
        console.error('保存简历失败:', error);
        throw error;
    }
};

// 保存简历家庭成员信息
export const saveResumeFamily = async(token, resumeId, familyData) => {
    try {
        const url = `${config.baseUrl}talent/resume/${resumeId}/family`;

        const response = await uni.request({
            url,
            method: 'PUT',
            header: {
                'Authorization': token,
                'Content-Type': 'application/json'
            },
            data: familyData
        });

        // 正确处理uni.request的返回值
        const result = response.data || {};

        if (result.code !== 200) {
            throw new Error(result.msg || '保存家庭成员信息失败');
        }

        return result;
    } catch (error) {
        console.error('保存家庭成员信息失败:', error);
        throw error;
    }
};

// 保存简历奖惩情况
export const saveResumeAwards = async(token, resumeId, awardsData) => {
    try {
        const url = `${config.baseUrl}talent/resume/${resumeId}/awards`;

        const response = await uni.request({
            url,
            method: 'PUT',
            header: {
                'Authorization': token,
                'Content-Type': 'application/json'
            },
            data: awardsData
        });

        // 正确处理uni.request的返回值
        const result = response.data || {};

        if (result.code !== 200) {
            throw new Error(result.msg || '保存奖惩情况失败');
        }

        return result;
    } catch (error) {
        console.error('保存奖惩情况失败:', error);
        throw error;
    }
};

// 保存简历现任职务
export const saveResumePosition = async(token, resumeId, positionData) => {
    try {
        const url = `${config.baseUrl}talent/resume/${resumeId}/position`;

        const response = await uni.request({
            url,
            method: 'PUT',
            header: {
                'Authorization': token,
                'Content-Type': 'application/json'
            },
            data: positionData
        });

        // 正确处理uni.request的返回值
        const result = response.data || {};

        if (result.code !== 200) {
            throw new Error(result.msg || '保存现任职务信息失败');
        }

        return result;
    } catch (error) {
        console.error('保存现任职务信息失败:', error);
        throw error;
    }
};

// 上传相关方法
export const uploadResumeFile = async(token, file) => {
    try {
        const res = await defaultHttp.upload('talent/resume/upload', file, {
            'Authorization': token
        });
        return res;
    } catch (error) {
        console.error('上传简历失败:', error);
        throw error;
    }
};

export const uploadAvatar = async(token, filePath) => {
    try {
        const res = await defaultHttp.upload('talent/resume/avatar', filePath, {
            'Authorization': token
        });

        console.log('Upload response:', res);

        if (!res) {
            throw new Error('上传失败');
        }

        return {
            code: res.code || 500,
            msg: res.msg || res.data, // 兼容处理，优先使用msg，如果没有则使用data
            message: res.msg || '上传失败'
        };

    } catch (error) {
        console.error('上传头像失败:', error);
        return {
            code: 500,
            message: error.message || '上传失败'
        };
    }
};

// 添加获取未读消息数量的方法
export const getUnreadMessageCount = async(token) => {
    try {
        const res = await defaultHttp.get('talent/message/unread/count', null, {
            'Authorization': token
        });
        return res;
    } catch (error) {
        console.error('获取未读消息数量失败:', error);
        return {
            code: 500,
            data: {
                count: 0
            },
            message: error.message || '获取未读消息数量失败'
        };
    }
};

// 添加获取消息列表的方法
export const getMessageList = async(token, params = {}) => {
    try {
        const res = await defaultHttp.get('talent/message/list', params, {
            'Authorization': token
        });
        return res;
    } catch (error) {
        console.error('获取消息列表失败:', error);
        return {
            code: 500,
            data: [],
            message: error.message || '获取消息列表失败'
        };
    }
};

// 标记消息已读
export const readMessage = async(token, messageId) => {
    try {
        const res = await defaultHttp.put(`talent/message/read/${messageId}`, null, {
            'Authorization': token
        });
        return res;
    } catch (error) {
        console.error('标记消息已读失败:', error);
        return {
            code: 500,
            message: error.message || '标记消息已读失败'
        };
    }
}

/**
 * 获取职位统计信息
 * @param {string} token - 用户token
 * @returns {Promise} 统计信息
 */
export const getJobDashboard = async(token) => {
    try {
        const res = await defaultHttp.get('talent/job/dashboard', {}, {
            'Authorization': token
        });
        return res;
    } catch (error) {
        console.error('获取职位统计信息失败:', error);
        return {
            data: {
                totalJobs: 0,
                todayNewJobs: 0,
            }
        };
    }
};

/**
 * 获取简历统计数据
 * @param {string} token 用户token
 * @returns {Promise<Object>} 统计数据
 */
export const getResumeStats = async(token) => {
    try {
        const res = await defaultHttp.get('talent/resume/stats', null, {
            'Authorization': token
        });

        if (res && res.code === 200) {
            return res;
        } else {
            throw new Error(res.msg || '获取统计数据失败');
        }
    } catch (error) {
        console.error('获取简历统计数据失败:', error);
        throw error;
    }
};

// 获取投递记录列表
export const getDeliveryList = async(token, params = {}) => {
    try {
        const res = await defaultHttp.get('talent/delivery/list', params, {
            'Authorization': token
        });
        return res;
    } catch (error) {
        console.error('获取投递记录失败:', error);
        return {
            code: 500,
            rows: [],
            total: 0,
            msg: '获取投递记录失败'
        };
    }
};

/**
 * 取消投递
 * @param {string} token - 用户token
 * @param {string} deliveryId - 投递记录ID
 * @returns {Promise} 取消结果
 */
export const cancelDelivery = async(token, deliveryId) => {
    try {
        const res = await defaultHttp.put('talent/job/delivery/status', {
            id: deliveryId,
            status: '-1', // 设置为"取消"状态
            feedback: '用户取消投递'
        }, {
            'Authorization': token
        });
        return res;
    } catch (error) {
        console.error('取消投递失败:', error);
        return {
            code: 500,
            message: error.message || '取消投递失败'
        };
    }
};

/**
 * 获取招聘单位列表
 * @param {string} token - 用户token
 * @returns {Promise} 单位列表
 */
export const getCompanyList = async(token) => {
    try {
        const res = await defaultHttp.get('talent/company/list', {}, {
            'Authorization': token
        });
        return res;
    } catch (error) {
        console.error('获取单位列表失败:', error);
        return {
            code: 500,
            rows: [],
            total: 0,
            msg: '获取单位列表失败'
        };
    }
};

// 获取收藏职位列表
export const getFavoriteJobs = async(token, params = {}) => {
    try {
        const res = await defaultHttp.get('talent/job/favorite/list', params, {
            'Authorization': token
        });
        return res;
    } catch (error) {
        console.error('获取收藏职位失败:', error);
        return {
            code: 500,
            rows: [],
            total: 0,
            msg: '获取收藏职位失败'
        };
    }
};

/**
 * 获取轮播图列表
 * @param {string} token - 用户token
 * @returns {Promise} 轮播图列表
 */
export const getBannerList = async(token) => {
    try {
        const res = await defaultHttp.get('talent/banner/list', null, {
            'Authorization': token
        });
        return res;
    } catch (error) {
        console.error('获取轮播图失败:', error);
        return {
            code: 500,
            data: [],
            message: error.message || '获取轮播图失败'
        };
    }
};

// 检查是否已收藏
export function checkFavorite(token, jobId) {
    return defaultHttp.get(`talent/favorite/check/${jobId}`, null, {
        'Authorization': token
    });
}

// 切换收藏状态
export function toggleFavorite(token, jobId) {
    return defaultHttp.post(`talent/favorite/toggle/${jobId}`, null, {
        'Authorization': token
    });
}

// 获取收藏列表
export function listFavorite(token, params) {
    return defaultHttp.get('talent/favorite/list', params, {
        'Authorization': token
    });
}

// 取消收藏
export function cancelFavorite(token, id) {
    return defaultHttp.delete(`talent/favorite/${id}`, null, {
        'Authorization': token
    });
}