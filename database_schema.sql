-- 简历家庭成员表
CREATE TABLE `resume_family` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `resume_id` varchar(32) NOT NULL COMMENT '简历ID',
  `relation` varchar(50) DEFAULT NULL COMMENT '称谓',
  `name` varchar(100) DEFAULT NULL COMMENT '姓名',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `political_status` varchar(50) DEFAULT NULL COMMENT '政治面貌',
  `work_unit` varchar(200) DEFAULT NULL COMMENT '工作单位及职务',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简历家庭成员表';

-- 简历奖惩情况表
CREATE TABLE `resume_awards` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `resume_id` varchar(32) NOT NULL COMMENT '简历ID',
  `awards` text DEFAULT NULL COMMENT '奖惩情况',
  `annual_review` text DEFAULT NULL COMMENT '年度考核结果',
  `appointment_reason` text DEFAULT NULL COMMENT '任免理由',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简历奖惩情况表';

-- 简历现任职务表
CREATE TABLE `resume_position` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `resume_id` varchar(32) NOT NULL COMMENT '简历ID',
  `current_position` varchar(200) DEFAULT NULL COMMENT '现任职务',
  `proposed_position` varchar(200) DEFAULT NULL COMMENT '拟任职务',
  `proposed_removal` varchar(200) DEFAULT NULL COMMENT '拟免职务',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简历现任职务表'; 

-- 在resume表中增加"现任职务"字段
ALTER TABLE resume ADD COLUMN current_position VARCHAR(200) DEFAULT NULL COMMENT '现任职务'; 