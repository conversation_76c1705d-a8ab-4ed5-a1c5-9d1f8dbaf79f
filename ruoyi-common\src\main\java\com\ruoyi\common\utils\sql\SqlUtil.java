package com.ruoyi.common.utils.sql;

import com.ruoyi.common.exception.UtilException;
import com.ruoyi.common.utils.StringUtils;

/**
 * sql操作工具类
 * 
 * <AUTHOR>
 */
public class SqlUtil
{
    /**
     * 定义常用的 sql关键字
     */
    public static String SQL_REGEX = "\u000B|and |extractvalue|updatexml|sleep|exec |insert |select |delete |update |drop |count |chr |mid |master |truncate |char |declare |or |union |like |+|/*|*/|user()|database()|system()|schema()|version()|@@|hex|unhex|load_file|outfile|dumpfile|benchmark|regexp|having|group by|order by|;|--|#|%|$|\\\\|\\u|0x";

    /**
     * 仅支持字母、数字、下划线、空格、逗号、小数点（支持多个字段排序）
     */
    public static String SQL_PATTERN = "[a-zA-Z0-9_\\ \\,\\.]+";

    /**
     * 限制orderBy最大长度
     */
    private static final int ORDER_BY_MAX_LENGTH = 500;

    /**
     * 检查字符，防止注入绕过
     */
    public static String escapeOrderBySql(String value)
    {
        if (StringUtils.isNotEmpty(value) && !isValidOrderBySql(value))
        {
            throw new UtilException("参数不符合规范，不能进行查询");
        }
        if (StringUtils.length(value) > ORDER_BY_MAX_LENGTH)
        {
            throw new UtilException("参数已超过最大限制，不能进行查询");
        }
        return value;
    }

    /**
     * 验证 order by 语法是否符合规范
     */
    public static boolean isValidOrderBySql(String value)
    {
        return value.matches(SQL_PATTERN);
    }

    /**
     * SQL关键字检查
     */
    public static void filterKeyword(String value)
    {
        if (StringUtils.isEmpty(value))
        {
            return;
        }
        
        // 转换为小写进行检查
        String lowerValue = value.toLowerCase();
        
        // 检查是否包含编码后的SQL注入尝试
        if (containsEncodedInjection(lowerValue))
        {
            throw new UtilException("参数存在SQL注入风险（编码绕过）");
        }
        
        String[] sqlKeywords = StringUtils.split(SQL_REGEX, "\\|");
        for (String sqlKeyword : sqlKeywords)
        {
            if (StringUtils.indexOfIgnoreCase(value, sqlKeyword) > -1)
            {
                throw new UtilException("参数存在SQL注入风险");
            }
        }
        
        // 检查常见的SQL注入模式
        checkSqlInjectionPatterns(lowerValue);
    }
    
    /**
     * 检查是否包含编码后的SQL注入尝试
     */
    private static boolean containsEncodedInjection(String value)
    {
        // 检查十六进制编码
        if (value.contains("0x") && value.matches(".*0x[0-9a-f]{2,}.*"))
        {
            return true;
        }
        
        // 检查URL编码
        if (value.contains("%") && value.matches(".*%[0-9a-f]{2}.*"))
        {
            return true;
        }
        
        // 检查Unicode编码
        if (value.contains("\\u") && value.matches(".*\\\\u[0-9a-f]{4}.*"))
        {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查常见的SQL注入模式
     */
    private static void checkSqlInjectionPatterns(String value)
    {
        // 检查常见的SQL注入模式
        String[] patterns = {
            ".*'\\s*or\\s*'.*'\\s*=\\s*'.*", // 'or''='
            ".*\"\\s*or\\s*\".*\"\\s*=\\s*\".*", // "or""="
            ".*'\\s*or\\s*[0-9].*=.*[0-9].*", // 'or 1=1
            ".*\"\\s*or\\s*[0-9].*=.*[0-9].*", // "or 1=1
            ".*'\\s*;.*--.*", // '; --
            ".*\"\\s*;.*--.*", // "; --
            ".*'\\s*;.*#.*", // '; #
            ".*\"\\s*;.*#.*", // "; #
            ".*(union|select|update|delete|insert|drop|alter)\\s+.*", // union select
            ".*exec\\s*\\(.*\\).*", // exec()
            ".*'\\s*waitfor\\s*delay\\s*'.*", // waitfor delay
            ".*'\\s*sleep\\s*\\(.*\\).*" // sleep()
        };
        
        for (String pattern : patterns)
        {
            if (value.matches(pattern))
            {
                throw new UtilException("参数存在SQL注入风险（模式匹配）");
            }
        }
    }
}
