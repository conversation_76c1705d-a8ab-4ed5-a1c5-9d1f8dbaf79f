<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ResumeWorkMapper">
    
    <resultMap type="ResumeWork" id="WorkResult">
        <id     property="id"          column="id"          />
        <result property="resumeId"    column="resume_id"   />
        <result property="company"     column="company"     />
        <result property="position"    column="position"    />
        <result property="department"  column="department"  />
        <result property="startDate"   column="start_date"  />
        <result property="endDate"     column="end_date"    />
        <result property="description" column="description" />
    </resultMap>

    <select id="selectByResumeId" parameterType="String" resultMap="WorkResult">
        select * from resume_work 
        where resume_id = #{resumeId}
        order by start_date desc
    </select>

    <insert id="insert" parameterType="ResumeWork">
        insert into resume_work (
            id, resume_id, company, position, department,
            start_date, end_date, description
        ) values (
            #{id}, #{resumeId}, #{company}, #{position}, #{department},
            #{startDate}, #{endDate}, #{description}
        )
    </insert>

    <update id="update" parameterType="ResumeWork">
        update resume_work
        <set>
            <if test="company != null">company = #{company},</if>
            <if test="position != null">position = #{position},</if>
            <if test="department != null">department = #{department},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="description != null">description = #{description},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="String">
        delete from resume_work where id = #{id}
    </delete>

    <delete id="deleteByResumeId" parameterType="String">
        delete from resume_work where resume_id = #{resumeId}
    </delete>
</mapper> 