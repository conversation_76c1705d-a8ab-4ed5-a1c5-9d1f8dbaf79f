package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.Resume;
import com.ruoyi.system.domain.ResumeEducation;
import com.ruoyi.system.domain.ResumeWork;
import com.ruoyi.system.domain.ResumeProject;
import com.ruoyi.system.domain.ResumeSkill;
import com.ruoyi.system.domain.JobDelivery;
import com.ruoyi.system.domain.ResumeDelivery;
import com.ruoyi.system.domain.ResumeAttachment;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ResumeMapper {
    List<Resume> selectResumeList(Resume resume);
    
    Resume selectResumeById(String id);
    
    Resume selectDefaultResume(String userId);
    
    int insertResume(Resume resume);
    
    int updateResume(Resume resume);
    
    int deleteResumeById(String id);
    
    int clearDefaultStatus(String userId);
    
    int setDefaultStatus(String id);
    
    int updateViews(String id);
    
    JobDelivery selectDeliveryByJobAndUser(@Param("jobId") String jobId, @Param("userId") String userId);
    
    int insertEducation(ResumeEducation education);
    
    int insertWork(ResumeWork work);
    
    int insertProject(ResumeProject project);
    
    int insertSkill(ResumeSkill skill);
    
    List<ResumeEducation> selectEducationByResumeId(String resumeId);
    
    List<ResumeWork> selectWorkByResumeId(String resumeId);
    
    List<ResumeProject> selectProjectByResumeId(String resumeId);
    
    List<ResumeSkill> selectSkillByResumeId(String resumeId);
    
    int deleteEducationByResumeId(String resumeId);
    
    int deleteWorkByResumeId(String resumeId);
    
    int deleteProjectByResumeId(String resumeId);
    
    int deleteSkillByResumeId(String resumeId);

    /**
     * 更新投递状态
     * 
     * @param delivery 投递信息
     * @return 结果
     */
    int updateDeliveryStatus(ResumeDelivery delivery);

    /**
     * 插入简历附件记录
     * 
     * @param attachment 附件信息
     * @return 结果
     */
    int insertAttachment(ResumeAttachment attachment);

    /**
     * 统计简历总数
     * 
     * @return 简历总数
     */
    int countTotalResumes();
} 