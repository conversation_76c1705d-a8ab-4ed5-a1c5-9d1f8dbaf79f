# 软删除实现说明

## 概述

为了保证数据完整性和避免关联数据问题，系统中的核心业务数据（岗位、简历、企业）已全部改为软删除（逻辑删除）实现。

## 实现详情

### 1. 岗位删除（talent_job表）

**删除标记字段：** `status`
- `'0'` = 正常状态
- `'1'` = 已删除状态

**修改文件：**
- `ruoyi-system/src/main/resources/mapper/system/JobMapper.xml`
  - `deleteJobById` 和 `deleteJobByIds` 方法改为UPDATE操作
  - 查询条件增加 `AND j.status != '1'` 排除已删除数据

### 2. 企业删除（company表）

**删除标记字段：** `status`
- `'0'` = 正常状态  
- `'1'` = 已删除状态

**修改文件：**
- `ruoyi-system/src/main/resources/mapper/system/CompanyMapper.xml`
  - `deleteCompanyById` 和 `deleteCompanyByIds` 方法改为UPDATE操作
  - 查询条件增加 `AND c.status != '1'` 排除已删除数据

### 3. 简历删除（resume表）

**删除标记字段：** `status`
- `'draft'`, `'published'` 等 = 正常状态
- `'deleted'` = 已删除状态

**修改文件：**
- `ruoyi-system/src/main/resources/mapper/system/ResumeMapper.xml`
  - `deleteResumeById` 方法改为UPDATE操作
  - 查询条件增加 `AND r.status != 'deleted'` 排除已删除数据
- `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/ResumeServiceImpl.java`
  - 移除删除投递记录的逻辑，保持数据完整性

## 优势

1. **数据完整性**：避免因删除核心数据导致的关联数据孤立问题
2. **数据恢复**：误删除的数据可以通过修改状态字段恢复
3. **审计追踪**：保留完整的数据变更历史
4. **关联关系**：投递记录、浏览记录等关联数据不会因为主数据删除而丢失

## 注意事项

1. **查询性能**：所有查询都需要增加状态过滤条件
2. **数据清理**：需要定期清理真正不需要的软删除数据
3. **统计准确性**：统计查询需要正确排除已删除数据
4. **权限控制**：管理员可能需要查看已删除数据的权限

## 相关查询示例

```sql
-- 查询正常岗位
SELECT * FROM talent_job WHERE status = '0';

-- 查询正常企业  
SELECT * FROM company WHERE status = '0';

-- 查询正常简历
SELECT * FROM resume WHERE status != 'deleted';

-- 恢复误删除的岗位
UPDATE talent_job SET status = '0' WHERE id = ?;
```

## 后续建议

1. 考虑添加删除时间字段记录删除操作的时间
2. 考虑添加删除人字段记录执行删除操作的用户
3. 建立定期数据清理机制
4. 为管理员提供查看和恢复已删除数据的功能界面
