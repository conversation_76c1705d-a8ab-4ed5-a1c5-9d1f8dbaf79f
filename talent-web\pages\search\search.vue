<template>
  <div class="dashboard">
    <div class="tabs">
      <div class="tab-header">
        <h3>员工服务</h3>
        <div class="active-tab"></div>
		
      </div>
		<div class="bottom">
			
		</div>
    </div>
	<div class="content">
	  <div class="data-grid">
	    <div class="data-item" @click="goto('/pages/salary/salary')">
	      <img class="value-image" src="/static/<EMAIL>" alt="财务">
	      <span class="label">工资情况</span>
	    </div>
	    <div class="data-item" @click="goto('/pages/holiday/holiday')">
	      <img class="value-image" src="/static/<EMAIL>" alt="人资">
	      <span class="label">年假查询</span>
	    </div>
	    <div class="data-item" @click="goto('/pages/training/training')">
	      <img class="value-image" src="/static/<EMAIL>" alt="安全">
	      <span class="label">培训学时</span>
	    </div>
	    <div class="data-item" @click="goto('/pages/permissions/permissions')">
	      <img class="value-image" src="/static/<EMAIL>" alt="投资">
	      <span class="label">个人权限</span>
	    </div>
	  </div>
	</div>
    <!-- <div class="content">
      <div class="service-grid">
        <div class="service-item" @click="goto">
          <img src="/static/<EMAIL>" alt="工资情况" />
          <span>工资情况</span>
        </div>
        <div class="service-item" @click="goto">
          <img src="/static/<EMAIL>" alt="年假查询" />
          <span>年假查询</span>
        </div>
        <div class="service-item" @click="goto">
          <img src="/static/<EMAIL>" alt="考勤情况" />
          <span>培训学时</span>
        </div>
        <div class="service-item" @click="goto">
          <img src="/static/<EMAIL>" alt="个人信息" />
          <span>个人权限</span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'employee-services'
    };
	
  },
  methods: {
	goto(url) {
		if(url!=""){
			console.log(url)
			uni.navigateTo({
				url:url
			});
		}
		
	}
}
};
</script>

<style scoped>
.dashboard {
  font-family: Arial, sans-serif;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

}

.tabs {
  display: flex;
  justify-content: flex-start; /* 左对齐 */
  width: 100%;
  flex-direction: column;
  padding-left: 10px; /* 添加左边距 */
  padding-top: 5px; /* 添加左边距 */


}

.tab-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 75px;
}

.tab-header h3 {
  margin: 0;
  padding: 10px 2px;
  font-size: 16px;
  font-weight: 600;
  color: #427CE8 ;
}
.bottom{            
	width:90%;
	margin-left: 5px ;
	margin-right: 5px ;
	border:1px solid #EEEEEE;
	background-color: #EEEEEE;
}
.active-tab {
  width: 30px;
  height: 2px;
  background: #437CE9;
  border-radius: 202px 202px 202px 202px;
}

.content {
  background-color: #fff;
  /* padding: 20px; */
  border-radius: 8px;
  /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); */
}

.data-grid {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  /* gap: 10px; */

}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 60px;
  padding: 10px;
}

.value-image {
  width: 40px;
  height: 40px;
  margin-bottom: 5px;
}

.label {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 500;
  font-size: 14px;
  color: #666666;
  line-height: 18px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

</style>
