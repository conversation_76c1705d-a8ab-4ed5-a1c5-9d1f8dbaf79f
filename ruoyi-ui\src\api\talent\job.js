import request from '@/utils/request'

// 查询职位列表
export function listJob(query) {
    return request({
        url: '/talent/job/list',
        method: 'get',
        params: query
    })
}

// 查询职位详细
export function getJob(id) {
    return request({
        url: '/talent/job/' + id,
        method: 'get'
    })
}

// 新增职位
export function addJob(data) {
    return request({
        url: '/talent/job',
        method: 'post',
        data: data
    })
}

// 修改职位
export function updateJob(data) {
    return request({
        url: '/talent/job',
        method: 'put',
        data: data
    })
}

// 删除职位
export function delJob(id) {
    return request({
        url: '/talent/job/' + id,
        method: 'delete'
    })
}

// 导入职位数据
export function importJob(data) {
    return request({
        url: '/talent/job/importData',
        method: 'post',
        data: data
    })
}

// 下载职位导入模板
export function getImportTemplate() {
    return request({
        url: '/talent/job/importTemplate',
        method: 'get'
    })
}

// 导出职位
export function exportJob(query) {
    return request({
        url: '/talent/job/export',
        method: 'get',
        params: query
    })
}