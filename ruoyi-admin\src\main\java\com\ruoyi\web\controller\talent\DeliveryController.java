package com.ruoyi.web.controller.talent;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.ResumeDelivery;
import com.ruoyi.system.service.IResumeDeliveryService;
import com.ruoyi.talent.service.ICompanyAdminService;
import com.ruoyi.talent.domain.CompanyAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/talent/delivery")
public class DeliveryController extends BaseController {
    
    @Autowired
    private IResumeDeliveryService deliveryService;

    @Autowired
    private ICompanyAdminService companyAdminService;

    /**
     * 获取投递列表
     * 返回数据将包含:
     * 1. 职位名称(jobTitle)
     * 2. 应聘单位名称(companyName)
     * 以及其他投递记录基本信息
     */
    @GetMapping("/list")
    public TableDataInfo list(ResumeDelivery delivery) {
        startPage();
        Long userId = SecurityUtils.getUserId();
        
        // 判断用户角色和权限
        if (SecurityUtils.isAdmin(userId) || SecurityUtils.hasRole("yiji_admin")) {
            // 系统管理员和一级管理员可以查看所有记录
        } else {
            // 查询用户关联的企业信息
            Long companyId = companyAdminService.getAdminCompanyId(userId);
            if (companyId != null) {
                // 企业管理员只能查看本企业的投递记录
                delivery.setCompanyId(companyId);
            } else {
                // 普通用户只能查看自己的投递记录
                delivery.setUserId(userId);
            }
        }
        
        List<ResumeDelivery> list = deliveryService.selectDeliveryList(delivery);
        return getDataTable(list);
    }

    /**
     * 获取投递详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable String id) {
        ResumeDelivery delivery = deliveryService.selectDeliveryById(id);
        if (delivery == null) {
            return AjaxResult.error("投递记录不存在");
        }

        Long userId = SecurityUtils.getUserId();
        // 判断用户角色和权限
        if (SecurityUtils.isAdmin(userId) || SecurityUtils.hasRole("yiji_admin")) {
            // 系统管理员和一级管理员可以查看所有记录
            return AjaxResult.success(delivery);
        }

        // 检查是否是企业管理员
        Long companyId = companyAdminService.getAdminCompanyId(userId);
        if (companyId != null && companyId.equals(delivery.getCompanyId())) {
            // 企业管理员可以查看本企业的记录
            return AjaxResult.success(delivery);
        }

        // 检查是否是本人的记录
        if (userId.equals(delivery.getUserId())) {
            // 投递人可以查看自己的记录
        return AjaxResult.success(delivery);
        }

        return AjaxResult.error("没有权限查看此投递记录");
    }

    /**
     * 更新投递状态
     */
    @PutMapping("/{id}/status")
    public AjaxResult updateStatus(@PathVariable String id, @RequestBody ResumeDelivery delivery) {
        // 获取要更新的投递记录
        ResumeDelivery existDelivery = deliveryService.selectDeliveryById(id);
        if (existDelivery == null) {
            return AjaxResult.error("投递记录不存在");
        }

        Long userId = SecurityUtils.getUserId();
        // 判断用户角色和权限
        if (!SecurityUtils.isAdmin(userId) && !SecurityUtils.hasRole("yiji_admin")) {
            // 检查是否是企业管理员
            Long companyId = companyAdminService.getAdminCompanyId(userId);
            if (companyId == null || !companyId.equals(existDelivery.getCompanyId())) {
                return AjaxResult.error("没有权限更新此投递记录");
            }
        }

        delivery.setId(id);
        return toAjax(deliveryService.updateDeliveryStatus(delivery));
    }
}