import request from '@/utils/request'

// 查询投递列表
export function listDelivery(query) {
    return request({
        url: '/talent/delivery/list',
        method: 'get',
        params: query
    })
}

// 查询投递详细
export function getDelivery(id) {
    return request({
        url: '/talent/delivery/' + id,
        method: 'get'
    })
}

// 新增投递
export function addDelivery(data) {
    return request({
        url: '/talent/delivery',
        method: 'post',
        data: data
    })
}

// 修改投递状态
export function updateDeliveryStatus(id, data) {
    return request({
        url: '/talent/delivery/' + id + '/status',
        method: 'put',
        data: data
    })
}