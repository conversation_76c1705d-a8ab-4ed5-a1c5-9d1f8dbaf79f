// Mapper接口
package com.ruoyi.system.mapper;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.DepartmentStatistics;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.system.domain.TotalStatistics;

@Mapper
public interface TotalStatisticsMapper {
    /**
     * 查询最新的总体统计数据
     */
    public TotalStatistics selectLatestTotalStatistics();

    /**
     * 根据日期查询总体统计数据
     */
    public TotalStatistics selectTotalStatisticsByDate(Date statisticsDate);

    /**
     * 查询本月的新增量和新增激活量
     */
    public TotalStatistics selectMonthlyIncrement();
}

