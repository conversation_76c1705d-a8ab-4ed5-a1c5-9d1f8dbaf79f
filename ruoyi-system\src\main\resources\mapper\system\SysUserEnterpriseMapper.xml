<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserEnterpriseMapper">

    <resultMap id="SysUserEnterpriseResult" type="SysUserEnterprise">
        <id property="userId" column="user_id"/>
        <id property="enterpriseId" column="enterprise_id"/>
        <result property="enterpriseCorpId" column="enterprise_corp_id"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="enterpriseUserId" column="enterprise_user_id"/>
        <result property="enterpriseNickname" column="enterprise_nickname"/>
        <result property="enterpriseDeptId" column="enterprise_dept_id"/>
        <result property="enterpriseDeptName" column="enterprise_dept_name"/>
    </resultMap>

    <select id="selectByEnterpriseUserIdAndCorpId" resultMap="SysUserEnterpriseResult">
        SELECT *
        FROM sys_user_enterprise
        WHERE enterprise_user_id = #{enterpriseUserId}
          AND enterprise_corp_id = #{enterpriseCorpId}
            LIMIT 1
    </select>

    <insert id="insertSysUserEnterprise" parameterType="SysUserEnterprise" useGeneratedKeys="true" keyProperty="userId">
        insert into sys_user_enterprise (
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="enterpriseId != null and enterpriseId != 0">enterprise_id,</if>
        <if test="enterpriseCorpId != null and enterpriseCorpId != ''">enterprise_corp_id,</if>
        <if test="enterpriseName != null and enterpriseName != ''">enterprise_name,</if>
        <if test="enterpriseUserId != null and enterpriseUserId != ''">enterprise_user_id,</if>
        <if test="enterpriseNickname != null and enterpriseNickname != ''">enterprise_nickname,</if>
        <if test="enterpriseDeptId != null and enterpriseDeptId != 0">enterprise_dept_id,</if>
        <if test="enterpriseDeptName != null and enterpriseDeptName != ''">enterprise_dept_name,</if>
        create_time
        ) values (
        <if test="userId != null and userId != 0">#{userId},</if>
        <if test="enterpriseId != null and enterpriseId != 0">#{enterpriseId},</if>
        <if test="enterpriseCorpId != null and enterpriseCorpId != ''">#{enterpriseCorpId},</if>
        <if test="enterpriseName != null and enterpriseName != ''">#{enterpriseName},</if>
        <if test="enterpriseUserId != null and enterpriseUserId != ''">#{enterpriseUserId},</if>
        <if test="enterpriseNickname != null and enterpriseNickname != ''">#{enterpriseNickname},</if>
        <if test="enterpriseDeptId != null and enterpriseDeptId != 0">#{enterpriseDeptId},</if>
        <if test="enterpriseDeptName != null and enterpriseDeptName != ''">#{enterpriseDeptName},</if>
        sysdate()
        )
    </insert>

</mapper>
