package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.Job;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface JobMapper {
    /**
     * 查询职位列表
     */
    public List<Job> selectJobList(Job job);

    /**
     * 查询职位详细信息
     */
    public Job selectJobById(Long id);

    /**
     * 新增职位
     */
    public int insertJob(Job job);

    /**
     * 修改职位
     */
    public int updateJob(Job job);

    /**
     * 删除职位
     */
    public int deleteJobById(Long id);

    /**
     * 批量删除职位
     */
    public int deleteJobByIds(Long[] ids);

    /**
     * 更新浏览量
     */
    public int updateJobViews(Long id);

    /**
     * 更新投递数量
     */
    public int updateJobApplications(Long id);

    /**
     * 统计职位总数
     */
    public Integer countTotalJobs();

    /**
     * 统计今日新增职位数
     */
    public Integer countTodayNewJobs();

    /**
     * 统计今日申请数
     */
    public Integer countTodayApplications();

    /**
     * 统计总浏览量
     */
    public Integer countTotalViews();

    /**
     * 查询职位投递情况
     *
     * @param jobId 职位ID
     * @return 投递情况列表
     */
    public List<Map<String, Object>> selectDeliveryList(Long jobId);

    /**
     * 获取最新岗位
     *
     * @param limit 限制数量
     * @return 最新岗位列表
     */
    public List<Map<String, Object>> selectRecentJobs(int limit);

    /**
     * 获取热门岗位
     *
     * @param limit 限制数量
     * @return 热门岗位列表
     */
    public List<Map<String, Object>> selectHotJobs(int limit);

    /**
     * 更新投递状态
     * 
     * @param params 参数
     * @return 结果
     */
    public int updateDeliveryStatus(Map<String, Object> params);

    /**
     * 根据职位标题和公司名称查询职位
     */
    public Job selectJobByTitleAndCompany(@Param("param1") String title, @Param("param2") String company);
}