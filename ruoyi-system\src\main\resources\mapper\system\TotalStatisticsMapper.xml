<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TotalStatisticsMapper">
    <select id="selectLatestTotalStatistics" resultType="TotalStatistics">
        SELECT
            id,
            total_count as totalCount,
            activated_count as activatedCount,
            activation_rate as activationRate,
            statistics_date as statisticsDate,
            activated_new as activatedNew,
            total_new as totalNew,
            created_at as createdTime,
            updated_at as updatedTime
        FROM total_statistics
        ORDER BY statistics_date DESC LIMIT 1
    </select>

    <select id="selectTotalStatisticsByDate" parameterType="java.util.Date" resultType="TotalStatistics">
        SELECT
            id,
            total_count as totalCount,
            activated_count as activatedCount,
            activation_rate as activationRate,
            statistics_date as statisticsDate,
            activated_new as activatedNew,
            total_new as totalNew,
            created_at as createdTime,
            updated_at as updatedTime
        FROM total_statistics
        WHERE statistics_date = #{statisticsDate}
    </select>

    <!-- 新增：查询本月增量 -->
    <select id="selectMonthlyIncrement" resultType="TotalStatistics">
        SELECT
            COALESCE(
                    (SELECT total_count FROM total_statistics
                     WHERE DATE_FORMAT(statistics_date, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
                     ORDER BY statistics_date DESC LIMIT 1)
                -
                (SELECT total_count FROM total_statistics
                 WHERE DATE_FORMAT(statistics_date, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m')
                 ORDER BY statistics_date DESC LIMIT 1),
                0
            ) as totalCount,
            COALESCE(
                    (SELECT activated_count FROM total_statistics
                     WHERE DATE_FORMAT(statistics_date, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
                     ORDER BY statistics_date DESC LIMIT 1)
                -
                (SELECT activated_count FROM total_statistics
                 WHERE DATE_FORMAT(statistics_date, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m')
                 ORDER BY statistics_date DESC LIMIT 1),
                0
            ) as activatedCount,
            CURDATE() as statisticsDate
    </select>
</mapper>