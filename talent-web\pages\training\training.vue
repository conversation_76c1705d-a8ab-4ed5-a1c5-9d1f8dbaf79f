<template>
  <div class="training-dashboard">
    <header>
      <p>{{ currentYear }}年培训概览</p>
    </header>
    
    <div class="overview-grid">
      <div v-for="(item, index) in trainingOverview" :key="index" class="overview-card">
        <div class="card-icon" :class="item.color">
          <!-- <i :class="item.icon"></i> -->
		  <roc-icon-plus type="fas" :name="item.icon" :size="20" :color="item.color" :rotate="90" ></roc-icon-plus>
        </div>
        <div class="card-content">
          <h3>{{ item.title }}</h3>
          <div><span style="font-size: 22px;">{{ item.value }}</span><span>{{item.dw}}</span></div>
        </div>
      </div>
    </div>

    <div class="training-records">
      <h3>已完成课程</h3>
      <table>
        <thead>
          <tr>
            <th>课程名称</th>
            <th>完成日期</th>
            <th>时长</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(record, index) in completedCourses" :key="index">
            <td>{{ record.courseName }}</td>
            <td>{{ record.completionDate }}</td>
            <td>{{ record.duration }}</td>
            <td>
              <span class="status completed">{{ record.status }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="training-records">
      <h3>进行中课程</h3>
      <table>
        <thead>
          <tr>
            <th>课程名称</th>
            <th>开始日期</th>
            <th>预计完成日期</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(record, index) in ongoingCourses" :key="index">
            <td>{{ record.courseName }}</td>
            <td>{{ record.startDate }}</td>
            <td>{{ record.expectedCompletionDate }}</td>
            <td>
              <span class="status ongoing">{{ record.status }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="quick-actions">
      <h3>快速操作</h3>
      <button @click="enrollInCourse" class="btn primary">报名新课程</button>
      <button @click="viewAllCourses" class="btn secondary">查看所有课程</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const currentYear = new Date().getFullYear();

const trainingOverview = ref([
  { title: '总学时', value: '150', dw:'小时',icon: 'fas fa-clock', color: 'purple' },
  { title: '总课程数', value: '15', dw:'课',icon: 'fas fa-book', color: 'blue' },
  { title: '已完成课程', value: '10', dw:'课',icon: 'fas fa-check', color: 'green' },
  { title: '进行中课程', value: '5', dw:'课',icon: 'fas fa-spinner', color: 'orange' }
]);

const completedCourses = ref([
  { courseName: 'Vue 3 基础', completionDate: '2023-03-01', duration: '10 小时', status: '已完成' },
  { courseName: 'JavaScript 高级', completionDate: '2023-05-15', duration: '15 小时', status: '已完成' }
]);

const ongoingCourses = ref([
  { courseName: 'CSS 高级技巧', startDate: '2023-06-01', expectedCompletionDate: '2023-07-01', status: '进行中' },
  { courseName: 'React 入门', startDate: '2023-06-15', expectedCompletionDate: '2023-07-15', status: '进行中' }
]);

const enrollInCourse = () => {
  // 实现报名新课程的逻辑
  console.log('Enrolling in a new course');
};

const viewAllCourses = () => {
  // 实现查看所有课程的逻辑
  console.log('Viewing all courses');
};
</script>

<style scoped>
.training-dashboard {
  font-family: 'Roboto', sans-serif;
  max-width: 1000px;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 15px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

header {
  text-align: center;
  margin-bottom: 1rem;
}

header h1 {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
}

header p {
  color: #7f8c8d;
  font-size: 1rem;
}

.overview-grid {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 1rem;
}

.overview-card {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 1rem;
  flex: 1;
  display: flex;
  align-items: center;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 2rem;
  margin-right: 0.5rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.card-icon.purple { background-color: #f3e5f5; color: #9c27b0; }
.card-icon.blue { background-color: #e3f2fd; color: #2196f3; }
.card-icon.green { background-color: #e8f5e9; color: #4caf50; }
.card-icon.orange { background-color: #fff3e0; color: #ff9800; }

.card-content h3 {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 0.2rem;
}

.card-content p {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
}

.training-records {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
}

.training-records h3 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

th, td {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

th {
  font-weight: 600;
  color: #34495e;
}

.status {
  /* padding: 0.2rem 0.5rem; */
  border-radius: 10px;
  font-size: 0.75rem;
}

.status.completed { background-color: #e8f5e9; color: #4caf50; }
.status.ongoing { background-color: #fff3e0; color: #ff9800; }

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn.primary {
  background-color: #2196f3;
  color: white;
}

.btn.primary:hover {
  background-color: #1976d2;
}

.btn.secondary {
  background-color: #e0e0e0;
  color: #333;
}

.btn.secondary:hover {
  background-color: #bdbdbd;
}

@media (max-width: 768px) {
  .overview-grid {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}

@media (max-width: 300px) {
  .overview-card {
    flex: 1 1 100%;
    max-width: 80px;
    padding: 0.5rem;
  }
  
  .overview-grid {
    justify-content: space-around;
  }
}
</style>
