<template>
  <div class="app-container home">
    <!-- Welcome Banner -->
    <el-card class="welcome-card" shadow="hover">
      <template #header>
        <div class="welcome-header">
          <h2>欢迎回来，{{ userName }}</h2>
          <span class="welcome-time">{{ currentTime }}</span>
        </div>
      </template>
      <div class="welcome-content">
        <img src="../assets/logo/logo_w.png" alt="Logo" class="welcome-logo">
        <div class="welcome-info">
          <h3>中国水务企业微信后台管理系统</h3>
          <p>当前系统版本：{{ version }}</p>
        </div>
      </div>
    </el-card>

    <!-- Statistics Cards -->
    <el-row :gutter="20" class="statistics-row">
      <el-col :xs="24" :sm="12" :md="4" :lg="4">
        <el-card shadow="hover" class="statistics-card">
          <template #header>
            <div class="card-header">
              <i class="el-icon-user"></i>
              <span>用户总数</span>
            </div>
          </template>
          <div class="card-body">
            <span class="card-value">{{ userCount }}</span>
            <span class="card-trend up">
              <i class="el-icon-top"></i>
              {{ userGrowth }}%
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="4" :lg="4">
        <el-card shadow="hover" class="statistics-card">
          <template #header>
            <div class="card-header">
              <i class="el-icon-s-cooperation"></i>
              <span>企业总数</span>
            </div>
          </template>
          <div class="card-body">
            <span class="card-value">{{ companyCount }}</span>
            <span class="card-trend up">
              <i class="el-icon-top"></i>
              {{ companyGrowth }}%
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="4" :lg="4">
        <el-card shadow="hover" class="statistics-card">
          <template #header>
            <div class="card-header">
              <i class="el-icon-s-order"></i>
              <span>职位总数</span>
            </div>
          </template>
          <div class="card-body">
            <span class="card-value">{{ jobCount }}</span>
            <span class="card-trend up">
              <i class="el-icon-top"></i>
              {{ jobGrowth }}%
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="4" :lg="4">
        <el-card shadow="hover" class="statistics-card">
          <template #header>
            <div class="card-header">
              <i class="el-icon-document"></i>
              <span>简历总数</span>
            </div>
          </template>
          <div class="card-body">
            <span class="card-value">{{ resumeCount }}</span>
            <span class="card-trend up">
              <i class="el-icon-top"></i>
              {{ resumeGrowth }}%
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="4" :lg="4">
        <el-card shadow="hover" class="statistics-card">
          <template #header>
            <div class="card-header">
              <i class="el-icon-view"></i>
              <span>总浏览数</span>
            </div>
          </template>
          <div class="card-body">
            <span class="card-value">{{ viewCount }}</span>
            <span class="card-trend up">
              <i class="el-icon-top"></i>
              {{ viewGrowth }}%
            </span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Quick Access -->
    <el-card class="quick-access-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>快捷入口</h3>
        </div>
      </template>
      <el-row :gutter="20" class="quick-links">
        <el-col :xs="12" :sm="8" :md="4" v-for="(item, index) in quickLinks" :key="index">
          <div class="quick-link-item" @click="navigateTo(item.path)">
            <i :class="item.icon"></i>
            <span>{{ item.title }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- Activity Information -->
    <el-row :gutter="20" class="activity-row">
      <!-- Recent Jobs -->
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="activity-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <i class="el-icon-s-order"></i>
              <span>最新岗位</span>
              <el-tag v-if="todayStats.newJobs > 0" type="success" size="small">
                今日新增 {{ todayStats.newJobs }}
              </el-tag>
            </div>
          </template>
          <div class="activity-content">
            <div v-if="recentJobs.length === 0" class="empty-state">
              <i class="el-icon-document"></i>
              <p>暂无最新岗位</p>
            </div>
            <div v-else class="job-list">
              <div
                v-for="job in recentJobs"
                :key="job.id"
                class="job-item"
                @click="navigateTo('/talent/job')"
              >
                <div class="job-title">{{ job.title }}</div>
                <div class="job-info">
                  <span class="company">{{ job.company }}</span>
                  <span class="location">{{ job.location }}</span>
                  <span class="salary">{{ job.salary }}</span>
                </div>
                <div class="job-meta">
                  <span class="views">
                    <i class="el-icon-view"></i>
                    {{ job.views || 0 }}
                  </span>
                  <span class="time">{{ formatDaysAgo(job.days_ago) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- Hot Jobs -->
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="activity-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <i class="el-icon-star-on"></i>
              <span>热门岗位</span>
            </div>
          </template>
          <div class="activity-content">
            <div v-if="hotJobs.length === 0" class="empty-state">
              <i class="el-icon-star-off"></i>
              <p>暂无热门岗位</p>
            </div>
            <div v-else class="job-list">
              <div
                v-for="job in hotJobs"
                :key="job.id"
                class="job-item"
                @click="navigateTo('/talent/job')"
              >
                <div class="job-title">{{ job.title }}</div>
                <div class="job-info">
                  <span class="company">{{ job.company }}</span>
                  <span class="location">{{ job.location }}</span>
                  <span class="salary">{{ job.salary }}</span>
                </div>
                <div class="job-meta">
                  <span class="views hot">
                    <i class="el-icon-view"></i>
                    {{ job.views || 0 }}
                  </span>
                  <span class="applications">
                    <i class="el-icon-user"></i>
                    {{ job.applications || 0 }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- Recent Deliveries -->
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="activity-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <i class="el-icon-s-promotion"></i>
              <span>最新投递</span>
              <el-tag v-if="todayStats.newDeliveries > 0" type="warning" size="small">
                今日新增 {{ todayStats.newDeliveries }}
              </el-tag>
            </div>
          </template>
          <div class="activity-content">
            <div v-if="recentDeliveries.length === 0" class="empty-state">
              <i class="el-icon-s-promotion"></i>
              <p>暂无投递记录</p>
            </div>
            <div v-else class="delivery-list">
              <div
                v-for="delivery in recentDeliveries"
                :key="delivery.id"
                class="delivery-item"
                @click="navigateTo('/talent/delivery')"
              >
                <div class="delivery-header">
                  <span class="resume-name">{{ delivery.resume_name }}</span>
                  <el-tag
                    :type="getStatusType(delivery.status)"
                    size="mini"
                  >
                    {{ delivery.status_text }}
                  </el-tag>
                </div>
                <div class="delivery-info">
                  <div class="job-title">{{ delivery.job_title }}</div>
                  <div class="company">{{ delivery.company }}</div>
                </div>
                <div class="delivery-meta">
                  <span class="time">{{ formatDaysAgo(delivery.days_ago) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- System Info -->
    <el-card class="system-info-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>系统信息</h3>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="system-info-item">
            <span class="label">系统名称：</span>
            <span class="value">中国水务企业微信后台管理系统</span>
          </div>
          <div class="system-info-item">
            <span class="label">系统版本：</span>
            <span class="value">{{ version }}</span>
          </div>
          <div class="system-info-item">
            <span class="label">服务器环境：</span>
            <span class="value">CentOS / Nginx / JDK 1.8</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="system-info-item">
            <span class="label">数据库版本：</span>
            <span class="value">MySQL 5.7</span>
          </div>
          <div class="system-info-item">
            <span class="label">前端框架：</span>
            <span class="value">Vue 3.x / Element Plus</span>
          </div>
          <div class="system-info-item">
            <span class="label">后端框架：</span>
            <span class="value">Spring Boot 2.5.x</span>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup name="Index">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import useUserStore from '@/store/modules/user'
import { getUserCount, getCompanyCount, getJobCount, getResumeCount, getViewCount, getActivities } from '@/api/talent/dashboard'

// 获取router实例，只调用一次
const router = useRouter()
const version = ref('3.8.9')
const currentTime = ref('')
const userStore = useUserStore()

// 统计数据
const userCount = ref(0)
const companyCount = ref(0)
const jobCount = ref(0)
const resumeCount = ref(0)
const viewCount = ref(0)

// 活动数据
const recentJobs = ref([])
const hotJobs = ref([])
const recentDeliveries = ref([])
const todayStats = ref({ newJobs: 0, newDeliveries: 0 })

// 增长率数据
const userGrowth = ref(0)
const companyGrowth = ref(0)
const jobGrowth = ref(0)
const resumeGrowth = ref(0)
const viewGrowth = ref(0)

const userName = computed(() => {
  return userStore.name || '管理员'
})

const quickLinks = ref([
  { title: '用户管理', icon: 'el-icon-user', path: '/system/user' },
  { title: '角色管理', icon: 'el-icon-s-check', path: '/system/role' },
  { title: '菜单管理', icon: 'el-icon-menu', path: '/system/menu' },
  { title: '企业管理', icon: 'el-icon-office-building', path: '/talent/company' },
  { title: '职位管理', icon: 'el-icon-s-order', path: '/talent/talent' },
  { title: '简历管理', icon: 'el-icon-document', path: '/talent/resume/resume' }
])

function updateTime() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  
  currentTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

function navigateTo(path) {
  router.push(path)
}

// 获取统计数据
async function fetchDashboardData() {
  try {
    // 直接调用各个单独的接口获取数据
    const [userRes, companyRes, jobRes, resumeRes, viewRes] = await Promise.allSettled([
      getUserCount(),
      getCompanyCount(),
      getJobCount(),
      getResumeCount(),
      getViewCount()
    ])

    // 同时获取活动数据
    fetchActivityData()
    
    // 处理用户数据
    if (userRes.status === 'fulfilled' && userRes.value && userRes.value.code === 200) {
      userCount.value = userRes.value.data || 0
      userGrowth.value = 5 // 默认增长率
    }
    
    // 处理企业数据
    if (companyRes.status === 'fulfilled' && companyRes.value && companyRes.value.code === 200) {
      companyCount.value = companyRes.value.data || 0
      companyGrowth.value = 3 // 默认增长率
    }
    
    // 处理职位数据
    if (jobRes.status === 'fulfilled' && jobRes.value && jobRes.value.code === 200) {
      jobCount.value = jobRes.value.data || 0
      jobGrowth.value = 8 // 默认增长率
    }
    
    // 处理简历数据
    if (resumeRes.status === 'fulfilled' && resumeRes.value && resumeRes.value.code === 200) {
      resumeCount.value = resumeRes.value.data || 0
      resumeGrowth.value = 10 // 默认增长率
    }

    // 处理浏览数据
    if (viewRes.status === 'fulfilled' && viewRes.value && viewRes.value.code === 200) {
      viewCount.value = viewRes.value.data || 0
      viewGrowth.value = 15 // 默认增长率
    }
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
  }
}

// 获取活动数据
async function fetchActivityData() {
  try {
    const response = await getActivities()
    if (response && response.code === 200) {
      const data = response.data
      recentJobs.value = data.recentJobs || []
      hotJobs.value = data.hotJobs || []
      recentDeliveries.value = data.recentDeliveries || []
      todayStats.value = data.todayStats || { newJobs: 0, newDeliveries: 0 }
    }
  } catch (error) {
    console.error('获取活动数据失败:', error)
    // 设置默认值
    recentJobs.value = []
    hotJobs.value = []
    recentDeliveries.value = []
    todayStats.value = { newJobs: 0, newDeliveries: 0 }
  }
}

// 格式化天数
function formatDaysAgo(days) {
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days <= 7) return `${days}天前`
  return '一周前'
}

// 获取状态类型
function getStatusType(status) {
  switch (status) {
    case '0': return 'info'    // 待处理
    case '1': return 'success' // 已通过
    case '2': return 'danger'  // 已拒绝
    case '-1': return 'info'   // 已取消
    default: return 'info'
  }
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)

  // 获取仪表盘数据
  fetchDashboardData()
})
</script>

<style scoped lang="scss">
.home {
  padding: 20px;
  
  .welcome-card {
    margin-bottom: 20px;
    
    .welcome-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
      
      .welcome-time {
        font-size: 14px;
        color: #909399;
      }
    }
    
    .welcome-content {
      display: flex;
      align-items: center;
      
      .welcome-logo {
        width: 80px;
        height: auto;
        margin-right: 20px;
      }
      
      .welcome-info {
        h3 {
          margin: 0 0 10px 0;
          font-size: 18px;
          font-weight: 500;
        }
        
        p {
          margin: 0;
          color: #606266;
        }
      }
    }
  }
  
  .statistics-row {
    margin-bottom: 20px;

    .statistics-card {
      height: 100%;
      min-height: 120px;

      .card-header {
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          font-size: 18px;
        }

        span {
          font-size: 14px;
          font-weight: 500;
        }
      }

      .card-body {
        display: flex;
        align-items: baseline;
        justify-content: space-between;

        .card-value {
          font-size: 28px;
          font-weight: bold;
          color: #409eff;
        }

        .card-trend {
          font-size: 12px;

          &.up {
            color: #67c23a;
          }

          &.down {
            color: #f56c6c;
          }

          i {
            margin-right: 4px;
          }
        }
      }
    }

    // 响应式布局
    @media (max-width: 1200px) {
      .el-col-lg-4 {
        width: 50% !important;
        margin-bottom: 20px;
      }
    }

    @media (max-width: 768px) {
      .el-col-lg-4 {
        width: 100% !important;
      }
    }
  }
  
  .quick-access-card {
    margin-bottom: 20px;
    
    .quick-links {
      .quick-link-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100px;
        background-color: #f5f7fa;
        border-radius: 4px;
        cursor: pointer;
        margin-bottom: 20px;
        transition: all 0.3s;
        
        &:hover {
          background-color: #ecf5ff;
          color: #409eff;
          transform: translateY(-5px);
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        i {
          font-size: 30px;
          margin-bottom: 10px;
        }
        
        span {
          font-size: 14px;
        }
      }
    }
  }
  
  .system-info-card {
    .system-info-item {
      margin-bottom: 15px;

      .label {
        display: inline-block;
        width: 100px;
        color: #909399;
      }

      .value {
        color: #606266;
        font-weight: 500;
      }
    }
  }

  .activity-row {
    margin-bottom: 20px;
  }

  .activity-card {
    height: 400px;

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 16px;
        color: #409eff;
      }

      span {
        font-weight: 500;
        flex: 1;
      }
    }

    .activity-content {
      height: 320px;
      overflow-y: auto;

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #909399;

        i {
          font-size: 48px;
          margin-bottom: 16px;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }

      .job-list, .delivery-list {
        .job-item, .delivery-item {
          padding: 12px;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: background-color 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }

      .job-item {
        .job-title {
          font-weight: 500;
          color: #303133;
          margin-bottom: 8px;
          font-size: 14px;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .job-info {
          display: flex;
          gap: 12px;
          margin-bottom: 8px;
          font-size: 12px;
          color: #606266;

          .company {
            color: #409eff;
            font-weight: 500;
          }

          .location {
            color: #909399;
          }

          .salary {
            color: #f56c6c;
            font-weight: 500;
          }
        }

        .job-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: #909399;

          .views {
            display: flex;
            align-items: center;
            gap: 4px;

            &.hot {
              color: #f56c6c;
            }

            i {
              font-size: 12px;
            }
          }

          .applications {
            display: flex;
            align-items: center;
            gap: 4px;

            i {
              font-size: 12px;
            }
          }

          .time {
            color: #c0c4cc;
          }
        }
      }

      .delivery-item {
        .delivery-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .resume-name {
            font-weight: 500;
            color: #303133;
            font-size: 14px;
          }
        }

        .delivery-info {
          margin-bottom: 8px;

          .job-title {
            font-size: 13px;
            color: #606266;
            margin-bottom: 4px;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .company {
            font-size: 12px;
            color: #909399;
          }
        }

        .delivery-meta {
          display: flex;
          justify-content: flex-end;

          .time {
            font-size: 12px;
            color: #c0c4cc;
          }
        }
      }
    }
  }
}
</style>

