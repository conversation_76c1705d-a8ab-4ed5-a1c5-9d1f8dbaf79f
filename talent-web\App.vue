<script setup>
	import {getUrlCode, getWxAuthorize, getUserInfo} from "@/utils/wx.js";
	import { getMessageList } from '@/utils/talent-market'
	import authStore from '@/stores/auth.js'
	
	import { getCurrentInstance, ref } from 'vue';
	import { onLaunch, onShow, onHide } from '@dcloudio/uni-app';

		
	onLaunch(async (e) => {
		
		const {
			proxy,
			appContext: {
				config: {
					globalProperties: global
				}
			}
		} = getCurrentInstance();

		// 使用统一的认证状态管理
		try {
			// 初始化认证状态，并获取token
			const token = await authStore.initialize();

			// 无论token是否存在，都解决初始化Promise
			proxy.$isResolve();
			
			// 只有在token存在的情况下检查消息
			if (token) {
				checkUnreadMessages(token);
			}
		} catch (error) {
			console.error('认证初始化失败:', error);
			// 发生错误时也要解决Promise
			proxy.$isResolve();
		}
	})
	
	onShow(async (e) => {
			console.log('App Show')
	})
	
	onHide( () =>{
			console.log('App Hide')
	})

	async function checkUnreadMessages(token) {
		try {
			const result = await getMessageList(token, {
				page: 1,
				pageSize: 1000
			})
			
			if (result && result.code === 200 && Array.isArray(result.data)) {
				const unreadCount = result.data.filter(msg => msg.status === 'UNREAD').length
				if (unreadCount > 0) {
					uni.setTabBarBadge({
						index: 1,
						text: unreadCount > 99 ? '99+' : unreadCount.toString()
					})
				} else {
					uni.removeTabBarBadge({
						index: 1
					})
				}
			}
		} catch (error) {
			console.error('检查未读消息失败:', error)
		}
	}
</script>
 
<style lang="scss">
@import './static/yzb/yzb-icon.css';
/* 全局样式 background-color: #ffffff; /* 蓝色背景 */
page {
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 通用卡片样式 */
.card {
  background: #fff;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 通用按钮样式 */
.btn-primary {
  background: #3498db;
  color: #fff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
}

.btn-default {
  background: #f5f5f5;
  border: 1px solid #ddd;
  padding: 0.5rem 1rem;
  border-radius: 4px;
}

/* 标签样式 */
.tag {
  background: #f0f7ff;
  color: #3498db;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-right: 0.5rem;
}
</style>
