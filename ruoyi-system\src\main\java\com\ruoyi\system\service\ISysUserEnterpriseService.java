package com.ruoyi.system.service;


import com.ruoyi.common.core.domain.entity.SysUserEnterprise;

import java.util.List;

public interface ISysUserEnterpriseService {

    /**
     * 根据企业微信用户ID和企业微信的企业ID查询用户企业关联信息
     *
     * @param enterpriseUserId 企业微信用户ID
     * @param enterpriseCorpId 企业微信的企业ID
     * @return 用户企业关联信息
     */
    SysUserEnterprise selectByEnterpriseUserIdAndCorpId(String enterpriseUserId, String enterpriseCorpId);

    /**
     * 根据用户ID查询用户企业关联信息
     *
     * @param userId 用户ID
     * @return 用户企业关联信息列表
     */
    List<SysUserEnterprise> selectByUserId(Long userId);

    /**
     * 根据企业ID查询用户企业关联信息
     *
     * @param enterpriseId 企业ID
     * @return 用户企业关联信息列表
     */
    List<SysUserEnterprise> selectByEnterpriseId(Long enterpriseId);

    /**
     * 新增用户企业关联信息
     *
     * @param sysUserEnterprise 用户企业关联信息
     * @return 结果
     */
    int insertSysUserEnterprise(SysUserEnterprise sysUserEnterprise);

    /**
     * 修改用户企业关联信息
     *
     * @param sysUserEnterprise 用户企业关联信息
     * @return 结果
     */
    int updateSysUserEnterprise(SysUserEnterprise sysUserEnterprise);

    /**
     * 删除用户企业关联信息
     *
     * @param userId 用户ID
     * @param enterpriseId 企业ID
     * @return 结果
     */
    int deleteSysUserEnterprise(Long userId, Long enterpriseId);

    /**
     * 批量删除用户企业关联信息
     *
     * @param userIds 需要删除的用户ID数组
     * @return 结果
     */
    int deleteSysUserEnterpriseByUserIds(Long[] userIds);

    /**
     * 批量删除用户企业关联信息
     *
     * @param enterpriseIds 需要删除的企业ID数组
     * @return 结果
     */
    int deleteSysUserEnterpriseByEnterpriseIds(Long[] enterpriseIds);
}