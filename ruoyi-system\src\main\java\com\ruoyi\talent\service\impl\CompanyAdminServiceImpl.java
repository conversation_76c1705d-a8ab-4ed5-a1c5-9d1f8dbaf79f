package com.ruoyi.talent.service.impl;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talent.mapper.CompanyAdminMapper;
import com.ruoyi.talent.domain.CompanyAdmin;
import com.ruoyi.talent.service.ICompanyAdminService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 企业管理员关联Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class CompanyAdminServiceImpl implements ICompanyAdminService 
{
    @Autowired
    private CompanyAdminMapper companyAdminMapper;

    /**
     * 查询企业的管理员列表
     * 
     * @param companyId 企业ID
     * @return 管理员列表
     */
    @Override
    public List<CompanyAdmin> selectCompanyAdminList(Long companyId)
    {
        return companyAdminMapper.selectCompanyAdminList(companyId);
    }

    /**
     * 批量新增企业管理员关联
     * 
     * @param companyId 企业ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    public int insertCompanyAdmins(Long companyId, Long[] userIds)
    {
        List<CompanyAdmin> list = new ArrayList<CompanyAdmin>();
        for (Long userId : userIds)
        {
            CompanyAdmin companyAdmin = new CompanyAdmin();
            companyAdmin.setCompanyId(companyId);
            companyAdmin.setUserId(userId);
            companyAdmin.setCreateBy(SecurityUtils.getUsername());
            list.add(companyAdmin);
        }
        return companyAdminMapper.batchInsertCompanyAdmin(list);
    }

    /**
     * 删除企业管理员关联
     * 
     * @param companyId 企业ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteCompanyAdmin(Long companyId, Long userId)
    {
        return companyAdminMapper.deleteCompanyAdmin(companyId, userId);
    }

    /**
     * 检查用户是否是企业管理员
     * 
     * @param companyId 企业ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean checkIsCompanyAdmin(Long companyId, Long userId)
    {
        return companyAdminMapper.checkIsCompanyAdmin(companyId, userId) > 0;
    }

    /**
     * 获取当前用户管理的企业ID
     * 
     * @param userId 用户ID
     * @return 企业ID，如果不是企业管理员则返回null
     */
    public Long getAdminCompanyId(Long userId)
    {
        return companyAdminMapper.selectAdminCompanyId(userId);
    }
} 