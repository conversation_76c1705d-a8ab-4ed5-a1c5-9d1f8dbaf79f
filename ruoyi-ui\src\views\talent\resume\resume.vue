<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="简历标题" prop="resumeTitle">
        <el-input
          v-model="queryParams.resumeTitle"
          placeholder="请输入简历标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['talent:resume:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['talent:resume:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 简历列表 -->
    <el-table v-loading="loading" :data="resumeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="性别" align="center" prop="gender" />
      <el-table-column label="民族" align="center" prop="nation" />
      <el-table-column label="政治面貌" align="center" prop="politicalStatus" />
      <el-table-column label="现任职务" align="center" prop="currentPosition" />
      <el-table-column label="手机号码" align="center" prop="phone" />
      <el-table-column label="邮箱" align="center" prop="email" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleView(scope.row)"
            v-hasPermi="['talent:resume:query']"
          >查看</el-button>
          <el-button
            type="text"
            icon="Edit"
            @click="handleEdit(scope.row)"
            v-hasPermi="['talent:resume:edit']"
          >修改</el-button>
          <el-button
            type="text"
            icon="Download"
            @click="handleDownload(scope.row)"
            v-hasPermi="['talent:resume:query']"
          >下载</el-button>
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['talent:resume:remove']"
          >删除</el-button>
          
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 简历编辑对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="resumeForm" :model="form" :rules="rules" label-width="120px">
        <!-- 标题 -->
        <div class="form-title">
          <h2>干部任免审批表</h2>
        </div>

        <!-- 基本信息区域 - 两列布局 -->
        <div class="basic-info-container">
          <!-- 左侧头像区域 -->
          <div class="avatar-container">
            <el-upload
              class="avatar-uploader"
              :action="uploadImgUrl"
              :headers="headers"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload">
              <img v-if="form.avatar" :src="form.avatar" class="avatar-image" />
              <div v-else class="avatar-placeholder">
                <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
                <div>点击上传头像</div>
              </div>
            </el-upload>
          </div>

          <!-- 右侧基本信息 -->
          <div class="info-container">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="简历标题" prop="resumeTitle">
                  <el-input v-model="form.resumeTitle" placeholder="请输入简历标题" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="form.name" placeholder="请输入姓名" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="性别" prop="gender">
                  <el-select v-model="form.gender" placeholder="请选择性别" style="width: 100%">
                    <el-option label="男" value="男" />
                    <el-option label="女" value="女" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="出生日期" prop="birthday">
                  <el-date-picker
                    v-model="form.birthday"
                    type="date"
                    placeholder="选择出生日期"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="联系电话" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="电子邮箱" prop="email">
                  <el-input v-model="form.email" placeholder="请输入电子邮箱" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="民族" prop="nation">
                  <el-select v-model="form.nation" placeholder="请选择民族" style="width: 100%">
                    <el-option v-for="item in nationOptions" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="籍贯" prop="nativePlace">
                  <el-input v-model="form.nativePlace" placeholder="请输入籍贯" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="政治面貌" prop="politicalStatus">
                  <el-select v-model="form.politicalStatus" placeholder="请选择政治面貌" style="width: 100%">
                    <el-option v-for="item in politicalStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="专业技术职务" prop="technicalPosition">
                  <el-input v-model="form.technicalPosition" placeholder="请输入专业技术职务" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item label="现任职务" prop="currentPosition">
                  <el-input v-model="form.currentPosition" placeholder="请输入现任职务" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 教育经历 -->
        <div class="section-title">教育经历</div>
        <div class="timeline-container">
          <div v-for="(edu, index) in form.education" :key="index" class="timeline-item-edit">
            <div class="timeline-header">
              <span class="timeline-index">{{ index + 1 }}</span>
              <el-button type="danger" size="small" @click="removeEducation(index)" class="delete-btn">删除</el-button>
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开始时间" :prop="'education.' + index + '.startDate'">
                  <el-date-picker
                    v-model="edu.startDate"
                    type="date"
                    placeholder="选择开始时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" :prop="'education.' + index + '.endDate'">
                  <el-date-picker
                    v-model="edu.endDate"
                    type="date"
                    placeholder="选择结束时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="学校名称" :prop="'education.' + index + '.school'">
                  <el-input v-model="edu.school" placeholder="请输入学校名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="专业" :prop="'education.' + index + '.major'">
                  <el-input v-model="edu.major" placeholder="请输入专业" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="学历" :prop="'education.' + index + '.degree'">
                  <el-select v-model="edu.degree" placeholder="请选择学历" style="width: 100%">
                    <el-option label="专科" value="专科" />
                    <el-option label="本科" value="本科" />
                    <el-option label="硕士" value="硕士" />
                    <el-option label="博士" value="博士" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="学位" :prop="'education.' + index + '.degreeType'">
                  <el-input v-model="edu.degreeType" placeholder="请输入学位" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="描述" :prop="'education.' + index + '.description'">
              <el-input
                v-model="edu.description"
                type="textarea"
                :rows="2"
                placeholder="请输入教育经历描述"
              />
            </el-form-item>
          </div>

          <div class="add-item-container">
            <el-button type="primary" @click="addEducation" icon="Plus">添加教育经历</el-button>
          </div>
        </div>

        <!-- 工作经历 -->
        <div class="section-title">工作经历</div>
        <div class="timeline-container">
          <div v-for="(work, index) in form.work" :key="index" class="timeline-item-edit">
            <div class="timeline-header">
              <span class="timeline-index">{{ index + 1 }}</span>
              <el-button type="danger" size="small" @click="removeWork(index)" class="delete-btn">删除</el-button>
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开始时间" :prop="'work.' + index + '.startDate'">
                  <el-date-picker
                    v-model="work.startDate"
                    type="date"
                    placeholder="选择开始时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" :prop="'work.' + index + '.endDate'">
                  <el-date-picker
                    v-model="work.endDate"
                    type="date"
                    placeholder="选择结束时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="公司名称" :prop="'work.' + index + '.company'">
                  <el-input v-model="work.company" placeholder="请输入公司名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职位" :prop="'work.' + index + '.position'">
                  <el-input v-model="work.position" placeholder="请输入职位" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="部门" :prop="'work.' + index + '.department'">
                  <el-input v-model="work.department" placeholder="请输入部门" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="薪资" :prop="'work.' + index + '.salary'">
                  <el-input v-model="work.salary" placeholder="请输入薪资" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="工作描述" :prop="'work.' + index + '.description'">
              <el-input
                v-model="work.description"
                type="textarea"
                :rows="2"
                placeholder="请输入工作描述"
              />
            </el-form-item>

            <el-form-item label="工作成就" :prop="'work.' + index + '.achievement'">
              <el-input
                v-model="work.achievement"
                type="textarea"
                :rows="2"
                placeholder="请输入工作成就"
              />
            </el-form-item>
          </div>

          <div class="add-item-container">
            <el-button type="primary" @click="addWork" icon="Plus">添加工作经历</el-button>
          </div>
        </div>

        <!-- 项目经历 -->
        <div class="section-title">项目经历</div>
        <div class="timeline-container">
          <div v-for="(project, index) in form.project" :key="index" class="timeline-item-edit">
            <div class="timeline-header">
              <span class="timeline-index">{{ index + 1 }}</span>
              <el-button type="danger" size="small" @click="removeProject(index)" class="delete-btn">删除</el-button>
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开始时间" :prop="'project.' + index + '.startDate'">
                  <el-date-picker
                    v-model="project.startDate"
                    type="date"
                    placeholder="选择开始时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" :prop="'project.' + index + '.endDate'">
                  <el-date-picker
                    v-model="project.endDate"
                    type="date"
                    placeholder="选择结束时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目名称" :prop="'project.' + index + '.name'">
                  <el-input v-model="project.name" placeholder="请输入项目名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="担任角色" :prop="'project.' + index + '.role'">
                  <el-input v-model="project.role" placeholder="请输入担任角色" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="项目描述" :prop="'project.' + index + '.description'">
              <el-input
                v-model="project.description"
                type="textarea"
                :rows="2"
                placeholder="请输入项目描述"
              />
            </el-form-item>

            <el-form-item label="项目成果" :prop="'project.' + index + '.achievement'">
              <el-input
                v-model="project.achievement"
                type="textarea"
                :rows="2"
                placeholder="请输入项目成果"
              />
            </el-form-item>
          </div>

          <div class="add-item-container">
            <el-button type="primary" @click="addProject" icon="Plus">添加项目经历</el-button>
          </div>
        </div>

        <!-- 技能特长 -->
        <div class="section-title">技能特长</div>
        <div class="timeline-container">
          <div v-for="(skill, index) in form.skills" :key="index" class="timeline-item-edit">
            <div class="timeline-header">
              <span class="timeline-index">{{ index + 1 }}</span>
              <el-button type="danger" size="small" @click="removeSkill(index)" class="delete-btn">删除</el-button>
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="技能名称" :prop="'skills.' + index + '.name'">
                  <el-input v-model="skill.name" placeholder="请输入技能名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="熟练度" :prop="'skills.' + index + '.level'">
                  <el-rate v-model="skill.level" :max="5" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="技能描述" :prop="'skills.' + index + '.description'">
              <el-input
                v-model="skill.description"
                type="textarea"
                :rows="2"
                placeholder="请输入技能描述"
              />
            </el-form-item>
          </div>

          <div class="add-item-container">
            <el-button type="primary" @click="addSkill" icon="Plus">添加技能特长</el-button>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { listResume, getResume, delResume, addResume, updateResume, setDefaultResume } from "@/api/talent/resume";
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { Plus } from '@element-plus/icons-vue';
import { getToken } from '@/utils/auth';

const router = useRouter(); // 在组件顶层获取router实例

const resumeList = ref([]);
const open = ref(false);
const title = ref("");
const loading = ref(true);

// 民族选项
const nationOptions = [
  { value: '汉族', label: '汉族' },
  { value: '蒙古族', label: '蒙古族' },
  { value: '回族', label: '回族' },
  { value: '藏族', label: '藏族' },
  { value: '维吾尔族', label: '维吾尔族' },
  { value: '苗族', label: '苗族' },
  { value: '彝族', label: '彝族' },
  { value: '壮族', label: '壮族' },
  { value: '布依族', label: '布依族' },
  { value: '朝鲜族', label: '朝鲜族' },
  { value: '满族', label: '满族' },
  { value: '侗族', label: '侗族' },
  { value: '瑶族', label: '瑶族' },
  { value: '白族', label: '白族' },
  { value: '土家族', label: '土家族' },
  { value: '哈尼族', label: '哈尼族' },
  { value: '哈萨克族', label: '哈萨克族' },
  { value: '傣族', label: '傣族' },
  { value: '黎族', label: '黎族' },
  { value: '傈僳族', label: '傈僳族' },
  { value: '佤族', label: '佤族' },
  { value: '畲族', label: '畲族' },
  { value: '高山族', label: '高山族' },
  { value: '拉祜族', label: '拉祜族' },
  { value: '水族', label: '水族' },
  { value: '东乡族', label: '东乡族' },
  { value: '纳西族', label: '纳西族' },
  { value: '景颇族', label: '景颇族' },
  { value: '柯尔克孜族', label: '柯尔克孜族' },
  { value: '土族', label: '土族' },
  { value: '达斡尔族', label: '达斡尔族' },
  { value: '仫佬族', label: '仫佬族' },
  { value: '羌族', label: '羌族' },
  { value: '布朗族', label: '布朗族' },
  { value: '撒拉族', label: '撒拉族' },
  { value: '毛南族', label: '毛南族' },
  { value: '仡佬族', label: '仡佬族' },
  { value: '锡伯族', label: '锡伯族' },
  { value: '阿昌族', label: '阿昌族' },
  { value: '普米族', label: '普米族' },
  { value: '塔吉克族', label: '塔吉克族' },
  { value: '怒族', label: '怒族' },
  { value: '乌孜别克族', label: '乌孜别克族' },
  { value: '俄罗斯族', label: '俄罗斯族' },
  { value: '鄂温克族', label: '鄂温克族' },
  { value: '德昂族', label: '德昂族' },
  { value: '保安族', label: '保安族' },
  { value: '裕固族', label: '裕固族' },
  { value: '京族', label: '京族' },
  { value: '塔塔尔族', label: '塔塔尔族' },
  { value: '独龙族', label: '独龙族' },
  { value: '鄂伦春族', label: '鄂伦春族' },
  { value: '赫哲族', label: '赫哲族' },
  { value: '门巴族', label: '门巴族' },
  { value: '珞巴族', label: '珞巴族' },
  { value: '基诺族', label: '基诺族' }
];

// 政治面貌选项
const politicalStatusOptions = [
  { value: '中共党员', label: '中共党员' },
  { value: '中共预备党员', label: '中共预备党员' },
  { value: '共青团员', label: '共青团员' },
  { value: '民革党员', label: '民革党员' },
  { value: '民盟盟员', label: '民盟盟员' },
  { value: '民建会员', label: '民建会员' },
  { value: '民进会员', label: '民进会员' },
  { value: '农工党党员', label: '农工党党员' },
  { value: '致公党党员', label: '致公党党员' },
  { value: '九三学社社员', label: '九三学社社员' },
  { value: '无党派人士', label: '无党派人士' },
  { value: '群众', label: '群众' }
];

const form = reactive({
  id: undefined,
  resumeTitle: undefined,
  name: undefined,
  gender: undefined,
  birthday: undefined,
  phone: undefined,
  email: undefined,
  location: undefined,
  // 新增字段
  nation: undefined,
  nativePlace: undefined,
  politicalStatus: undefined,
  technicalPosition: undefined,
  currentPosition: undefined,
  // 关联数据
  education: [],
  work: [],
  project: [],
  skills: [],
  avatar: undefined // 新增头像字段
});

const rules = {
  resumeTitle: [{ required: true, message: "简历标题不能为空", trigger: "blur" }],
  name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
  phone: [{ required: true, message: "联系电话不能为空", trigger: "blur" }],
  email: [{ required: true, message: "电子邮箱不能为空", trigger: "blur" }]
};

// 遮罩层
// const loading = ref(true);  // Remove this duplicate declaration
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  resumeTitle: undefined,
  name: undefined,
  phone: undefined
});

/** 查询简历列表 */
const getList = async () => {
  loading.value = true;
  try {
    const response = await listResume(queryParams);
    resumeList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    console.error("获取简历列表失败:", error);
  }
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.resumeTitle = undefined;
  queryParams.name = undefined;
  queryParams.phone = undefined;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 新增简历
const handleAdd = () => {
  reset();
  open.value = true;
  title.value = "添加简历";
};

// 编辑简历
const handleEdit = async (row) => {
  reset();
  const id = row.id;
  const response = await getResume(id);
  Object.assign(form, response.data);
  // 确保ID被正确设置
  form.id = id;
  open.value = true;
  title.value = "修改简历";
};

// 查看简历
const handleView = (row) => {
  const id = row.id || row;
  router.push({ path: '/talent/resume/view', query: { id } });
};

// 删除简历
const handleDelete = (row) => {
  ElMessageBox.confirm('是否确认删除该简历?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    await delResume(row.id);
    getList();
    ElMessage.success("删除成功");
  });
};

// 下载简历
const handleDownload = async (row) => {
  try {
    const token = getToken();

    if (!token) {
      ElMessage.error('请先登录');
      return;
    }

    const response = await fetch(`/dev-api/talent/resume/downloadPdf/${row.id}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });

    if (!response.ok) {
      if (response.status === 403) {
        ElMessage.error('没有权限下载此简历');
      } else if (response.status === 404) {
        ElMessage.error('简历不存在');
      } else {
        ElMessage.error('下载失败');
      }
      return;
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = (row.name || '简历') + '_' + row.id + '.pdf';
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    window.URL.revokeObjectURL(url);

    ElMessage.success("简历下载成功");
  } catch (error) {
    console.error('下载简历失败:', error);
    ElMessage.error('下载简历失败');
  }
};

// 设置默认简历
const handleSetDefault = async (row) => {
  try {
    await setDefaultResume(row.id);
    getList();
    ElMessage.success("设置默认简历成功");
  } catch (error) {
    console.error("设置默认简历失败:", error);
  }
};

// 表单重置
const reset = () => {
  form.id = undefined;
  form.resumeTitle = undefined;
  form.name = undefined;
  form.gender = undefined;
  form.birthday = undefined;
  form.phone = undefined;
  form.email = undefined;
  form.location = undefined;
  form.nation = undefined;
  form.nativePlace = undefined;
  form.politicalStatus = undefined;
  form.technicalPosition = undefined;
  form.currentPosition = undefined;
  form.education = [];
  form.work = [];
  form.project = [];
  form.skills = [];
  form.avatar = undefined; // 重置头像
};

// 取消按钮
const cancel = () => {
  open.value = false;
  reset();
};

// 提交表单
const submitForm = async () => {
  try {
    console.log("提交表单，form.id:", form.id);
    console.log("表单数据:", form);

    if (form.id) {
      console.log("执行更新操作");
      await updateResume(form);
    } else {
      console.log("执行新增操作");
      await addResume(form);
    }
    open.value = false;
    getList();
    ElMessage.success("操作成功");
  } catch (error) {
    console.error("提交表单失败:", error);
    ElMessage.error("操作失败: " + (error.message || error));
  }
};

// 添加教育经历
const addEducation = () => {
  form.education.push({
    school: '',
    major: '',
    degree: '',
    degreeType: '',
    startDate: null,
    endDate: null,
    description: ''
  });
};

// 添加工作经历
const addWork = () => {
  form.work.push({
    company: '',
    position: '',
    department: '',
    salary: '',
    startDate: null,
    endDate: null,
    description: '',
    achievement: ''
  });
};

// 添加项目经历
const addProject = () => {
  form.project.push({
    name: '',
    role: '',
    startDate: null,
    endDate: null,
    description: '',
    achievement: ''
  });
};

// 添加技能特长
const addSkill = () => {
  form.skills.push({
    name: '',
    level: 3,
    description: ''
  });
};

// 删除教育经历
const removeEducation = (index) => {
  form.education.splice(index, 1);
};

// 删除工作经历
const removeWork = (index) => {
  form.work.splice(index, 1);
};

// 删除项目经历
const removeProject = (index) => {
  form.project.splice(index, 1);
};

// 删除技能特长
const removeSkill = (index) => {
  form.skills.splice(index, 1);
};

// 头像上传成功
const handleAvatarSuccess = (response) => {
  if (response.code === 200) {
    form.avatar = response.data.url;
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
};

// 上传前校验
const beforeAvatarUpload = (rawFile) => {
  const isJPGPNG = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png';
  const isLt2M = rawFile.size / 1024 / 1024 < 2;

  if (!isJPGPNG) {
    ElMessage.error('图片只能是 JPG/PNG 格式!');
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!');
  }
  return isJPGPNG && isLt2M;
};

// 获取上传图片的headers
const headers = {
  Authorization: 'Bearer ' + localStorage.getItem('token')
};

// 上传图片的URL
const uploadImgUrl = import.meta.env.VITE_APP_BASE_API + '/file/upload';

onMounted(() => {
  getList();
});
</script>

<style scoped>
.resume-card {
  margin-bottom: 20px;
}

.resume-header {
  padding: 20px;
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
}

.resume-title {
  margin-left: 20px;
}

.resume-title h3 {
  margin: 0;
  margin-bottom: 5px;
}

.resume-body {
  padding: 20px;
}

.info-item {
  margin-bottom: 10px;
}

.info-item span {
  font-weight: bold;
  margin-right: 10px;
}

.resume-footer {
  padding: 15px;
  text-align: center;
  border-top: 1px solid #ebeef5;
}

.education-item,
.work-item,
.project-item,
.skill-item {
  border: 1px solid #ebeef5;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
  background-color: #ffffff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  transition: box-shadow 0.3s;
}

.education-item:hover,
.work-item:hover,
.project-item:hover,
.skill-item:hover {
  box-shadow: 0 2px 8px rgba(0, 21, 41, 0.12);
}

.el-divider {
  margin: 32px 0;
  font-weight: bold;
  color: #303133;
}

.el-table .el-progress {
  width: 90%;
  margin: 0 auto;
}

.avatar-uploader .el-upload {
  border: none;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 100%;
  height: 100%;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.text-center {
  text-align: center;
}

.avatar-uploader {
  width: 100%;
  height: 100%;
}

/* 编辑对话框样式 - 与view.vue保持一致 */
.form-title {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 2px solid #409eff;
}

.form-title h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: bold;
}

.basic-info-container {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.avatar-container {
  flex: 0 0 150px;
  height: 150px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
  text-align: center;
}

.avatar-placeholder .avatar-uploader-icon {
  font-size: 24px;
  margin-bottom: 8px;
  width: auto;
  height: auto;
  line-height: 1;
}

.info-container {
  flex: 1;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin: 30px 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.timeline-container {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  background-color: #fafafa;
}

.timeline-item-edit {
  background-color: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  position: relative;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.timeline-index {
  background-color: #409eff;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.delete-btn {
  padding: 5px 10px;
  font-size: 12px;
}

.add-item-container {
  text-align: center;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .basic-info-container {
    flex-direction: column;
    align-items: center;
  }

  .avatar-container {
    margin-bottom: 20px;
  }
}

.remove-button {
  float: right;
}

.el-form-item {
  margin-bottom: 24px;
}

.dialog-footer {
  text-align: center;
  padding: 20px;
  border-top: 1px solid #ebeef5;
  background-color: #ffffff;
}
</style>
