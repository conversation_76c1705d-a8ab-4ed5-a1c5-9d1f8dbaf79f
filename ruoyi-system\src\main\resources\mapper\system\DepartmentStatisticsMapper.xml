<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DepartmentStatisticsMapper">
    <select id="selectLatestDepartmentStatistics" resultType="DepartmentStatistics">
        SELECT
        d.id,
        d.department_name as departmentName,
        d.dept_count as deptCount,
        d.dept_activated_count as deptActivatedCount,
        d.activation_rate as activationRate,
        d.statistics_date as statisticsDate,
        d.activated_new as activatedNew,
        d.total_new as totalNew,
        d.created_at as createTime,
        d.updated_at as updatedTime
        FROM department_statistics d
        INNER JOIN (
        SELECT MAX(statistics_date) as latest_date
        FROM department_statistics
        ) latest ON d.statistics_date = latest.latest_date
        ORDER BY d.dept_count DESC
    </select>

    <select id="selectDepartmentStatisticsByDate" parameterType="java.util.Date" resultType="DepartmentStatistics">
        SELECT
            id,
            department_name as departmentName,
            dept_count as deptCount,
            dept_activated_count as deptActivatedCount,
            activation_rate as activationRate,
            statistics_date as statisticsDate,
            activated_new as activatedNew,
            total_new as totalNew,
            created_at as createTime,
            updated_at as updatedTime
        FROM department_statistics
        WHERE statistics_date = #{statisticsDate}
        ORDER BY dept_count DESC
    </select>
</mapper>