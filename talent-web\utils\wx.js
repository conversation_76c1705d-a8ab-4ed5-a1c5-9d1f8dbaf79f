import { createHttp } from './request';
import config from '../config';
// 创建默认的 HTTP 客户端实例
const defaultHttp = createHttp('request');

// 微信企业授权登录
export const getWxAuthorize = async(code) => {
    try {
        // 确保传入的参数是简单的键值对
        const params = {
            code: code, // 确保 code 是字符串
            agentId: config.wx.agentid
        };

        const res = await defaultHttp.get('enterpriseLogin', params);
        console.log("res", res)
        return {
            code: res.code,
            data: res.token,
            message: res.msg
        };
    } catch (error) {
        console.error('企业微信授权失败:', error);
        return {
            code: 500,
            message: error.message || '授权失败'
        };
    }
};

// 获取用户信息
export const getUserInfo = async(token) => {
    try {
        const res = await defaultHttp.get('getInfo', null, {
            'Authorization': token
        });
        return {
            code: res.code,
            data: res.user,
            message: res.msg
        };
    } catch (error) {
        console.error('获取用户信息失败:', error);
        return {
            code: 500,
            message: error.message || '获取用户信息失败'
        };
    }
};

// 获取URL中的code参数
export const getUrlCode = (name) => {
    return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1]
        .replace(/\+/g, '%20')) || null;
};