<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JobMapper">

    <resultMap type="Job" id="JobResult">
        <id     property="id"           column="id"           />
        <result property="companyId"    column="company_id"   />
        <result property="title"        column="title"        />
        <result property="salary"       column="salary"       />
        <result property="company"      column="company"      />
        <result property="companyName"  column="company_name" />
        <result property="companyLogo"  column="company_logo" />
        <result property="companyDesc"  column="company_desc" />
        <result property="location"     column="location"     />
        <result property="experience"   column="experience"   />
        <result property="education"    column="education"    />
        <result property="description"  column="description"  />
        <result property="requirements" column="requirements" />
        <result property="views"        column="views"        />
        <result property="applications" column="applications" />
        <result property="tags"         column="tags"         />
        <result property="status"       column="status"       />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="department"   column="department"   />
        <result property="other"        column="other"        />
        <result property="headcount"    column="headcount"    />
        <result property="targetRange"  column="target_range" />
        <result property="demandType"   column="demand_type"  />
    </resultMap>

    <select id="countTotalJobs" resultType="int">
        select count(*) 
        from talent_job 
        where status = '0'
    </select>
    
    <select id="countTodayNewJobs" resultType="int">
        select count(*) 
        from talent_job 
        where status = '0' 
        and date(create_time) = curdate()
    </select>
    
    <select id="countTodayApplications" resultType="int">
        select sum(applications) 
        from talent_job 
        where status = '0' 
        and date(update_time) = curdate()
    </select>
    
    <select id="countTotalViews" resultType="Integer">
        select COALESCE(sum(COALESCE(views, 0)), 0) from talent_job
    </select>

    <select id="selectJobList" parameterType="Job" resultMap="JobResult">
        SELECT j.id, j.title, j.salary, j.company_id, c.name as company, j.company_name, j.location, 
               j.experience, j.education, j.description, j.requirements, 
               j.tags, j.status, j.department, j.headcount, j.demand_type, 
               j.target_range, j.other, j.create_time, j.update_time, j.applications, j.views
        FROM talent_job j
        LEFT JOIN company c ON j.company_id = c.id
        <where>
            AND j.status != '1'
            <if test="companyId != null">
                AND j.company_id = #{companyId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (j.title like concat('%', #{keyword}, '%') OR j.company_name like concat('%', #{keyword}, '%'))
            </if>
            <if test="status != null and status != ''">
                AND j.status = #{status}
            </if>
        </where>
        ORDER BY j.create_time DESC
    </select>

    <select id="selectJobById" parameterType="Long" resultMap="JobResult">
        SELECT j.*, c.name as company
        FROM talent_job j
        LEFT JOIN company c ON j.company_id = c.id
        WHERE j.id = #{id}
    </select>

    <insert id="insertJob" parameterType="Job" useGeneratedKeys="true" keyProperty="id">
        insert into talent_job (
            company_id, title, salary, company, company_name, company_logo, company_desc,
            location, experience, education, description, requirements,
            views, applications, tags, status, create_by, create_time,
            department, other,
            headcount, target_range, demand_type
        ) values (
                     #{companyId}, #{title}, #{salary}, #{company}, #{companyName}, #{companyLogo}, #{companyDesc},
                     #{location}, #{experience}, #{education}, #{description}, #{requirements},
                     #{views}, #{applications}, #{tags}, #{status}, #{createBy}, sysdate(),
                     #{department}, #{other},
                     #{headcount}, #{targetRange}, #{demandType}
                 )
    </insert>

    <update id="updateJob" parameterType="Job">
        update talent_job
        <set>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="salary != null">salary = #{salary},</if>
            <if test="company != null">company = #{company},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companyLogo != null">company_logo = #{companyLogo},</if>
            <if test="companyDesc != null">company_desc = #{companyDesc},</if>
            <if test="location != null">location = #{location},</if>
            <if test="experience != null">experience = #{experience},</if>
            <if test="education != null">education = #{education},</if>
            <if test="description != null">description = #{description},</if>
            <if test="requirements != null">requirements = #{requirements},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="status != null">status = #{status},</if>
            <if test="headcount != null">headcount = #{headcount},</if>
            <if test="targetRange != null">target_range = #{targetRange},</if>
            <if test="demandType != null">demand_type = #{demandType},</if>
            update_by = #{updateBy},
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <update id="updateJobViews" parameterType="Long">
        update talent_job 
        set views = COALESCE(views, 0) + 1,
            update_time = sysdate()
        where id = #{id}
    </update>

    <update id="updateJobApplications" parameterType="Long">
        update talent_job 
        set applications = COALESCE(applications, 0) + 1,
            update_time = sysdate()
        where id = #{id}
    </update>

    <update id="deleteJobById" parameterType="Long">
        update talent_job set status = '1', update_time = sysdate() where id = #{id}
    </update>

    <update id="deleteJobByIds" parameterType="Long">
        update talent_job set status = '1', update_time = sysdate() where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <sql id="selectJobVo">
        select id, company_id, title, salary, company, company_name, company_logo, company_desc, location, experience, education, description, requirements, views, applications, tags, status, create_by, create_time, update_by, update_time, department, other, headcount, target_range, demand_type
        from talent_job
    </sql>

    <!-- 查询职位投递情况 -->
    <select id="selectDeliveryList" parameterType="Long" resultType="java.util.Map">
        SELECT 
            d.id,
            d.job_id as jobId,
            d.user_id as userId,
            u.user_name as userName,
            d.resume_id as resumeId,
            r.name as resumeName,
            d.status,
            d.feedback,
            d.delivery_time as createTime
        FROM resume_delivery d
        LEFT JOIN sys_user u ON d.user_id = u.user_id
        LEFT JOIN resume r ON d.resume_id = r.id
        WHERE d.job_id = #{jobId}
        ORDER BY d.delivery_time DESC
    </select>
    <!-- 查询职位投递数量 -->
    <select id="selectDeliveryCount" parameterType="Long" resultType="Integer">
        SELECT COUNT(1)
        FROM resume_delivery
        WHERE job_id = #{jobId}
    </select>

    <select id="selectJobByTitleAndCompany" resultMap="JobResult">
        <include refid="selectJobVo"/>
        where title = #{param1} and company = #{param2}
        limit 1
    </select>

    <select id="selectRecentJobs" parameterType="int" resultType="java.util.Map">
        SELECT
            j.id,
            j.title,
            COALESCE(j.company_name, j.company) as company,
            j.location,
            j.salary,
            j.create_time,
            COALESCE(j.views, 0) as views,
            COALESCE(j.applications, 0) as applications,
            DATEDIFF(NOW(), j.create_time) as days_ago
        FROM talent_job j
        WHERE j.status = '0'
        ORDER BY j.create_time DESC
        LIMIT #{limit}
    </select>

    <select id="selectHotJobs" parameterType="int" resultType="java.util.Map">
        SELECT
            j.id,
            j.title,
            COALESCE(j.company_name, j.company) as company,
            j.location,
            j.salary,
            j.create_time,
            COALESCE(j.views, 0) as views,
            COALESCE(j.applications, 0) as applications
        FROM talent_job j
        WHERE j.status = '0'
        ORDER BY COALESCE(j.views, 0) DESC, COALESCE(j.applications, 0) DESC, j.create_time DESC
        LIMIT #{limit}
    </select>
</mapper>