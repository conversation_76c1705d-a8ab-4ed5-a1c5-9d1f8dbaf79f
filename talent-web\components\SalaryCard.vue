<template>
  <div class="salary-card">
    <h3>{{ title }}</h3>
    <p>{{ value }}</p>
  </div>
</template>

<script setup>
const props = defineProps({
  title: String,
  value: String
});
</script>

<style scoped>
.salary-card {
  background-color: #f0f4f8;
  border-radius: 10px;
  padding: 20px;
  width: calc(25% - 20px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  font-size: 1.2em;
}

h3 {
  margin-bottom: 10px;
  color: #34495e;
}

p {
  font-weight: bold;
  color: #2c3e50;
}

@media (max-width: 768px) {
  .salary-card {
    width: calc(50% - 20px);
  }
}

@media (max-width: 480px) {
  .salary-card {
    width: 100%;
  }
}
</style>
