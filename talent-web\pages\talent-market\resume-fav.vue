<template>
  <view class="favorites-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="back-btn" @click="goBack">
        <text class="yzb yzb-fanhui"></text>
      </view>
      <view class="nav-title">职位收藏</view>
    </view>

    <!-- 列表区 -->
    <view v-if="favorites.length > 0" class="favorites-list">
      <view class="job-card" v-for="item in favorites" :key="item.id">
        <view class="card-header">
          <view class="job-title">{{ item.jobTitle }}</view>
          <view class="salary"></view>
        </view>
        <view class="company">{{ item.companyName }}</view>
        <view class="tags">
          <text class="tag" v-for="(tag, index) in item.tags" :key="index">{{ tag }}</text>
        </view>
        <view class="actions">
          <view class="action-time">收藏于 {{ item.favoriteTime }}</view>
          <view class="action-btns">
            <view class="btn btn-apply" @click="applyJob(item.jobId)">申请职位</view>
            <view class="btn btn-cancel" @click="handleCancelFavorite(item.id)">取消收藏</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 空状态 -->
    <view v-else class="empty">
      <image src="/static/empty.png" class="empty-img" />
      <view class="empty-text">暂无收藏职位</view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getFavoriteJobs, listFavorite, cancelFavorite as cancelFavoriteApi } from '@/utils/talent-market' // 只导入存在的导出

const favorites = ref([])
const page = ref(1)
const pageSize = ref(10)

// 获取收藏列表
const fetchFavorites = async () => {
  try {
    const token = uni.getStorageSync('token')
    const result = await listFavorite(token, {
      pageNum: page.value,
      pageSize: pageSize.value
    })

    if (result.code === 200) {
      favorites.value = result.rows.map(item => ({
        ...item,
        favoriteTime: item.createTime,
        tags: [item.experience || '经验不限', item.education || '学历不限', item.jobType || '全职']
      }))
    } else {
      throw new Error(result.msg || '获取收藏列表失败')
    }
  } catch (error) {
    console.error('获取收藏列表失败:', error)
    uni.showToast({
      title: '获取收藏列表失败',
      icon: 'none'
    })
  }
}

onMounted(() => {
  fetchFavorites()
})

const goBack = () => {
  uni.navigateBack()
}

// 申请职位
const applyJob = (jobId) => {
  uni.navigateTo({
    url: `/pages/talent-market/detail?id=${jobId}`,
    success: () => {
      console.log('跳转到职位详情页成功')
    },
    fail: (err) => {
      console.error('跳转失败:', err)
      uni.showToast({
        title: '跳转失败，请重试',
        icon: 'none'
      })
    }
  })
}

// 取消收藏
const handleCancelFavorite = async (id) => {
  try {
    uni.showModal({
      title: '提示',
      content: '确定取消收藏该职位吗？',
      success: async (res) => {
        if (res.confirm) {
          const token = uni.getStorageSync('token')
          const result = await cancelFavoriteApi(token, id)
          
          if (result.code === 200) {
            favorites.value = favorites.value.filter(item => item.id !== id)
            uni.showToast({
              title: '取消收藏成功',
              icon: 'success'
            })
          } else {
            throw new Error(result.msg || '取消收藏失败')
          }
        }
      }
    })
  } catch (error) {
    console.error('取消收藏失败:', error)
    uni.showToast({
      title: '取消收藏失败',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss">
.favorites-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 40rpx;
}
.nav-bar {
  height: 100rpx;
  display: flex;
  align-items: center;
  background: #2186f7;
  color: #fff;
  padding: 0 24rpx;
  position: sticky;
  top: 0;
  z-index: 10;
}
.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}
.nav-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
}
.favorites-list {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.job-card {
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(33,134,247,0.06);
  padding: 28rpx 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.job-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #222;
}
.salary {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: 600;
}
.company {
  font-size: 26rpx;
  color: #666;
}
.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.tag {
  font-size: 22rpx;
  padding: 4rpx 16rpx;
  background: #f2f6fc;
  color: #67c23a;
  border-radius: 6rpx;
}
.actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
  padding-top: 16rpx;
  border-top: 1px solid #f0f0f0;
}
.action-time {
  font-size: 22rpx;
  color: #aaa;
}
.action-btns {
  display: flex;
  gap: 16rpx;
}
.btn {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  text-align: center;
}
.btn-apply {
  background: #2186f7;
  color: #fff;
}
.btn-cancel {
  background: #f5f5f5;
  color: #666;
}
.empty {
  margin-top: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  .empty-img {
    width: 240rpx;
    height: 180rpx;
    margin-bottom: 24rpx;
  }
  .empty-text {
    color: #bbb;
    font-size: 28rpx;
  }
}
</style>