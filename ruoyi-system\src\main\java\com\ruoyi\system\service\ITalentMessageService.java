package com.ruoyi.system.service;

import com.ruoyi.system.domain.TalentMessage;
import java.util.List;

public interface ITalentMessageService {
    List<TalentMessage> selectMessageList(TalentMessage message);
    
    int selectUnreadCount(Long userId);
    
    int updateMessageRead(String messageId);
    
    int updateAllMessageRead(Long userId);
    
    void sendMessage(TalentMessage message);
} 