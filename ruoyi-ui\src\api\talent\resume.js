import request from '@/utils/request'

// 查询简历列表
export function listResume(query) {
    return request({
        url: '/talent/resume/list',
        method: 'get',
        params: query
    })
}

// 查询简历详细
export function getResume(id) {
    return request({
        url: '/talent/resume/' + id,
        method: 'get'
    })
}

// 新增简历
export function addResume(data) {
    return request({
        url: '/talent/resume',
        method: 'post',
        data: data
    })
}

// 修改简历
export function updateResume(data) {
    return request({
        url: '/talent/resume/' + data.id,
        method: 'put',
        data: data
    })
}

// 删除简历
export function delResume(id) {
    return request({
        url: '/talent/resume/' + id,
        method: 'delete'
    })
}

// 设置默认简历
export function setDefaultResume(id) {
    return request({
        url: '/talent/resume/' + id + '/default',
        method: 'put'
    })
}

// 获取简历家庭成员信息
export function getResumeFamily(id) {
    return request({
        url: '/talent/resume/' + id + '/family',
        method: 'get'
    })
}

// 获取简历奖惩情况
export function getResumeAwards(id) {
    return request({
        url: '/talent/resume/' + id + '/awards',
        method: 'get'
    })
}

// 获取简历现任职务信息
export function getResumePosition(id) {
    return request({
        url: '/talent/resume/' + id + '/position',
        method: 'get'
    })
}

// 更新简历家庭成员信息
export function updateResumeFamily(id, data) {
    return request({
        url: '/talent/resume/' + id + '/family',
        method: 'put',
        data: data
    })
}

// 更新简历奖惩情况
export function updateResumeAwards(id, data) {
    return request({
        url: '/talent/resume/' + id + '/awards',
        method: 'put',
        data: data
    })
}

// 更新简历现任职务信息
export function updateResumePosition(id, data) {
    return request({
        url: '/talent/resume/' + id + '/position',
        method: 'put',
        data: data
    })
}

// 上传简历附件
export function uploadAttachment(data) {
    return request({
        url: '/talent/resume/upload',
        method: 'post',
        data: data
    })
}

// 上传头像
export function uploadAvatar(data) {
    return request({
        url: '/talent/resume/avatar',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data: data
    })
}