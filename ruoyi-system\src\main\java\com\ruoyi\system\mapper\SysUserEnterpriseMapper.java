package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.SysUserEnterprise;
import io.lettuce.core.dynamic.annotation.Param;

public interface SysUserEnterpriseMapper {
    // ...其他方法...

    /**
     * 根据企业用户ID和企业ID查询用户企业关联信息
     *
     * @param enterpriseUserId 企业用户ID
     * @param enterpriseCorpId 企业ID
     * @return 用户企业关联信息
     */
    SysUserEnterprise selectByEnterpriseUserIdAndCorpId(@Param("enterpriseUserId") String enterpriseUserId, @Param("enterpriseCorpId") String enterpriseCorpId);
}