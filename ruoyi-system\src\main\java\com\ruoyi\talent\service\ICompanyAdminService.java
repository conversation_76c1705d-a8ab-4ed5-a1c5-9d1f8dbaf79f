package com.ruoyi.talent.service;

import java.util.List;
import com.ruoyi.talent.domain.CompanyAdmin;

/**
 * 企业管理员关联Service接口
 * 
 * <AUTHOR>
 */
public interface ICompanyAdminService 
{
    /**
     * 查询企业的管理员列表
     * 
     * @param companyId 企业ID
     * @return 管理员列表
     */
    public List<CompanyAdmin> selectCompanyAdminList(Long companyId);

    /**
     * 批量新增企业管理员关联
     * 
     * @param companyId 企业ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int insertCompanyAdmins(Long companyId, Long[] userIds);

    /**
     * 删除企业管理员关联
     * 
     * @param companyId 企业ID
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteCompanyAdmin(Long companyId, Long userId);

    /**
     * 检查用户是否是企业管理员
     * 
     * @param companyId 企业ID
     * @param userId 用户ID
     * @return 结果
     */
    public boolean checkIsCompanyAdmin(Long companyId, Long userId);

    /**
     * 获取用户管理的企业ID
     * 
     * @param userId 用户ID
     * @return 企业ID，如果不是企业管理员则返回null
     */
    public Long getAdminCompanyId(Long userId);
} 