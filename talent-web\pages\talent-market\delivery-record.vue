<template>
  <view class="delivery-record-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="back-btn" @click="goBack">
        <text class="yzb yzb-fanhui"></text>
      </view>
      <view class="nav-title">投递记录</view>
    </view>

    <!-- 列表区 -->
    <view v-if="records.length > 0" class="record-list">
      <view class="record-card" v-for="item in records" :key="item.id">
        <view class="card-header">
          <view class="job-title">{{ item.jobTitle }}</view>
          <view class="status-tag" :class="statusClass(item.status)">{{ statusText(item.status) }}</view>
        </view>
        <view class="company">{{ item.companyName }}</view>
        <view class="meta">
          <text class="time">{{ item.deliveryTime }}</text>
        </view>
      </view>
    </view>
    <!-- 空状态 -->
    <view v-else class="empty">
      <image src="/static/empty.png" class="empty-img" />
      <view class="empty-text">暂无投递记录</view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getDeliveryList } from '@/utils/talent-market'

const records = ref([])
const page = ref(1)
const pageSize = ref(10)

// 获取投递记录列表
const fetchRecords = async () => {
  try {
    const token = uni.getStorageSync('token')
    
    const result = await getDeliveryList(token, {
      page: page.value,
      pageSize: pageSize.value
    })

    if (result.code === 200) {
      records.value = result.rows || []
    } else {
      uni.showToast({
        title: result.msg || '获取数据失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取投递记录失败:', error)
    uni.showToast({
      title: '获取投递记录失败',
      icon: 'none'
    })
  }
}

onMounted(() => {
  fetchRecords()
})

const goBack = () => {
  uni.navigateBack()
}

const statusText = (status) => {
  switch (status) {
    case '0': return '待处理'
    case '1': return '已查看'
    case '2': return '邀请面试'
    case '3': return '不合适'
    case '4': return '已录用'
    case '-1': return '已取消'
    default: return '未知'
  }
}

const statusClass = (status) => {
  const classes = {
    '0': 'tag-pending',
    '1': 'tag-reviewing',
    '2': 'tag-interview',
    '3': 'tag-rejected',
    '4': 'tag-accepted',
    '-1': 'tag-cancelled'
  }
  return classes[status] || 'tag-pending'
}
</script>

<style lang="scss">
.delivery-record-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 40rpx;
}
.nav-bar {
  height: 100rpx;
  display: flex;
  align-items: center;
  background: #2186f7;
  color: #fff;
  padding: 0 24rpx;
  position: sticky;
  top: 0;
  z-index: 10;
}
.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}
.nav-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
}
.record-list {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.record-card {
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(33,134,247,0.06);
  padding: 28rpx 24rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.job-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #222;
}
.status-tag {
  font-size: 24rpx;
  padding: 4rpx 18rpx;
  border-radius: 16rpx;
  font-weight: 500;
}
.tag-pending {
  background: #fffbe6;
  color: #faad14;
}
.tag-reviewing {
  background: #e6f7ff;
  color: #1890ff;
}
.tag-interview {
  background: #fcf4e9;
  color: #fa8c16;
}
.tag-accepted {
  background: #e6fffb;
  color: #12ae85;
}
.tag-rejected {
  background: #fff1f0;
  color: #f56c6c;
}
.tag-cancelled {
  background: #f5f5f5;
  color: #909399;
}
.company {
  font-size: 26rpx;
  color: #666;
}
.meta {
  font-size: 22rpx;
  color: #aaa;
}
.empty {
  margin-top: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  .empty-img {
    width: 240rpx;
    height: 180rpx;
    margin-bottom: 24rpx;
  }
  .empty-text {
    color: #bbb;
    font-size: 28rpx;
  }
}
</style> 