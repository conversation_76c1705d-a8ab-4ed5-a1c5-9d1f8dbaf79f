package com.ruoyi.talent.service.impl;

import com.ruoyi.talent.domain.JobFavorite;
import com.ruoyi.talent.mapper.JobFavoriteMapper;
import com.ruoyi.talent.service.IJobFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 职位收藏Service实现
 */
@Service
public class JobFavoriteServiceImpl implements IJobFavoriteService {
    @Autowired
    private JobFavoriteMapper jobFavoriteMapper;

    @Override
    public List<JobFavorite> selectJobFavoriteList(JobFavorite jobFavorite) {
        return jobFavoriteMapper.selectJobFavoriteList(jobFavorite);
    }

    @Override
    public int insertJobFavorite(JobFavorite jobFavorite) {
        return jobFavoriteMapper.insertJobFavorite(jobFavorite);
    }

    @Override
    public int deleteJobFavorite(Long id) {
        return jobFavoriteMapper.deleteJobFavorite(id);
    }

    @Override
    public int deleteJobFavoriteByIds(Long[] ids) {
        return jobFavoriteMapper.deleteJobFavoriteByIds(ids);
    }

    @Override
    public boolean checkIsFavorite(Long userId, Long jobId) {
        return jobFavoriteMapper.checkIsFavorite(userId, jobId) != null;
    }

    @Override
    public boolean toggleFavorite(Long userId, Long jobId) {
        JobFavorite favorite = jobFavoriteMapper.checkIsFavorite(userId, jobId);
        if (favorite != null) {
            // 如果已收藏，则取消收藏
            jobFavoriteMapper.deleteJobFavorite(favorite.getId());
            return false;
        } else {
            // 如果未收藏，则添加收藏
            JobFavorite newFavorite = new JobFavorite();
            newFavorite.setUserId(userId);
            newFavorite.setJobId(jobId);
            jobFavoriteMapper.insertJobFavorite(newFavorite);
            return true;
        }
    }
} 