import request from '@/utils/request'

// 查询收藏列表
export function listFavorite(query) {
    return request({
        url: '/talent/favorite/list',
        method: 'get',
        params: query
    })
}

// 检查是否已收藏
export function checkFavorite(jobId) {
    return request({
        url: `/talent/favorite/check/${jobId}`,
        method: 'get'
    })
}

// 切换收藏状态
export function toggleFavorite(jobId) {
    return request({
        url: `/talent/favorite/toggle/${jobId}`,
        method: 'post'
    })
}

// 取消收藏
export function cancelFavorite(ids) {
    return request({
        url: `/talent/favorite/${ids}`,
        method: 'delete'
    })
}