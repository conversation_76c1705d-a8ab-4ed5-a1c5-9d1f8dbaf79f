<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ResumeAwardsMapper">
    
    <resultMap type="ResumeAwards" id="AwardsResult">
        <id     property="id"          column="id"          />
        <result property="resumeId"    column="resume_id"   />
        <result property="awards"      column="awards"      />
        <result property="annualReview" column="annual_review" />
        <result property="appointmentReason" column="appointment_reason" />
        <result property="createTime"  column="created_at"  />
        <result property="updateTime"  column="updated_at"  />
    </resultMap>

    <select id="selectByResumeId" parameterType="String" resultMap="AwardsResult">
        select * from resume_awards 
        where resume_id = #{resumeId}
        limit 1
    </select>

    <insert id="insert" parameterType="ResumeAwards">
        insert into resume_awards (
            id, resume_id, awards, annual_review, appointment_reason, created_at
        ) values (
            #{id}, #{resumeId}, #{awards}, #{annualReview}, #{appointmentReason}, sysdate()
        )
    </insert>

    <update id="update" parameterType="ResumeAwards">
        update resume_awards
        <set>
            <if test="awards != null">awards = #{awards},</if>
            <if test="annualReview != null">annual_review = #{annualReview},</if>
            <if test="appointmentReason != null">appointment_reason = #{appointmentReason},</if>
            updated_at = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="String">
        delete from resume_awards where id = #{id}
    </delete>

    <delete id="deleteByResumeId" parameterType="String">
        delete from resume_awards where resume_id = #{resumeId}
    </delete>
</mapper> 