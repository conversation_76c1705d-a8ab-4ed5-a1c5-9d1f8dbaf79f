package com.ruoyi.system.domain.dto;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = true)
public class JobImportDTO extends BaseEntity {
    
    @Excel(name = "序号")
    private Integer serialNumber;

    @Excel(name = "单位", cellType = Excel.ColumnType.NUMERIC)
    private String company;
    
    @Excel(name = "公司名称")
    private String companyName;

    @Excel(name = "部门")
    private String department;

    @Excel(name = "岗位", cellType = Excel.ColumnType.NUMERIC)
    private String title;

    @Excel(name = "工作地点")
    private String location;

    @Excel(name = "所需人数")
    private Integer headcount;

    @Excel(name = "招聘性质")
    private String recruitmentType;

    @Excel(name = "需求形式")
    private String demandType;

    @Excel(name = "工作内容描述")
    private String description;

    @Excel(name = "招聘条件要求")
    private String requirements;

    @Excel(name = "其他")
    private String other;

    @Excel(name = "目标范围")
    private String targetRange;

    public boolean isValid() {
        return StringUtils.isNotEmpty(company) 
            && StringUtils.isNotEmpty(title);
    }
} 