<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>安全管理驾驶舱</h1>
    </div>
    <div class="dashboard-content">
      <div v-for="company in companies" :key="company.name" class="company-section" :data-name="company.name">
        <h2>{{ company.name }}</h2>
        <div class="dashboard-cards">
          <div class="dashboard-card" v-for="(item, index) in company.items" :key="index" :class="getTrendClass(item.trend)">
            <div class="card-icon">
              <i :class="['fas', item.icon]"></i>
            </div>
            <div class="card-content">
              <h3>{{ item.title }}</h3>
              <p class="value">{{ item.value }}</p>
              <span class="trend-icon" :class="getTrendClass(item.trend)">
                <i :class="['fas', item.trend === 'up' ? 'fa-arrow-up' : 'fa-arrow-down']"></i>
              </span>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <div class="chart safetyEventsChart"></div>
          <div class="chart trendAnalysisChart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';

const companies = ref([
  {
    name: '钱江水利',
    items: [
      { title: '安全事件统计', value: '5 起', icon: 'fa-exclamation-triangle', trend: 'up' },
      { title: '风险评估', value: '2 高风险', icon: 'fa-chart-bar', trend: 'down' },
      { title: '安全培训进度', value: '80%', icon: 'fa-user-graduate', trend: 'up' },
      { title: '应急预案', value: '3 次演练', icon: 'fa-clipboard-list', trend: 'up' },
      { title: '安全检查', value: '15 次', icon: 'fa-search', trend: 'down' },
      { title: '安全设备状态', value: '98% 正常', icon: 'fa-shield-alt', trend: 'up' },
    ],
  },
  // 其他子公司数据同理添加...
  {
    name: '华东区域总部',
    items: [
      { title: '安全事件统计', value: '3 起', icon: 'fa-exclamation-triangle', trend: 'down' },
      { title: '风险评估', value: '1 高风险', icon: 'fa-chart-bar', trend: 'down' },
      { title: '安全培训进度', value: '90%', icon: 'fa-user-graduate', trend: 'up' },
      { title: '应急预案', value: '2 次演练', icon: 'fa-clipboard-list', trend: 'down' },
      { title: '安全检查', value: '10 次', icon: 'fa-search', trend: 'up' },
      { title: '安全设备状态', value: '97% 正常', icon: 'fa-shield-alt', trend: 'down' },
    ],
  },
  // 添加其他子公司信息...
]);

const getTrendClass = (trend) => {
  return trend === 'up' ? 'trend-up' : 'trend-down';
};

onMounted(() => {
  companies.value.forEach(company => {
    const safetyEventsChart = echarts.init(document.querySelector(`.company-section[data-name="${company.name}"] .safetyEventsChart`));
    const trendAnalysisChart = echarts.init(document.querySelector(`.company-section[data-name="${company.name}"] .trendAnalysisChart`));

    safetyEventsChart.setOption({
      title: { text: '安全事件统计' },
      tooltip: { trigger: 'item' },
      legend: { top: '5%' },
      series: [
        {
          name: '安全事件',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: { borderRadius: 10, borderColor: '#fff', borderWidth: 2 },
          label: { show: false, position: 'center' },
          emphasis: {
            label: { show: true, fontSize: '30', fontWeight: 'bold' }
          },
          labelLine: { show: false },
          data: [
            { value: 1048, name: '钱江水利' },
            { value: 735, name: '华东区域总部' },
            { value: 580, name: '华南区域总部' },
            { value: 484, name: '华北区域总部' },
            { value: 300, name: '山东区域总部' },
            { value: 200, name: '内蒙古天河水务' },
            { value: 154, name: '齐齐哈尔水务集团' },
            { value: 120, name: '其他' },
          ]
        }
      ]
    });

    trendAnalysisChart.setOption({
      title: { text: '事故趋势分析' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['趋势'] },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '趋势',
          type: 'line',
          stack: '总量',
          data: [120, 132, 101, 134, 90, 230, 210, 180, 165, 175, 155, 160]
        }
      ]
    });
  });
});
</script>

<style scoped>
.dashboard {
  font-family: 'Roboto', sans-serif;
  background-color: #f0f4f8;
  min-height: 100vh;
  padding: 10px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 15px;
  background-color: #3498db;
  color: white;
  padding: 10px;
  border-radius: 8px;
}

h1 {
  margin: 0;
  font-size: 18px;
}

.company-section {
  background-color: #ffffff;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.company-section h2 {
  text-align: center;
  margin-bottom: 10px;
  font-size: 16px;
  color: #2c3e50;
}

.dashboard-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 10px;
}

.dashboard-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 10px;
  width: calc(50% - 5px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-icon {
  background-color: #ecf0f1;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

.card-icon i {
  font-size: 18px;
  color: #34495e;
}

h3 {
  font-size: 12px;
  color: #7f8c8d;
  margin: 0 0 5px 0;
}

.value {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.trend-icon {
  font-size: 10px;
  display: inline-block;
  padding: 2px 4px;
  border-radius: 10px;
}

.trend-up {
  background-color: #e6f7ee;
  color: #27ae60;
}

.trend-down {
  background-color: #fdeaea;
  color: #e74c3c;
}

.chart-container {
  margin-top: 15px;
}

.chart {
  width: 100%;
  height: 200px;
  margin-bottom: 15px;
}

@media (min-width: 768px) {
  .dashboard {
    padding: 20px;
  }

  h1 {
    font-size: 24px;
  }

  .dashboard-card {
    width: calc(33.33% - 10px);
  }

  .chart {
    height: 300px;
  }
}
</style>