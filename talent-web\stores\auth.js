import { getUrlCode, getWxAuthorize, getUserInfo } from "@/utils/wx.js";

// 认证状态管理
class AuthStore {
    constructor() {
        this.token = null;
        this.userInfo = null;
        this.isInitialized = false;
        this.initializationPromise = null;
    }

    /**
     * 初始化认证状态
     * @returns {Promise<string>} 返回token
     */
    async initialize() {
        // 如果已经有初始化Promise在进行中，直接返回该Promise
        if (this.initializationPromise) return this.initializationPromise;

        // 创建新的初始化Promise
        this.initializationPromise = new Promise(async(resolve) => {
            console.log("开始初始化认证状态...");

            // 从存储读取token
            const storedToken = uni.getStorageSync('token');
            // 获取URL中的code参数
            const code = getUrlCode('code');

            console.log("初始化状态: code=", code, "token=", storedToken ? "已存在" : "不存在");

            if (code) {
                // 如果有code，使用code获取token
                try {
                    console.log("使用code获取token...");
                    const res = await getWxAuthorize(code);
                    if (res && res.data) {
                        this.token = res.data;
                        uni.setStorageSync('token', res.data);
                        console.log("获取token成功:", res.data.substring(0, 10) + "...");

                        // 获取用户信息
                        await this.fetchUserInfo(res.data);
                    } else {
                        console.error("获取token失败:", res);
                    }
                } catch (error) {
                    console.error('认证失败:', error);
                }
            } else if (storedToken) {
                // 如果已有token，直接使用
                this.token = storedToken;
                console.log("使用已存储的token:", storedToken.substring(0, 10) + "...");

                // 检查是否需要获取用户信息
                const storedUserInfo = uni.getStorageSync('userinfo');
                if (!storedUserInfo) {
                    await this.fetchUserInfo(storedToken);
                } else {
                    this.userInfo = storedUserInfo;
                }
            }

            this.isInitialized = true;
            resolve(this.token);
        });

        return this.initializationPromise;
    }

    /**
     * 获取用户信息
     * @param {string} token 认证token
     * @returns {Promise<object>} 用户信息
     */
    async fetchUserInfo(token) {
        if (!token) return null;

        try {
            console.log("获取用户信息...");
            const res = await getUserInfo(token);
            if (res && res.data) {
                this.userInfo = res.data;
                uni.setStorageSync('userinfo', res.data);
                console.log("用户信息获取成功");
                return res.data;
            } else {
                console.warn("获取用户信息返回数据为空");
                return null;
            }
        } catch (error) {
            console.error("获取用户信息失败:", error);
            return null;
        }
    }

    /**
     * 获取token
     * @returns {string} token值
     */
    getToken() {
        return this.token;
    }

    /**
     * 获取用户信息
     * @returns {object} 用户信息
     */
    getUserInfo() {
        return this.userInfo;
    }

    /**
     * 清除认证状态
     */
    clear() {
        this.token = null;
        this.userInfo = null;
        uni.removeStorageSync('token');
        uni.removeStorageSync('userinfo');
    }
}

// 创建单例实例
const authStore = new AuthStore();

export default authStore;