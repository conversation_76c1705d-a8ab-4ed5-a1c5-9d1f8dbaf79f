package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.ResumeDelivery;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface ResumeDeliveryMapper {
    List<ResumeDelivery> selectDeliveryList(ResumeDelivery delivery);

    ResumeDelivery selectDeliveryById(String id);

    ResumeDelivery selectDeliveryByJobAndUser(@Param("jobId") Long jobId, @Param("userId") Long userId);

    int insertDelivery(ResumeDelivery delivery);

    int updateDelivery(ResumeDelivery delivery);

    int deleteDeliveryById(String id);

    int countUserDeliveries(String userId);

    int countUserInterviewInvites(String userId);

    int countJobDeliveries(String jobId);

    int countAllDeliveries();

    int countTodayDeliveries();

    /**
     * 统计简历投递数量
     * 
     * @param delivery 投递信息
     * @return 投递数量
     */
    int selectResumeDeliveryCount(ResumeDelivery delivery);
    
    /**
     * 删除指定简历ID的所有投递记录
     * 
     * @param resumeId 简历ID
     * @return 结果
     */
    int deleteDeliveryByResumeId(String resumeId);
    
    /**
     * 检查简历是否投递到指定企业
     *
     * @param resumeId 简历ID
     * @param companyId 企业ID
     * @return 投递记录数量
     */
    int checkResumeDeliveredToCompany(@Param("resumeId") String resumeId, @Param("companyId") Long companyId);

    /**
     * 获取最新简历投递记录
     *
     * @param limit 限制数量
     * @return 最新投递记录列表
     */
    List<Map<String, Object>> selectRecentDeliveries(int limit);
}