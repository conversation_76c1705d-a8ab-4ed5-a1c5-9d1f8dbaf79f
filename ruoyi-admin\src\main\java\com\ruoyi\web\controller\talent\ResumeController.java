package com.ruoyi.web.controller.talent;

import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.net.URL;
import java.net.HttpURLConnection;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;
import javax.servlet.http.HttpServletResponse;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.util.Units;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.system.domain.Resume;
import com.ruoyi.system.service.IResumeService;
import com.ruoyi.system.mapper.ResumeMapper;
import com.ruoyi.talent.service.ICompanyAdminService;
import com.ruoyi.system.domain.ResumeFamily;
import com.ruoyi.system.domain.ResumeAwards;
import com.ruoyi.system.domain.ResumePosition;
import com.ruoyi.system.domain.ResumeEducation;
import com.ruoyi.system.domain.ResumeWork;
import com.ruoyi.system.domain.ResumeProject;
import com.ruoyi.system.domain.ResumeSkill;

@RestController
@RequestMapping("/talent/resume")
public class ResumeController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(ResumeController.class);

    @Autowired
    private IResumeService resumeService;

    @Autowired
    private ICompanyAdminService companyAdminService;

    @Autowired
    private ResumeMapper resumeMapper;

    /**
     * 获取简历列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Resume resume) {
        startPage();  // 开启分页，这个方法来自BaseController

        Long userId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(userId) && !SecurityUtils.hasRole("yiji_admin")) {
            resume.setUserId(userId.toString());
        }

        List<Resume> list = resumeService.selectResumeList(resume);
        return getDataTable(list);  // 这个方法来自BaseController，会自动处理分页数据
    }

    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable String id) {
        // 查询简历信息
        Resume resume = resumeService.selectResumeById(id);
        
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        
        // 判断权限：
        // 1. 管理员和一级管理员可以查看所有简历
        // 2. 简历所有者可以查看自己的简历
        // 3. 企业管理员可以查看投递到其企业的简历
        if (SecurityUtils.isAdmin(currentUserId) || SecurityUtils.hasRole("yiji_admin")) {
            // 管理员和一级管理员可以查看所有简历
            return AjaxResult.success(resume);
        } else if (resume.getUserId().equals(currentUserId.toString())) {
            // 简历所有者可以查看自己的简历
            return AjaxResult.success(resume);
        } else {
            // 检查是否是企业管理员，且简历是否投递到了该企业
            // 获取企业管理员所管理的企业ID
            Long companyId = companyAdminService.getAdminCompanyId(currentUserId);
            if (companyId != null) {
                // 检查该简历是否投递到了该企业
                boolean hasDelivered = resumeService.checkResumeDeliveredToCompany(id, companyId);
                if (hasDelivered) {
                    return AjaxResult.success(resume);
                }
            }
            
            return AjaxResult.error("没有权限查看此简历");
        }
    }

    @GetMapping("/default")
    public AjaxResult getDefault() {
        return AjaxResult.success(resumeService.selectDefaultResume(SecurityUtils.getUserId().toString()));
    }

    @PostMapping
    public AjaxResult add(@RequestBody Resume resume) {
        try {
            // 如果是管理员或一级管理员且指定了userId，则使用指定的userId
            if ((SecurityUtils.isAdmin(SecurityUtils.getUserId()) || SecurityUtils.hasRole("yiji_admin")) && StringUtils.hasText(resume.getUserId())) {
                // 保持原有的userId
            } else {
                // 非管理员或管理员未指定userId，使用当前登录用户的id
                resume.setUserId(SecurityUtils.getUserId().toString());
            }
            
            // 设置默认值
            if (resume.getCompleteness() == null) {
                resume.setCompleteness(0);
            }
            if (resume.getViews() == null) {
                resume.setViews(0);
            }
            if (resume.getStatus() == null) {
                resume.setStatus("0");  // 0表示正常状态
            }
            if (resume.getIsDefault() == null) {
                resume.setIsDefault(false);
            }
            
            return toAjax(resumeService.insertResume(resume));
        } catch (Exception e) {
            return AjaxResult.error("添加简历失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public AjaxResult edit(@PathVariable String id, @RequestBody Resume resume) {
        // 检查简历是否存在
        Resume existingResume = resumeService.selectResumeById(id);
        if (existingResume == null) {
            return AjaxResult.error("简历不存在");
        }

        Long currentUserId = SecurityUtils.getUserId();
        String currentUserIdStr = currentUserId.toString();
        String resumeUserId = existingResume.getUserId();

        // 详细的权限检查日志
        logger.info("编辑简历权限检查 - 简历ID: {}, 当前用户ID: {}, 简历所有者ID: {}",
                   id, currentUserIdStr, resumeUserId);

        // 权限检查逻辑
        boolean hasPermission = false;
        String permissionReason = "";

        // 1. 检查是否为系统管理员 (ID = 1)
        if (SecurityUtils.isAdmin(currentUserId)) {
            hasPermission = true;
            permissionReason = "系统管理员";
        }
        // 2. 检查是否为简历所有者
        else if (resumeUserId != null && resumeUserId.equals(currentUserIdStr)) {
            hasPermission = true;
            permissionReason = "简历所有者";
        }
        // 3. 检查是否有管理员角色
        else {
            try {
                LoginUser loginUser = SecurityUtils.getLoginUser();
                if (loginUser != null && loginUser.getUser() != null && loginUser.getUser().getRoles() != null) {
                    List<String> roleKeys = loginUser.getUser().getRoles().stream()
                        .map(role -> role.getRoleKey())
                        .collect(Collectors.toList());

                    logger.info("当前用户角色列表: {}", roleKeys);

                    // 检查是否有admin角色或yiji_admin角色
                    if (roleKeys.contains("admin") || roleKeys.contains("yiji_admin")) {
                        hasPermission = true;
                        permissionReason = "管理员角色: " + roleKeys;
                    }
                }
            } catch (Exception e) {
                logger.warn("获取用户角色信息失败: {}", e.getMessage());
            }
        }

        logger.info("权限检查结果 - 用户: {}, 简历: {}, 有权限: {}, 原因: {}",
                   currentUserIdStr, id, hasPermission, permissionReason);

        if (!hasPermission) {
            logger.warn("用户 {} 尝试修改不属于自己的简历 {}, 简历所有者: {}",
                       currentUserIdStr, id, resumeUserId);
            return AjaxResult.error("没有权限修改此简历");
        }

        resume.setId(id);
        return toAjax(resumeService.updateResume(resume));
    }

    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable String id) {
        // 检查权限
        Resume resume = resumeService.selectResumeById(id);
        if (resume == null) {
            return AjaxResult.error("简历不存在");
        }
        
        Long currentUserId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(currentUserId) && !SecurityUtils.hasRole("yiji_admin")
            && !resume.getUserId().equals(currentUserId.toString())) {
            return AjaxResult.error("没有权限删除此简历");
        }
        
        return toAjax(resumeService.deleteResumeById(id));
    }

    @PutMapping("/{id}/default")
    public AjaxResult setDefault(@PathVariable String id) {
        // 检查权限
        Resume resume = resumeService.selectResumeById(id);
        if (resume == null) {
            return AjaxResult.error("简历不存在");
        }
        
        Long currentUserId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(currentUserId) 
            && !resume.getUserId().equals(currentUserId.toString())) {
            return AjaxResult.error("没有权限设置此简历");
        }
        
        return toAjax(resumeService.setDefaultResume(id, resume.getUserId()));
    }

    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("file") MultipartFile file, @RequestParam(required = false) String userId) throws Exception {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择文件");
        }
        
        // 如果不是管理员或一级管理员，只能上传给自己
        Long currentUserId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(currentUserId) && !SecurityUtils.hasRole("yiji_admin")) {
            userId = currentUserId.toString();
        }
        
        String url = resumeService.uploadAttachment(userId, file.getOriginalFilename(), file.getBytes());
        return AjaxResult.success(url);
    }

    @PostMapping("/avatar")
    public AjaxResult uploadAvatar(@RequestParam("file") MultipartFile file, @RequestParam(required = false) String userId) throws Exception {
        if (file == null) {
            logger.error("未接收到上传文件");
            return AjaxResult.error("请选择文件");
        }
        
        // 如果不是管理员，只能上传给自己
        Long currentUserId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(currentUserId)) {
            userId = currentUserId.toString();
        }
        
        if (file.isEmpty()) {
            return AjaxResult.error("请选择文件");
        }
        
        String fileName = file.getOriginalFilename();
        if (!isValidImageType(fileName)) {
            return AjaxResult.error("只能上传jpg/png/gif格式的图片");
        }
        
        try {
            String avatarUrl = resumeService.uploadAvatar(file.getBytes(), fileName);
            return AjaxResult.success(avatarUrl);
        } catch (Exception e) {
            logger.error("文件上传失败", e);
            return AjaxResult.error("文件上传失败：" + e.getMessage());
        }
    }

    private boolean isValidImageType(String fileName) {
        String[] allowedExtensions = {".jpg", ".jpeg", ".png", ".gif"};
        String fileExtension = fileName.toLowerCase();
        for (String extension : allowedExtensions) {
            if (fileExtension.endsWith(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取简历统计数据
     */
    @GetMapping("/stats")
    public AjaxResult getResumeStats() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> stats = new HashMap<>();
        
        // 获取简历列表
        Resume resume = new Resume();
        resume.setUserId(userId.toString());
        List<Resume> resumes = resumeService.selectResumeList(resume);
        
        // 计算总浏览量
        int totalViews = resumes.stream()
            .mapToInt(r -> r.getViews() != null ? r.getViews() : 0)
            .sum();
            
        // 获取投递次数和面试邀请数
        int totalDeliveries = resumeService.getTotalDeliveryCount(userId.toString());
        int interviewInvites = resumeService.getInterviewInviteCount(userId.toString());
        
        stats.put("totalViews", totalViews);
        stats.put("totalDeliveries", totalDeliveries);
        stats.put("interviewInvites", interviewInvites);
        
        return AjaxResult.success(stats);
    }

    @PostMapping("/upload/word")
    public AjaxResult uploadWordResume(@RequestParam("file") MultipartFile file) throws Exception {
        if (file == null) {
            logger.error("未接收到上传文件");
            return AjaxResult.error("请选择文件");
        }
        
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        
        if (file.isEmpty()) {
            return AjaxResult.error("请选择文件");
        }
        
        String fileName = file.getOriginalFilename();
        // 检查文件类型
        if (!isValidWordType(fileName)) {
            return AjaxResult.error("只能上传doc/docx格式的文件");
        }
        
        try {
            // 调用服务层处理文件上传
            String resumeId = resumeService.uploadWordResume(file.getBytes(), fileName, userId.toString());
            return AjaxResult.success("上传成功", resumeId);
        } catch (Exception e) {
            logger.error("文件上传失败", e);
            return AjaxResult.error("文件上传失败：" + e.getMessage());
        }
    }

    private boolean isValidWordType(String fileName) {
        String[] allowedExtensions = {".doc", ".docx"};
        String fileExtension = fileName.toLowerCase();
        for (String extension : allowedExtensions) {
            if (fileExtension.endsWith(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取简历总数
     */
    @GetMapping("/count-total")
    public AjaxResult countTotal() {
        int count = resumeService.countTotalResumes();
        return AjaxResult.success(count);
    }

    @GetMapping("/{id}/family")
    public AjaxResult getFamilyMembers(@PathVariable String id) {
        try {
            List<ResumeFamily> familyMembers = resumeService.getFamilyMembers(id);
            return AjaxResult.success(familyMembers);
        } catch (Exception e) {
            return AjaxResult.error("获取家庭成员信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}/awards")
    public AjaxResult getAwards(@PathVariable String id) {
        try {
            ResumeAwards awards = resumeService.getAwards(id);
            return AjaxResult.success(awards);
        } catch (Exception e) {
            return AjaxResult.error("获取奖惩情况失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}/position")
    public AjaxResult getPosition(@PathVariable String id) {
        try {
            ResumePosition position = resumeService.getPosition(id);
            return AjaxResult.success(position);
        } catch (Exception e) {
            return AjaxResult.error("获取现任职务信息失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}/family")
    public AjaxResult updateFamilyMembers(@PathVariable String id, @RequestBody List<ResumeFamily> familyMembers) {
        try {
            resumeService.updateFamilyMembers(id, familyMembers);
            return AjaxResult.success("更新家庭成员信息成功");
        } catch (Exception e) {
            return AjaxResult.error("更新家庭成员信息失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}/awards")
    public AjaxResult updateAwards(@PathVariable String id, @RequestBody ResumeAwards awards) {
        try {
            resumeService.updateAwards(id, awards);
            return AjaxResult.success("更新奖惩情况成功");
        } catch (Exception e) {
            return AjaxResult.error("更新奖惩情况失败：" + e.getMessage());
        }
    }

    @PutMapping("/{id}/position")
    public AjaxResult updatePosition(@PathVariable String id, @RequestBody ResumePosition position) {
        try {
            resumeService.updatePosition(id, position);
            return AjaxResult.success("更新现任职务信息成功");
        } catch (Exception e) {
            return AjaxResult.error("更新现任职务信息失败：" + e.getMessage());
        }
    }

    /**
     * 下载简历
     */
    @GetMapping("/download/{id}")
    public void downloadResume(@PathVariable String id, HttpServletResponse response) {
        try {
            logger.info("开始下载简历，ID: {}", id);
            logger.info("请求路径: /talent/resume/download/{}", id);

            Resume resume;
            try {
                resume = resumeService.selectResumeById(id);
            } catch (Exception e) {
                logger.warn("简历不存在或查询失败，ID: {}, 错误: {}", id, e.getMessage());
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            if (resume == null) {
                logger.warn("简历不存在，ID: {}", id);
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            logger.info("找到简历，姓名: {}, 用户ID: {}", resume.getName(), resume.getUserId());

            // 权限检查
            Long currentUserId = SecurityUtils.getUserId();
            logger.info("当前用户ID: {}", currentUserId);

            if (!SecurityUtils.isAdmin(currentUserId) && !SecurityUtils.hasRole("yiji_admin")
                && !resume.getUserId().equals(currentUserId.toString())) {
                // 检查是否是企业管理员，且简历是否投递到了该企业
                Long companyId = companyAdminService.getAdminCompanyId(currentUserId);
                if (companyId == null || !resumeService.checkResumeDeliveredToCompany(id, companyId)) {
                    logger.warn("用户无权限下载简历，用户ID: {}, 简历ID: {}", currentUserId, id);
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    return;
                }
            }

            logger.info("权限检查通过，开始生成Word文档");

            // 生成Word文档
            ByteArrayOutputStream docStream = generateResumeWordDocument(resume);
            String fileName = (resume.getName() != null ? resume.getName() : "简历") + "_" + id + ".docx";

            logger.info("Word文档生成成功，文件名: {}, 大小: {} bytes", fileName, docStream.size());

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setCharacterEncoding("UTF-8");
            FileUtils.setAttachmentResponseHeader(response, fileName);

            // 写入文件内容
            docStream.writeTo(response.getOutputStream());
            response.getOutputStream().flush();

            logger.info("简历下载完成，ID: {}", id);

        } catch (Exception e) {
            logger.error("下载简历失败，ID: " + id, e);
            try {
                if (!response.isCommitted()) {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("application/json");
                    response.setCharacterEncoding("UTF-8");
                    response.getWriter().write("{\"code\":500,\"msg\":\"下载失败: " + e.getMessage() + "\"}");
                }
            } catch (Exception ex) {
                logger.error("写入错误响应失败", ex);
            }
        }
    }



    /**
     * 生成简历Word文档
     */
    private ByteArrayOutputStream generateResumeWordDocument(Resume resume) throws Exception {
        XWPFDocument document = new XWPFDocument();

        // 设置页面边距
        setPageMargins(document);

        // 创建标题 - "干部任免审批表"
        createDocumentTitle(document);

        // 创建基本信息区域（左侧头像，右侧基本信息表格）
        createBasicInfoArea(document, resume);

        // 学历学位信息
        createEducationDegreeSection(document, resume);

        // 现任职务
        createCurrentPositionSection(document, resume);

        // 教育经历
        try {
            if (resume.getEducation() != null && !resume.getEducation().isEmpty()) {
                addEducationSection(document, resume.getEducation());
            } else {
                logger.info("简历没有教育经历信息");
            }
        } catch (Exception e) {
            logger.warn("处理教育经历时出错: " + e.getMessage(), e);
        }

        // 工作经历
        try {
            if (resume.getWork() != null && !resume.getWork().isEmpty()) {
                addWorkSection(document, resume.getWork());
            } else {
                logger.info("简历没有工作经历信息");
            }
        } catch (Exception e) {
            logger.warn("处理工作经历时出错: " + e.getMessage(), e);
        }

        // 项目经历
        try {
            if (resume.getProject() != null && !resume.getProject().isEmpty()) {
                addProjectSection(document, resume.getProject());
            } else {
                logger.info("简历没有项目经历信息");
            }
        } catch (Exception e) {
            logger.warn("处理项目经历时出错: " + e.getMessage(), e);
        }

        // 输出到字节流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        document.write(outputStream);
        document.close();

        return outputStream;
    }

    /**
     * 创建文档标题 - "干部任免审批表"
     */
    private void createDocumentTitle(XWPFDocument document) {
        try {
            XWPFParagraph titlePara = document.createParagraph();
            titlePara.setAlignment(ParagraphAlignment.CENTER);

            XWPFRun titleRun = titlePara.createRun();
            titleRun.setText("干部任免审批表");
            titleRun.setBold(true);
            titleRun.setFontSize(24);
            titleRun.setFontFamily("宋体");
            titleRun.setColor("303133");

            // 添加空行
            document.createParagraph();
        } catch (Exception e) {
            logger.warn("创建文档标题失败: " + e.getMessage());
        }
    }

    /**
     * 创建基本信息区域（左侧头像，右侧基本信息表格）
     */
    private void createBasicInfoArea(XWPFDocument document, Resume resume) {
        try {
            // 创建包含头像和基本信息的表格
            XWPFTable basicTable = document.createTable(1, 2);
            basicTable.setWidth("100%");

            // 左侧：头像区域
            XWPFTableCell avatarCell = basicTable.getRow(0).getCell(0);
            avatarCell.removeParagraph(0);

            XWPFParagraph avatarPara = avatarCell.addParagraph();
            avatarPara.setAlignment(ParagraphAlignment.CENTER);

            // 添加头像
            addAvatarToCell(avatarPara, resume.getAvatar());

            // 右侧：基本信息表格
            XWPFTableCell infoCell = basicTable.getRow(0).getCell(1);
            infoCell.removeParagraph(0);

            // 在右侧单元格中创建基本信息表格
            createBasicInfoTableInCell(infoCell, resume);

            // 添加空行
            document.createParagraph();

        } catch (Exception e) {
            logger.warn("创建基本信息区域失败: " + e.getMessage());
        }
    }

    /**
     * 创建学历学位信息
     */
    private void createEducationDegreeSection(XWPFDocument document, Resume resume) {
        try {
            // 创建章节标题
            createWebStyleSectionTitle(document, "学历学位");

            // 创建学历学位表格
            XWPFTable eduTable = document.createTable(2, 4);
            eduTable.setWidth("100%");
            setTableStyle(eduTable);

            // 获取最高学历信息
            String highestEducation = getHighestEducation(resume.getEducation());
            String highestDegree = getHighestDegree(resume.getEducation());
            String highestSchool = getHighestSchool(resume.getEducation());
            String highestMajor = getHighestMajor(resume.getEducation());

            // 第一行
            setTableCell(eduTable, 0, 0, "学历", true);
            setTableCell(eduTable, 0, 1, highestEducation, false);
            setTableCell(eduTable, 0, 2, "学位", true);
            setTableCell(eduTable, 0, 3, highestDegree, false);

            // 第二行
            setTableCell(eduTable, 1, 0, "毕业院校", true);
            setTableCell(eduTable, 1, 1, highestSchool, false);
            setTableCell(eduTable, 1, 2, "专业", true);
            setTableCell(eduTable, 1, 3, highestMajor, false);

            // 添加空行
            document.createParagraph();

        } catch (Exception e) {
            logger.warn("创建学历学位信息失败: " + e.getMessage());
        }
    }

    /**
     * 创建现任职务信息
     */
    private void createCurrentPositionSection(XWPFDocument document, Resume resume) {
        try {
            // 创建章节标题
            createWebStyleSectionTitle(document, "现任职务");

            // 创建现任职务表格
            XWPFTable posTable = document.createTable(1, 2);
            posTable.setWidth("100%");
            setTableStyle(posTable);

            setTableCell(posTable, 0, 0, "现任职务", true);
            setTableCell(posTable, 0, 1, resume.getCurrentPosition() != null ? resume.getCurrentPosition() : "-", false);

            // 添加空行
            document.createParagraph();

        } catch (Exception e) {
            logger.warn("创建现任职务信息失败: " + e.getMessage());
        }
    }

    /**
     * 在单元格中创建基本信息表格
     */
    private void createBasicInfoTableInCell(XWPFTableCell cell, Resume resume) {
        try {
            // 简化处理，直接添加段落显示信息
            addInfoParagraph(cell, "姓名", resume.getName());
            addInfoParagraph(cell, "性别", resume.getGender());
            addInfoParagraph(cell, "出生年月", formatDateToYearMonth(resume.getBirthday()));
            addInfoParagraph(cell, "年龄", resume.getAge() != null ? resume.getAge() + "岁" : null);
            addInfoParagraph(cell, "民族", resume.getNation());
            addInfoParagraph(cell, "籍贯", resume.getNativePlace());
            addInfoParagraph(cell, "政治面貌", resume.getPoliticalStatus());
            addInfoParagraph(cell, "联系电话", resume.getPhone());
            addInfoParagraph(cell, "电子邮箱", resume.getEmail());
            addInfoParagraph(cell, "专业技术职务", resume.getTechnicalPosition());

        } catch (Exception e) {
            logger.warn("在单元格中创建基本信息表格失败: " + e.getMessage());
            // 降级处理，直接添加文本
            XWPFParagraph para = cell.addParagraph();
            XWPFRun run = para.createRun();
            run.setText("基本信息");
            run.setBold(true);
        }
    }

    /**
     * 添加信息段落
     */
    private void addInfoParagraph(XWPFTableCell cell, String label, String value) {
        if (value == null || value.trim().isEmpty()) {
            value = "-";
        }

        XWPFParagraph para = cell.addParagraph();

        // 标签（加粗）
        XWPFRun labelRun = para.createRun();
        labelRun.setText(label + "：");
        labelRun.setBold(true);
        labelRun.setFontSize(10);
        labelRun.setFontFamily("宋体");

        // 值
        XWPFRun valueRun = para.createRun();
        valueRun.setText(value);
        valueRun.setFontSize(10);
        valueRun.setFontFamily("宋体");
    }

    /**
     * 创建基本信息表格
     */
    private void createBasicInfoTable(XWPFDocument document, Resume resume) {
        // 创建段落标题
        XWPFParagraph sectionTitle = document.createParagraph();
        XWPFRun sectionRun = sectionTitle.createRun();
        sectionRun.setText("基本信息");
        sectionRun.setBold(true);
        sectionRun.setFontSize(14);
        sectionRun.setFontFamily("宋体");

        // 创建表格
        XWPFTable table = document.createTable(4, 4);
        table.setWidth("100%");

        // 设置表格样式
        table.getCTTbl().getTblPr().getTblBorders();

        // 填充基本信息
        setTableCell(table, 0, 0, "姓名", true);
        setTableCell(table, 0, 1, resume.getName() != null ? resume.getName() : "", false);
        setTableCell(table, 0, 2, "性别", true);
        setTableCell(table, 0, 3, resume.getGender() != null ? resume.getGender() : "", false);

        setTableCell(table, 1, 0, "出生日期", true);
        setTableCell(table, 1, 1, formatDate(resume.getBirthday()), false);
        setTableCell(table, 1, 2, "民族", true);
        setTableCell(table, 1, 3, resume.getNation() != null ? resume.getNation() : "", false);

        setTableCell(table, 2, 0, "政治面貌", true);
        setTableCell(table, 2, 1, resume.getPoliticalStatus() != null ? resume.getPoliticalStatus() : "", false);
        setTableCell(table, 2, 2, "联系电话", true);
        setTableCell(table, 2, 3, resume.getPhone() != null ? resume.getPhone() : "", false);

        setTableCell(table, 3, 0, "电子邮箱", true);
        setTableCell(table, 3, 1, resume.getEmail() != null ? resume.getEmail() : "", false);
        setTableCell(table, 3, 2, "现任职务", true);
        setTableCell(table, 3, 3, resume.getCurrentPosition() != null ? resume.getCurrentPosition() : "", false);

        // 添加空行
        document.createParagraph();
    }

    /**
     * 创建网页样式的章节标题
     */
    private void createWebStyleSectionTitle(XWPFDocument document, String title) {
        try {
            XWPFParagraph titlePara = document.createParagraph();
            titlePara.setAlignment(ParagraphAlignment.LEFT);

            XWPFRun titleRun = titlePara.createRun();
            titleRun.setText(title);
            titleRun.setBold(true);
            titleRun.setFontSize(16);
            titleRun.setFontFamily("宋体");
            titleRun.setColor("303133");

            // 添加小间距
            XWPFParagraph spacePara = document.createParagraph();
            XWPFRun spaceRun = spacePara.createRun();
            spaceRun.setFontSize(4);

        } catch (Exception e) {
            logger.warn("创建章节标题失败: " + e.getMessage());
        }
    }

    /**
     * 格式化日期为年月格式
     */
    private String formatDateToYearMonth(Date date) {
        if (date == null) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
            return sdf.format(date);
        } catch (Exception e) {
            logger.warn("日期格式化失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取最高学历
     */
    private String getHighestEducation(List<ResumeEducation> educationList) {
        if (educationList == null || educationList.isEmpty()) {
            return "-";
        }
        ResumeEducation highest = educationList.stream()
            .filter(edu -> edu.getIsHighest() != null && edu.getIsHighest())
            .findFirst()
            .orElse(educationList.get(0));
        return highest.getDegree() != null ? highest.getDegree() : "-";
    }

    /**
     * 获取最高学位
     */
    private String getHighestDegree(List<ResumeEducation> educationList) {
        if (educationList == null || educationList.isEmpty()) {
            return "-";
        }
        ResumeEducation highest = educationList.stream()
            .filter(edu -> edu.getIsHighest() != null && edu.getIsHighest())
            .findFirst()
            .orElse(educationList.get(0));

        // 根据学历推断学位
        String degree = highest.getDegree();
        if (degree == null) return "-";

        switch (degree) {
            case "专科": return "专科";
            case "本科": return "学士";
            case "硕士": return "硕士";
            case "博士": return "博士";
            default: return "-";
        }
    }

    /**
     * 获取最高学历学校
     */
    private String getHighestSchool(List<ResumeEducation> educationList) {
        if (educationList == null || educationList.isEmpty()) {
            return "-";
        }
        ResumeEducation highest = educationList.stream()
            .filter(edu -> edu.getIsHighest() != null && edu.getIsHighest())
            .findFirst()
            .orElse(educationList.get(0));
        return highest.getSchool() != null ? highest.getSchool() : "-";
    }

    /**
     * 获取最高学历专业
     */
    private String getHighestMajor(List<ResumeEducation> educationList) {
        if (educationList == null || educationList.isEmpty()) {
            return "-";
        }
        ResumeEducation highest = educationList.stream()
            .filter(edu -> edu.getIsHighest() != null && edu.getIsHighest())
            .findFirst()
            .orElse(educationList.get(0));
        return highest.getMajor() != null ? highest.getMajor() : "-";
    }

    /**
     * 设置表格单元格内容
     */
    private void setTableCell(XWPFTable table, int row, int col, String text, boolean bold) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setBold(bold);
        run.setFontFamily("宋体");
        run.setFontSize(10);
    }

    /**
     * 添加头像到单元格
     */
    private void addAvatarToCell(XWPFParagraph paragraph, String avatarUrl) {
        try {
            XWPFRun run = paragraph.createRun();

            if (avatarUrl != null && !avatarUrl.isEmpty()) {
                logger.info("头像URL: " + avatarUrl);

                try {
                    InputStream imageStream = null;
                    int pictureType = XWPFDocument.PICTURE_TYPE_JPEG;
                    String tempFilePath = null;

                    if (avatarUrl.startsWith("/profile/")) {
                        // 本地头像处理
                        String avatarPath = RuoYiConfig.getProfile() + avatarUrl.substring("/profile".length());
                        logger.info("本地头像路径: " + avatarPath);

                        File avatarFile = new File(avatarPath);
                        if (avatarFile.exists() && avatarFile.isFile()) {
                            imageStream = new java.io.FileInputStream(avatarFile);
                            pictureType = getPictureType(avatarFile.getName());
                            logger.info("本地头像文件找到: " + avatarPath);
                        } else {
                            logger.warn("本地头像文件不存在: " + avatarPath);
                        }
                    } else if (avatarUrl.startsWith("http://") || avatarUrl.startsWith("https://")) {
                        // 网络头像处理 - 下载到临时文件
                        logger.info("开始下载网络头像: " + avatarUrl);
                        tempFilePath = downloadNetworkAvatar(avatarUrl);

                        if (tempFilePath != null) {
                            File tempFile = new File(tempFilePath);
                            if (tempFile.exists()) {
                                imageStream = new java.io.FileInputStream(tempFile);
                                pictureType = getPictureType(tempFile.getName());
                                logger.info("网络头像下载成功: " + tempFilePath);
                            }
                        }
                    } else {
                        // 其他格式，尝试作为相对路径处理
                        logger.info("尝试相对路径: " + avatarUrl);
                        File avatarFile = new File(avatarUrl);
                        if (avatarFile.exists() && avatarFile.isFile()) {
                            imageStream = new java.io.FileInputStream(avatarFile);
                            pictureType = getPictureType(avatarFile.getName());
                            logger.info("相对路径头像找到: " + avatarUrl);
                        } else {
                            logger.warn("相对路径头像文件不存在: " + avatarUrl);
                        }
                    }

                    // 如果成功获取到图片流，添加到文档
                    if (imageStream != null) {
                        try {
                            run.addPicture(imageStream, pictureType, "avatar",
                                          Units.toEMU(150), Units.toEMU(150)); // 150x150像素
                            imageStream.close();
                            logger.info("头像成功添加到Word文档");

                            // 清理临时文件
                            if (tempFilePath != null) {
                                File tempFile = new File(tempFilePath);
                                if (tempFile.exists()) {
                                    tempFile.delete();
                                    logger.info("临时头像文件已删除: " + tempFilePath);
                                }
                            }
                            return;
                        } catch (Exception e) {
                            logger.warn("添加图片到文档失败: " + e.getMessage(), e);
                            imageStream.close();
                        }
                    }

                    // 如果图片处理失败，显示头像信息
                    run.setText("[头像照片]");
                    run.setBold(true);
                    run.setFontFamily("宋体");
                    run.setFontSize(10);

                } catch (Exception e) {
                    // 头像处理失败，显示占位符
                    logger.warn("处理头像失败: " + e.getMessage(), e);
                    run.setText("[头像照片]");
                    run.setBold(true);
                    run.setFontFamily("宋体");
                    run.setFontSize(10);
                }
            } else {
                // 没有头像，显示占位符
                run.setText("[头像照片]");
                run.setBold(true);
                run.setFontFamily("宋体");
                run.setFontSize(10);
            }
        } catch (Exception e) {
            logger.warn("添加头像失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加头像到文档
     */
    private void addAvatarToDocument(XWPFDocument document, String avatarUrl) {
        try {
            // 创建段落
            XWPFParagraph paragraph = document.createParagraph();
            paragraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun run = paragraph.createRun();

            if (avatarUrl != null && !avatarUrl.isEmpty()) {
                logger.info("头像URL: " + avatarUrl);

                try {
                    InputStream imageStream = null;
                    int pictureType = XWPFDocument.PICTURE_TYPE_JPEG;
                    String tempFilePath = null;

                    if (avatarUrl.startsWith("/profile/")) {
                        // 本地头像处理
                        String avatarPath = RuoYiConfig.getProfile() + avatarUrl.substring("/profile".length());
                        logger.info("本地头像路径: " + avatarPath);

                        File avatarFile = new File(avatarPath);
                        if (avatarFile.exists() && avatarFile.isFile()) {
                            imageStream = new java.io.FileInputStream(avatarFile);
                            pictureType = getPictureType(avatarFile.getName());
                            logger.info("本地头像文件找到: " + avatarPath);
                        } else {
                            logger.warn("本地头像文件不存在: " + avatarPath);
                        }
                    } else if (avatarUrl.startsWith("http://") || avatarUrl.startsWith("https://")) {
                        // 网络头像处理 - 下载到临时文件
                        logger.info("开始下载网络头像: " + avatarUrl);
                        tempFilePath = downloadNetworkAvatar(avatarUrl);

                        if (tempFilePath != null) {
                            File tempFile = new File(tempFilePath);
                            if (tempFile.exists()) {
                                imageStream = new java.io.FileInputStream(tempFile);
                                pictureType = getPictureType(tempFile.getName());
                                logger.info("网络头像下载成功: " + tempFilePath);
                            }
                        }
                    } else {
                        // 其他格式，尝试作为相对路径处理
                        logger.info("尝试相对路径: " + avatarUrl);
                        File avatarFile = new File(avatarUrl);
                        if (avatarFile.exists() && avatarFile.isFile()) {
                            imageStream = new java.io.FileInputStream(avatarFile);
                            pictureType = getPictureType(avatarFile.getName());
                            logger.info("相对路径头像找到: " + avatarUrl);
                        } else {
                            logger.warn("相对路径头像文件不存在: " + avatarUrl);
                        }
                    }

                    // 如果成功获取到图片流，添加到文档
                    if (imageStream != null) {
                        try {
                            run.addPicture(imageStream, pictureType, "avatar",
                                          Units.toEMU(100), Units.toEMU(120)); // 100x120像素
                            imageStream.close();
                            logger.info("头像成功添加到Word文档");

                            // 清理临时文件
                            if (tempFilePath != null) {
                                File tempFile = new File(tempFilePath);
                                if (tempFile.exists()) {
                                    tempFile.delete();
                                    logger.info("临时头像文件已删除: " + tempFilePath);
                                }
                            }
                            return;
                        } catch (Exception e) {
                            logger.warn("添加图片到文档失败: " + e.getMessage(), e);
                            imageStream.close();
                        }
                    }

                    // 如果图片处理失败，显示头像信息
                    if (avatarUrl.startsWith("http://") || avatarUrl.startsWith("https://")) {
                        run.setText("[网络头像]");
                        run.setBold(true);
                        run.setFontFamily("宋体");
                        run.setFontSize(10);

                        run.addBreak();
                        XWPFRun urlRun = paragraph.createRun();
                        urlRun.setText("头像地址: " + avatarUrl);
                        urlRun.setFontFamily("宋体");
                        urlRun.setFontSize(8);
                    } else {
                        run.setText("[头像照片]");
                        run.setBold(true);
                        run.setFontFamily("宋体");
                        run.setFontSize(10);
                    }

                } catch (Exception e) {
                    // 头像处理失败，显示占位符
                    logger.warn("处理头像失败: " + e.getMessage(), e);
                    run.setText("[头像照片]");
                    run.setBold(true);
                    run.setFontFamily("宋体");
                    run.setFontSize(10);
                }
            } else {
                // 没有头像，显示占位符
                run.setText("[头像照片]");
                run.setBold(true);
                run.setFontFamily("宋体");
                run.setFontSize(10);
            }
        } catch (Exception e) {
            logger.warn("添加头像失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加教育经历部分
     */
    private void addEducationSection(XWPFDocument document, List<ResumeEducation> educationList) {
        // 创建章节标题
        createWebStyleSectionTitle(document, "教育经历");

        // 创建时间轴容器
        createTimelineContainer(document, educationList, "education");

        // 添加空行
        document.createParagraph();
    }

    /**
     * 创建时间轴容器（统一处理教育、工作、项目经历）
     */
    private void createTimelineContainer(XWPFDocument document, List<?> itemList, String type) {
        try {
            if (itemList == null || itemList.isEmpty()) {
                // 显示空数据提示
                XWPFParagraph emptyPara = document.createParagraph();
                emptyPara.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun emptyRun = emptyPara.createRun();
                emptyRun.setText("暂无" + getTypeName(type) + "信息");
                emptyRun.setFontSize(11);
                emptyRun.setFontFamily("宋体");
                emptyRun.setColor("909399");
                emptyRun.setItalic(true);
                return;
            }

            // 为每个条目创建时间轴样式
            for (Object item : itemList) {
                createTimelineItem(document, item, type);
            }

        } catch (Exception e) {
            logger.warn("创建时间轴容器失败: " + e.getMessage());
        }
    }

    /**
     * 创建单个时间轴条目
     */
    private void createTimelineItem(XWPFDocument document, Object item, String type) {
        try {
            // 创建时间轴条目表格（左侧时间，右侧内容）
            XWPFTable timelineTable = document.createTable(1, 2);
            timelineTable.setWidth("100%");

            // 设置列宽比例（时间列较窄，内容列较宽）
            XWPFTableCell timeCell = timelineTable.getRow(0).getCell(0);
            XWPFTableCell contentCell = timelineTable.getRow(0).getCell(1);

            // 清除默认段落
            timeCell.removeParagraph(0);
            contentCell.removeParagraph(0);

            // 填充时间和内容
            switch (type) {
                case "education":
                    fillEducationTimelineItem(timeCell, contentCell, (ResumeEducation) item);
                    break;
                case "work":
                    fillWorkTimelineItem(timeCell, contentCell, (ResumeWork) item);
                    break;
                case "project":
                    fillProjectTimelineItem(timeCell, contentCell, (ResumeProject) item);
                    break;
            }

            // 添加小间距
            XWPFParagraph spacePara = document.createParagraph();
            XWPFRun spaceRun = spacePara.createRun();
            spaceRun.setFontSize(6);

        } catch (Exception e) {
            logger.warn("创建时间轴条目失败: " + e.getMessage());
        }
    }

    /**
     * 填充教育经历时间轴条目
     */
    private void fillEducationTimelineItem(XWPFTableCell timeCell, XWPFTableCell contentCell, ResumeEducation edu) {
        // 时间部分
        XWPFParagraph timePara = timeCell.addParagraph();
        XWPFRun timeRun = timePara.createRun();
        timeRun.setText(formatEducationTime(edu));
        timeRun.setBold(true);
        timeRun.setFontSize(11);
        timeRun.setFontFamily("宋体");
        timeRun.setColor("303133");

        // 内容部分
        // 学校名称（标题）
        XWPFParagraph titlePara = contentCell.addParagraph();
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(edu.getSchool() != null ? edu.getSchool() : "未知学校");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");
        titleRun.setColor("303133");

        // 专业和学历（副标题）
        XWPFParagraph subtitlePara = contentCell.addParagraph();
        XWPFRun subtitleRun = subtitlePara.createRun();
        String subtitle = (edu.getMajor() != null ? edu.getMajor() : "未知专业") +
                         " | " + (edu.getDegree() != null ? edu.getDegree() : "未知学历");
        subtitleRun.setText(subtitle);
        subtitleRun.setFontSize(14);
        subtitleRun.setFontFamily("宋体");
        subtitleRun.setColor("909399");
    }

    /**
     * 填充工作经历时间轴条目
     */
    private void fillWorkTimelineItem(XWPFTableCell timeCell, XWPFTableCell contentCell, ResumeWork work) {
        // 时间部分
        XWPFParagraph timePara = timeCell.addParagraph();
        XWPFRun timeRun = timePara.createRun();
        timeRun.setText(formatWorkTime(work));
        timeRun.setBold(true);
        timeRun.setFontSize(11);
        timeRun.setFontFamily("宋体");
        timeRun.setColor("303133");

        // 内容部分
        // 公司名称（标题）
        XWPFParagraph titlePara = contentCell.addParagraph();
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(work.getCompany() != null ? work.getCompany() : "未知公司");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");
        titleRun.setColor("303133");

        // 职位和部门（副标题）
        XWPFParagraph subtitlePara = contentCell.addParagraph();
        XWPFRun subtitleRun = subtitlePara.createRun();
        String subtitle = (work.getPosition() != null ? work.getPosition() : "未知职位") +
                         " | " + (work.getDepartment() != null ? work.getDepartment() : "");
        subtitleRun.setText(subtitle);
        subtitleRun.setFontSize(14);
        subtitleRun.setFontFamily("宋体");
        subtitleRun.setColor("909399");

        // 工作描述
        if (work.getDescription() != null && !work.getDescription().trim().isEmpty()) {
            XWPFParagraph descPara = contentCell.addParagraph();
            XWPFRun descRun = descPara.createRun();
            descRun.setText(work.getDescription());
            descRun.setFontSize(14);
            descRun.setFontFamily("宋体");
            descRun.setColor("606266");
        }
    }

    /**
     * 填充项目经历时间轴条目
     */
    private void fillProjectTimelineItem(XWPFTableCell timeCell, XWPFTableCell contentCell, ResumeProject project) {
        // 时间部分
        XWPFParagraph timePara = timeCell.addParagraph();
        XWPFRun timeRun = timePara.createRun();
        timeRun.setText(formatProjectTime(project));
        timeRun.setBold(true);
        timeRun.setFontSize(11);
        timeRun.setFontFamily("宋体");
        timeRun.setColor("303133");

        // 内容部分
        // 项目名称（标题）
        XWPFParagraph titlePara = contentCell.addParagraph();
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(project.getName() != null ? project.getName() : "未知项目");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");
        titleRun.setColor("303133");

        // 担任角色（副标题）
        XWPFParagraph subtitlePara = contentCell.addParagraph();
        XWPFRun subtitleRun = subtitlePara.createRun();
        subtitleRun.setText(project.getRole() != null ? project.getRole() : "未知角色");
        subtitleRun.setFontSize(14);
        subtitleRun.setFontFamily("宋体");
        subtitleRun.setColor("909399");

        // 项目描述
        if (project.getDescription() != null && !project.getDescription().trim().isEmpty()) {
            XWPFParagraph descPara = contentCell.addParagraph();
            XWPFRun descRun = descPara.createRun();
            descRun.setText(project.getDescription());
            descRun.setFontSize(14);
            descRun.setFontFamily("宋体");
            descRun.setColor("606266");
        }

        // 项目成果
        if (project.getAchievement() != null && !project.getAchievement().trim().isEmpty()) {
            XWPFParagraph achievementPara = contentCell.addParagraph();
            XWPFRun achievementRun = achievementPara.createRun();
            achievementRun.setText("成果：" + project.getAchievement());
            achievementRun.setFontSize(14);
            achievementRun.setFontFamily("宋体");
            achievementRun.setColor("409eff");
            achievementRun.setBold(true);
        }
    }

    /**
     * 获取类型名称
     */
    private String getTypeName(String type) {
        switch (type) {
            case "education": return "教育经历";
            case "work": return "工作经历";
            case "project": return "项目经历";
            default: return "经历";
        }
    }

    /**
     * 创建单个教育经历条目
     */
    private void createEducationItem(XWPFDocument document, ResumeEducation edu) {
        try {
            // 创建主要信息段落
            XWPFParagraph mainPara = document.createParagraph();
            mainPara.setAlignment(ParagraphAlignment.LEFT);

            // 学校名称（加粗）
            XWPFRun schoolRun = mainPara.createRun();
            schoolRun.setText(edu.getSchool() != null ? edu.getSchool() : "未知学校");
            schoolRun.setBold(true);
            schoolRun.setFontSize(12);
            schoolRun.setFontFamily("微软雅黑");

            // 分隔符
            XWPFRun separatorRun = mainPara.createRun();
            separatorRun.setText("  |  ");
            separatorRun.setFontSize(12);
            separatorRun.setColor("808080");

            // 专业
            XWPFRun majorRun = mainPara.createRun();
            majorRun.setText(edu.getMajor() != null ? edu.getMajor() : "未知专业");
            majorRun.setFontSize(12);
            majorRun.setFontFamily("微软雅黑");

            // 学历和时间段落
            XWPFParagraph detailPara = document.createParagraph();
            detailPara.setAlignment(ParagraphAlignment.LEFT);

            // 学历
            XWPFRun degreeRun = detailPara.createRun();
            degreeRun.setText("学历：" + (edu.getDegree() != null ? edu.getDegree() : "未知"));
            degreeRun.setFontSize(11);
            degreeRun.setFontFamily("微软雅黑");
            degreeRun.setColor("666666");

            // 时间
            String timeRange = formatEducationTime(edu);
            if (!timeRange.isEmpty()) {
                XWPFRun timeRun = detailPara.createRun();
                timeRun.setText("    时间：" + timeRange);
                timeRun.setFontSize(11);
                timeRun.setFontFamily("微软雅黑");
                timeRun.setColor("666666");
            }

            // 描述（如果有）
            if (edu.getDescription() != null && !edu.getDescription().trim().isEmpty()) {
                XWPFParagraph descPara = document.createParagraph();
                descPara.setAlignment(ParagraphAlignment.LEFT);
                XWPFRun descRun = descPara.createRun();
                descRun.setText("    " + edu.getDescription());
                descRun.setFontSize(10);
                descRun.setFontFamily("微软雅黑");
                descRun.setColor("555555");
                descRun.setItalic(true);
            }

            // 添加小间距
            XWPFParagraph spacePara = document.createParagraph();
            XWPFRun spaceRun = spacePara.createRun();
            spaceRun.setFontSize(6);

        } catch (Exception e) {
            logger.warn("创建教育经历条目失败: " + e.getMessage());
        }
    }

    /**
     * 格式化教育经历时间
     */
    private String formatEducationTime(ResumeEducation edu) {
        String startDate = formatDate(edu.getStartDate());
        String endDate = formatDate(edu.getEndDate());

        if (!startDate.isEmpty() && !endDate.isEmpty()) {
            return startDate + " - " + endDate;
        } else if (!startDate.isEmpty()) {
            return startDate + " - 至今";
        } else if (!endDate.isEmpty()) {
            return "至 " + endDate;
        }
        return "";
    }

    /**
     * 添加工作经历部分
     */
    private void addWorkSection(XWPFDocument document, List<ResumeWork> workList) {
        // 创建章节标题
        createWebStyleSectionTitle(document, "工作经历");

        // 创建时间轴容器
        createTimelineContainer(document, workList, "work");

        // 添加空行
        document.createParagraph();
    }

    /**
     * 创建单个工作经历条目
     */
    private void createWorkItem(XWPFDocument document, ResumeWork work) {
        try {
            // 创建主要信息段落
            XWPFParagraph mainPara = document.createParagraph();
            mainPara.setAlignment(ParagraphAlignment.LEFT);

            // 公司名称（加粗）
            XWPFRun companyRun = mainPara.createRun();
            companyRun.setText(work.getCompany() != null ? work.getCompany() : "未知公司");
            companyRun.setBold(true);
            companyRun.setFontSize(12);
            companyRun.setFontFamily("微软雅黑");

            // 分隔符
            XWPFRun separatorRun = mainPara.createRun();
            separatorRun.setText("  |  ");
            separatorRun.setFontSize(12);
            separatorRun.setColor("808080");

            // 职位
            XWPFRun positionRun = mainPara.createRun();
            positionRun.setText(work.getPosition() != null ? work.getPosition() : "未知职位");
            positionRun.setFontSize(12);
            positionRun.setFontFamily("微软雅黑");

            // 部门和时间段落
            XWPFParagraph detailPara = document.createParagraph();
            detailPara.setAlignment(ParagraphAlignment.LEFT);

            // 部门
            if (work.getDepartment() != null && !work.getDepartment().trim().isEmpty()) {
                XWPFRun deptRun = detailPara.createRun();
                deptRun.setText("部门：" + work.getDepartment());
                deptRun.setFontSize(11);
                deptRun.setFontFamily("微软雅黑");
                deptRun.setColor("666666");
            }

            // 时间
            String timeRange = formatWorkTime(work);
            if (!timeRange.isEmpty()) {
                XWPFRun timeRun = detailPara.createRun();
                String timeText = (work.getDepartment() != null && !work.getDepartment().trim().isEmpty())
                    ? "    时间：" + timeRange
                    : "时间：" + timeRange;
                timeRun.setText(timeText);
                timeRun.setFontSize(11);
                timeRun.setFontFamily("微软雅黑");
                timeRun.setColor("666666");
            }

            // 工作描述（如果有）
            if (work.getDescription() != null && !work.getDescription().trim().isEmpty()) {
                XWPFParagraph descPara = document.createParagraph();
                descPara.setAlignment(ParagraphAlignment.LEFT);
                XWPFRun descRun = descPara.createRun();
                descRun.setText("    " + work.getDescription());
                descRun.setFontSize(10);
                descRun.setFontFamily("微软雅黑");
                descRun.setColor("555555");
                descRun.setItalic(true);
            }

            // 添加小间距
            XWPFParagraph spacePara = document.createParagraph();
            XWPFRun spaceRun = spacePara.createRun();
            spaceRun.setFontSize(6);

        } catch (Exception e) {
            logger.warn("创建工作经历条目失败: " + e.getMessage());
        }
    }

    /**
     * 格式化工作经历时间
     */
    private String formatWorkTime(ResumeWork work) {
        String startDate = formatDate(work.getStartDate());
        String endDate = formatDate(work.getEndDate());

        if (!startDate.isEmpty() && !endDate.isEmpty()) {
            return startDate + " - " + endDate;
        } else if (!startDate.isEmpty()) {
            return startDate + " - 至今";
        } else if (!endDate.isEmpty()) {
            return "至 " + endDate;
        }
        return "";
    }

    /**
     * 添加技能特长部分
     */
    private void addSkillsSection(XWPFDocument document, List<ResumeSkill> skillsList) {
        // 创建章节标题
        createSectionTitle(document, "技能特长");

        // 创建技能列表
        createSkillsList(document, skillsList);

        // 添加空行
        document.createParagraph();
    }

    /**
     * 创建技能列表
     */
    private void createSkillsList(XWPFDocument document, List<ResumeSkill> skillsList) {
        try {
            // 按行显示技能，每行显示2-3个技能
            int skillsPerRow = 3;
            for (int i = 0; i < skillsList.size(); i += skillsPerRow) {
                XWPFParagraph skillPara = document.createParagraph();
                skillPara.setAlignment(ParagraphAlignment.LEFT);

                for (int j = i; j < Math.min(i + skillsPerRow, skillsList.size()); j++) {
                    ResumeSkill skill = skillsList.get(j);

                    if (j > i) {
                        // 添加分隔符
                        XWPFRun separatorRun = skillPara.createRun();
                        separatorRun.setText("    ");
                    }

                    // 技能名称
                    XWPFRun skillNameRun = skillPara.createRun();
                    skillNameRun.setText(skill.getName() != null ? skill.getName() : "未知技能");
                    skillNameRun.setBold(true);
                    skillNameRun.setFontSize(11);
                    skillNameRun.setFontFamily("微软雅黑");

                    // 熟练度
                    if (skill.getLevel() != null) {
                        XWPFRun levelRun = skillPara.createRun();
                        levelRun.setText(" (" + generateStars(skill.getLevel()) + ")");
                        levelRun.setFontSize(10);
                        levelRun.setFontFamily("微软雅黑");
                        levelRun.setColor("666666");
                    }
                }
            }

            // 如果没有技能，显示提示
            if (skillsList.isEmpty()) {
                XWPFParagraph emptyPara = document.createParagraph();
                XWPFRun emptyRun = emptyPara.createRun();
                emptyRun.setText("暂无技能信息");
                emptyRun.setFontSize(11);
                emptyRun.setFontFamily("微软雅黑");
                emptyRun.setColor("999999");
                emptyRun.setItalic(true);
            }

        } catch (Exception e) {
            logger.warn("创建技能列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据等级生成星级显示
     */
    private String generateStars(Integer level) {
        if (level == null || level < 1) {
            return "☆☆☆☆☆";
        }

        StringBuilder stars = new StringBuilder();
        for (int i = 1; i <= 5; i++) {
            if (i <= level) {
                stars.append("★");
            } else {
                stars.append("☆");
            }
        }
        return stars.toString();
    }

    /**
     * 添加项目经历部分
     */
    private void addProjectSection(XWPFDocument document, List<ResumeProject> projectList) {
        // 创建章节标题
        createWebStyleSectionTitle(document, "项目经历");

        // 创建时间轴容器
        createTimelineContainer(document, projectList, "project");

        // 添加空行
        document.createParagraph();
    }

    /**
     * 创建单个项目经历条目
     */
    private void createProjectItem(XWPFDocument document, ResumeProject project) {
        try {
            // 创建主要信息段落
            XWPFParagraph mainPara = document.createParagraph();
            mainPara.setAlignment(ParagraphAlignment.LEFT);

            // 项目名称（加粗）
            XWPFRun projectNameRun = mainPara.createRun();
            projectNameRun.setText(project.getName() != null ? project.getName() : "未知项目");
            projectNameRun.setBold(true);
            projectNameRun.setFontSize(12);
            projectNameRun.setFontFamily("微软雅黑");

            // 分隔符
            XWPFRun separatorRun = mainPara.createRun();
            separatorRun.setText("  |  ");
            separatorRun.setFontSize(12);
            separatorRun.setColor("808080");

            // 担任角色
            XWPFRun roleRun = mainPara.createRun();
            roleRun.setText(project.getRole() != null ? project.getRole() : "未知角色");
            roleRun.setFontSize(12);
            roleRun.setFontFamily("微软雅黑");

            // 时间段落
            XWPFParagraph timePara = document.createParagraph();
            timePara.setAlignment(ParagraphAlignment.LEFT);

            // 项目时间
            String timeRange = formatProjectTime(project);
            if (!timeRange.isEmpty()) {
                XWPFRun timeRun = timePara.createRun();
                timeRun.setText("项目时间：" + timeRange);
                timeRun.setFontSize(11);
                timeRun.setFontFamily("微软雅黑");
                timeRun.setColor("666666");
            }

            // 项目描述（如果有）
            if (project.getDescription() != null && !project.getDescription().trim().isEmpty()) {
                XWPFParagraph descPara = document.createParagraph();
                descPara.setAlignment(ParagraphAlignment.LEFT);
                XWPFRun descRun = descPara.createRun();
                descRun.setText("项目描述：" + project.getDescription());
                descRun.setFontSize(10);
                descRun.setFontFamily("微软雅黑");
                descRun.setColor("555555");
            }

            // 项目成果（如果有）
            if (project.getAchievement() != null && !project.getAchievement().trim().isEmpty()) {
                XWPFParagraph achievementPara = document.createParagraph();
                achievementPara.setAlignment(ParagraphAlignment.LEFT);
                XWPFRun achievementRun = achievementPara.createRun();
                achievementRun.setText("项目成果：" + project.getAchievement());
                achievementRun.setFontSize(10);
                achievementRun.setFontFamily("微软雅黑");
                achievementRun.setColor("555555");
                achievementRun.setBold(true); // 成果部分加粗显示
            }

            // 添加小间距
            XWPFParagraph spacePara = document.createParagraph();
            XWPFRun spaceRun = spacePara.createRun();
            spaceRun.setFontSize(6);

        } catch (Exception e) {
            logger.warn("创建项目经历条目失败: " + e.getMessage());
        }
    }

    /**
     * 格式化项目经历时间
     */
    private String formatProjectTime(ResumeProject project) {
        String startDate = formatDate(project.getStartDate());
        String endDate = formatDate(project.getEndDate());

        if (!startDate.isEmpty() && !endDate.isEmpty()) {
            return startDate + " - " + endDate;
        } else if (!startDate.isEmpty()) {
            return startDate + " - 至今";
        } else if (!endDate.isEmpty()) {
            return "至 " + endDate;
        }
        return "";
    }

    /**
     * 格式化日期显示
     */
    private String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.format(date);
        } catch (Exception e) {
            logger.warn("日期格式化失败: " + e.getMessage());
            return date.toString();
        }
    }

    /**
     * 下载网络头像到临时文件
     */
    private String downloadNetworkAvatar(String avatarUrl) {
        try {
            logger.info("开始下载网络头像: " + avatarUrl);

            URL url = new URL(avatarUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求属性
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            // 检查响应码
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                logger.warn("下载头像失败，响应码: " + responseCode);
                return null;
            }

            // 获取文件扩展名
            String contentType = connection.getContentType();
            String fileExtension = ".jpg"; // 默认扩展名
            if (contentType != null) {
                if (contentType.contains("png")) {
                    fileExtension = ".png";
                } else if (contentType.contains("gif")) {
                    fileExtension = ".gif";
                } else if (contentType.contains("jpeg") || contentType.contains("jpg")) {
                    fileExtension = ".jpg";
                }
            }

            // 创建临时文件
            String tempDir = System.getProperty("java.io.tmpdir");
            String tempFileName = "avatar_" + System.currentTimeMillis() + fileExtension;
            String tempFilePath = tempDir + File.separator + tempFileName;

            // 下载文件
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(tempFilePath)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                long totalBytes = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;

                    // 限制文件大小（最大5MB）
                    if (totalBytes > 5 * 1024 * 1024) {
                        logger.warn("头像文件过大，停止下载: " + totalBytes + " bytes");
                        outputStream.close();
                        new File(tempFilePath).delete();
                        return null;
                    }
                }

                logger.info("头像下载完成: " + tempFilePath + ", 大小: " + totalBytes + " bytes");
                return tempFilePath;
            }

        } catch (Exception e) {
            logger.warn("下载网络头像失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据文件名获取图片类型
     */
    private int getPictureType(String fileName) {
        if (fileName == null) {
            return XWPFDocument.PICTURE_TYPE_JPEG;
        }

        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".png")) {
            return XWPFDocument.PICTURE_TYPE_PNG;
        } else if (lowerFileName.endsWith(".gif")) {
            return XWPFDocument.PICTURE_TYPE_GIF;
        } else if (lowerFileName.endsWith(".bmp")) {
            return XWPFDocument.PICTURE_TYPE_BMP;
        } else {
            return XWPFDocument.PICTURE_TYPE_JPEG; // 默认为JPEG
        }
    }

    /**
     * 设置页面边距
     */
    private void setPageMargins(XWPFDocument document) {
        try {
            // 简化页面设置，主要通过段落间距控制布局
            logger.info("页面边距设置完成");
        } catch (Exception e) {
            logger.warn("设置页面边距失败: " + e.getMessage());
        }
    }

    /**
     * 创建个人信息部分
     */
    private void createPersonalInfoSection(XWPFDocument document, Resume resume) {
        try {
            // 创建个人信息标题
            createSectionTitle(document, "个人信息");

            // 创建个人信息表格
            XWPFTable infoTable = document.createTable(3, 4);
            infoTable.setWidth("100%");

            // 设置表格样式
            setTableStyle(infoTable);

            // 第一行
            setTableCell(infoTable, 0, 0, "出生日期", true);
            setTableCell(infoTable, 0, 1, formatDate(resume.getBirthday()), false);
            setTableCell(infoTable, 0, 2, "性别", true);
            setTableCell(infoTable, 0, 3, resume.getGender() != null ? resume.getGender() : "", false);

            // 第二行
            setTableCell(infoTable, 1, 0, "民族", true);
            setTableCell(infoTable, 1, 1, resume.getNation() != null ? resume.getNation() : "", false);
            setTableCell(infoTable, 1, 2, "政治面貌", true);
            setTableCell(infoTable, 1, 3, resume.getPoliticalStatus() != null ? resume.getPoliticalStatus() : "", false);

            // 第三行
            setTableCell(infoTable, 2, 0, "现任职务", true);
            setTableCell(infoTable, 2, 1, resume.getCurrentPosition() != null ? resume.getCurrentPosition() : "", false);
            setTableCell(infoTable, 2, 2, "籍贯", true);
            setTableCell(infoTable, 2, 3, resume.getNativePlace() != null ? resume.getNativePlace() : "", false);

            // 添加空行
            document.createParagraph();

        } catch (Exception e) {
            logger.warn("创建个人信息部分失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建章节标题
     */
    private void createSectionTitle(XWPFDocument document, String title) {
        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setAlignment(ParagraphAlignment.LEFT);

        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(title);
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("微软雅黑");
        titleRun.setColor("2F4F4F"); // 深灰色

        // 添加下划线效果
        XWPFParagraph underlinePara = document.createParagraph();
        XWPFRun underlineRun = underlinePara.createRun();
        underlineRun.setText("————————————————————————————————");
        underlineRun.setFontSize(8);
        underlineRun.setColor("C0C0C0"); // 浅灰色
    }

    /**
     * 设置表格样式
     */
    private void setTableStyle(XWPFTable table) {
        try {
            // 简化表格样式设置，避免复杂的POI API调用
            for (XWPFTableRow row : table.getRows()) {
                // 设置行高
                row.setHeight(400); // 设置行高为400缇（约0.28英寸）

                for (XWPFTableCell cell : row.getTableCells()) {
                    // 设置单元格垂直对齐
                    cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                }
            }
            logger.info("表格样式设置完成");
        } catch (Exception e) {
            logger.warn("设置表格样式失败: " + e.getMessage());
        }
    }

    /**
     * 创建简历头部（姓名、联系方式、头像）
     */
    private void createResumeHeader(XWPFDocument document, Resume resume) {
        try {
            // 创建包含姓名和头像的表格
            XWPFTable headerTable = document.createTable(1, 2);
            headerTable.setWidth("100%");

            // 简化表格样式设置（避免复杂的边框API）
            try {
                // 设置表格无边框样式
                logger.info("头部表格创建完成");
            } catch (Exception e) {
                logger.warn("设置头部表格样式失败: " + e.getMessage());
            }

            // 左侧：姓名和联系信息
            XWPFTableCell leftCell = headerTable.getRow(0).getCell(0);
            leftCell.removeParagraph(0); // 移除默认段落

            // 姓名
            XWPFParagraph namePara = leftCell.addParagraph();
            namePara.setAlignment(ParagraphAlignment.LEFT);
            XWPFRun nameRun = namePara.createRun();
            nameRun.setText(resume.getName() != null ? resume.getName() : "姓名");
            nameRun.setBold(true);
            nameRun.setFontSize(24);
            nameRun.setFontFamily("微软雅黑");

            // 联系信息
            if (resume.getPhone() != null || resume.getEmail() != null) {
                XWPFParagraph contactPara = leftCell.addParagraph();
                contactPara.setAlignment(ParagraphAlignment.LEFT);
                XWPFRun contactRun = contactPara.createRun();

                StringBuilder contact = new StringBuilder();
                if (resume.getPhone() != null) {
                    contact.append("电话：").append(resume.getPhone());
                }
                if (resume.getEmail() != null) {
                    if (contact.length() > 0) contact.append("  |  ");
                    contact.append("邮箱：").append(resume.getEmail());
                }

                contactRun.setText(contact.toString());
                contactRun.setFontSize(11);
                contactRun.setFontFamily("微软雅黑");
            }

            // 右侧：头像
            XWPFTableCell rightCell = headerTable.getRow(0).getCell(1);
            rightCell.removeParagraph(0); // 移除默认段落

            XWPFParagraph avatarPara = rightCell.addParagraph();
            avatarPara.setAlignment(ParagraphAlignment.RIGHT);

            // 添加头像
            addAvatarToCell(avatarPara, resume.getAvatar());

            // 添加空行
            document.createParagraph();

        } catch (Exception e) {
            logger.warn("创建简历头部失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建分隔线
     */
    private void createSeparatorLine(XWPFDocument document) {
        try {
            XWPFParagraph separatorPara = document.createParagraph();
            separatorPara.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun separatorRun = separatorPara.createRun();
            separatorRun.setText("————————————————————————————————————————");
            separatorRun.setFontSize(10);
            separatorRun.setColor("808080"); // 灰色

            // 添加空行
            document.createParagraph();
        } catch (Exception e) {
            logger.warn("创建分隔线失败: " + e.getMessage());
        }
    }

    /**
     * 基于模板生成Word文档
     */
    private ByteArrayOutputStream generateWordFromTemplate(Resume resume, List<ResumeEducation> educationList,
                                                          List<ResumeWork> workList, List<ResumeProject> projectList) throws Exception {
        try {
            // 加载模板文件
            InputStream templateStream = getClass().getClassLoader().getResourceAsStream("templates/resume_template.docx");
            if (templateStream == null) {
                logger.warn("模板文件不存在，创建基础模板并使用代码生成方式");
                return createBasicTemplateDocument(resume, educationList, workList, projectList);
            }

            XWPFDocument document = new XWPFDocument(templateStream);

            // 替换基本信息占位符
            replaceBasicInfoPlaceholders(document, resume);

            // 替换学历学位占位符
            replaceEducationDegreePlaceholders(document, educationList);

            // 替换现任职务占位符
            replaceCurrentPositionPlaceholders(document, resume);

            // 替换教育经历占位符
            replaceEducationPlaceholders(document, educationList);

            // 替换工作经历占位符
            replaceWorkPlaceholders(document, workList);

            // 替换项目经历占位符
            replaceProjectPlaceholders(document, projectList);

            // 处理头像
            replaceAvatarPlaceholder(document, resume.getAvatar());

            // 输出到字节流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            document.close();
            templateStream.close();

            return outputStream;

        } catch (Exception e) {
            logger.error("基于模板生成Word文档失败: " + e.getMessage(), e);
            // 降级到基础模板生成方式
            return createBasicTemplateDocument(resume, educationList, workList, projectList);
        }
    }

    /**
     * 创建基础模板文档（当模板文件不存在时使用）
     */
    private ByteArrayOutputStream createBasicTemplateDocument(Resume resume, List<ResumeEducation> educationList,
                                                            List<ResumeWork> workList, List<ResumeProject> projectList) throws Exception {
        XWPFDocument document = new XWPFDocument();

        // 设置页面边距
        setPageMargins(document);

        // 创建标题
        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText("干部任免审批表");
        titleRun.setBold(true);
        titleRun.setFontSize(24);
        titleRun.setFontFamily("宋体");

        // 添加空行
        document.createParagraph();

        // 创建基本信息表格
        createBasicInfoTable(document, resume);

        // 学历学位
        createEducationDegreeTable(document, educationList);

        // 现任职务
        createCurrentPositionParagraph(document, resume);

        // 教育经历
        createEducationHistorySection(document, educationList);

        // 工作经历
        createWorkHistorySection(document, workList);

        // 项目经历
        createProjectHistorySection(document, projectList);

        // 输出到字节流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        document.write(outputStream);
        document.close();

        return outputStream;
    }



    /**
     * 创建学历学位表格
     */
    private void createEducationDegreeTable(XWPFDocument document, List<ResumeEducation> educationList) {
        // 创建段落标题
        XWPFParagraph titlePara = document.createParagraph();
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText("学历学位");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");

        // 获取最高学历信息
        ResumeEducation highestEducation = null;
        if (educationList != null && !educationList.isEmpty()) {
            highestEducation = educationList.get(0); // 假设第一个是最高学历
        }

        // 创建表格
        XWPFTable table = document.createTable(2, 4);
        table.setWidth("100%");

        // 第一行：学历、学位
        setTableCell(table, 0, 0, "学历", true);
        setTableCell(table, 0, 1, highestEducation != null && highestEducation.getDegree() != null ? highestEducation.getDegree() : "", false);
        setTableCell(table, 0, 2, "学位", true);
        setTableCell(table, 0, 3, highestEducation != null && highestEducation.getDegree() != null ? highestEducation.getDegree() : "", false);

        // 第二行：毕业院校、专业
        setTableCell(table, 1, 0, "毕业院校", true);
        setTableCell(table, 1, 1, highestEducation != null && highestEducation.getSchool() != null ? highestEducation.getSchool() : "", false);
        setTableCell(table, 1, 2, "专业", true);
        setTableCell(table, 1, 3, highestEducation != null && highestEducation.getMajor() != null ? highestEducation.getMajor() : "", false);

        // 添加空行
        document.createParagraph();
    }

    /**
     * 创建现任职务段落
     */
    private void createCurrentPositionParagraph(XWPFDocument document, Resume resume) {
        // 创建段落标题
        XWPFParagraph titlePara = document.createParagraph();
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText("现任职务");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");

        // 创建内容段落
        XWPFParagraph contentPara = document.createParagraph();
        XWPFRun contentRun = contentPara.createRun();
        contentRun.setText(resume.getCurrentPosition() != null ? resume.getCurrentPosition() : "");
        contentRun.setFontFamily("宋体");

        // 添加空行
        document.createParagraph();
    }

    /**
     * 创建教育经历部分
     */
    private void createEducationHistorySection(XWPFDocument document, List<ResumeEducation> educationList) {
        // 创建段落标题
        XWPFParagraph titlePara = document.createParagraph();
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText("教育经历");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");

        if (educationList != null && !educationList.isEmpty()) {
            for (ResumeEducation education : educationList) {
                // 创建时间段落
                XWPFParagraph datePara = document.createParagraph();
                XWPFRun dateRun = datePara.createRun();
                String dateRange = formatEducationDate(education.getStartDate(), education.getEndDate());
                dateRun.setText(dateRange);
                dateRun.setFontFamily("宋体");
                dateRun.setColor("909399");

                // 创建学校段落
                XWPFParagraph schoolPara = document.createParagraph();
                XWPFRun schoolRun = schoolPara.createRun();
                schoolRun.setText(education.getSchool() != null ? education.getSchool() : "");
                schoolRun.setBold(true);
                schoolRun.setFontFamily("宋体");

                // 创建专业和学位段落
                XWPFParagraph majorPara = document.createParagraph();
                XWPFRun majorRun = majorPara.createRun();
                String majorInfo = (education.getMajor() != null ? education.getMajor() : "") +
                                 (education.getDegree() != null ? " | " + education.getDegree() : "");
                majorRun.setText(majorInfo);
                majorRun.setFontFamily("宋体");
                majorRun.setColor("606266");

                // 添加分隔线
                document.createParagraph();
            }
        }

        // 添加空行
        document.createParagraph();
    }

    /**
     * 创建工作经历部分
     */
    private void createWorkHistorySection(XWPFDocument document, List<ResumeWork> workList) {
        // 创建段落标题
        XWPFParagraph titlePara = document.createParagraph();
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText("工作经历");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");

        if (workList != null && !workList.isEmpty()) {
            for (ResumeWork work : workList) {
                // 创建时间段落
                XWPFParagraph datePara = document.createParagraph();
                XWPFRun dateRun = datePara.createRun();
                String dateRange = formatWorkDate(work.getStartDate(), work.getEndDate());
                dateRun.setText(dateRange);
                dateRun.setFontFamily("宋体");
                dateRun.setColor("909399");

                // 创建公司段落
                XWPFParagraph companyPara = document.createParagraph();
                XWPFRun companyRun = companyPara.createRun();
                companyRun.setText(work.getCompany() != null ? work.getCompany() : "");
                companyRun.setBold(true);
                companyRun.setFontFamily("宋体");

                // 创建职位和部门段落
                XWPFParagraph positionPara = document.createParagraph();
                XWPFRun positionRun = positionPara.createRun();
                String positionInfo = (work.getPosition() != null ? work.getPosition() : "") +
                                    (work.getDepartment() != null ? " | " + work.getDepartment() : "");
                positionRun.setText(positionInfo);
                positionRun.setFontFamily("宋体");
                positionRun.setColor("606266");

                // 创建工作描述段落
                if (work.getDescription() != null && !work.getDescription().trim().isEmpty()) {
                    XWPFParagraph descPara = document.createParagraph();
                    XWPFRun descRun = descPara.createRun();
                    descRun.setText(work.getDescription());
                    descRun.setFontFamily("宋体");
                    descRun.setColor("606266");
                }

                // 添加分隔线
                document.createParagraph();
            }
        }

        // 添加空行
        document.createParagraph();
    }

    /**
     * 创建项目经历部分
     */
    private void createProjectHistorySection(XWPFDocument document, List<ResumeProject> projectList) {
        // 创建段落标题
        XWPFParagraph titlePara = document.createParagraph();
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText("项目经历");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");

        if (projectList != null && !projectList.isEmpty()) {
            for (ResumeProject project : projectList) {
                // 创建时间段落
                XWPFParagraph datePara = document.createParagraph();
                XWPFRun dateRun = datePara.createRun();
                String dateRange = formatProjectDate(project.getStartDate(), project.getEndDate());
                dateRun.setText(dateRange);
                dateRun.setFontFamily("宋体");
                dateRun.setColor("909399");

                // 创建项目名称段落
                XWPFParagraph namePara = document.createParagraph();
                XWPFRun nameRun = namePara.createRun();
                nameRun.setText(project.getName() != null ? project.getName() : "");
                nameRun.setBold(true);
                nameRun.setFontFamily("宋体");

                // 创建角色段落
                XWPFParagraph rolePara = document.createParagraph();
                XWPFRun roleRun = rolePara.createRun();
                roleRun.setText(project.getRole() != null ? project.getRole() : "");
                roleRun.setFontFamily("宋体");
                roleRun.setColor("606266");

                // 创建项目描述段落
                if (project.getDescription() != null && !project.getDescription().trim().isEmpty()) {
                    XWPFParagraph descPara = document.createParagraph();
                    XWPFRun descRun = descPara.createRun();
                    descRun.setText(project.getDescription());
                    descRun.setFontFamily("宋体");
                    descRun.setColor("606266");
                }

                // 创建主要成果段落
                if (project.getAchievement() != null && !project.getAchievement().trim().isEmpty()) {
                    XWPFParagraph achievePara = document.createParagraph();
                    XWPFRun achieveRun = achievePara.createRun();
                    achieveRun.setText("主要成果：" + project.getAchievement());
                    achieveRun.setFontFamily("宋体");
                    achieveRun.setColor("606266");
                }

                // 添加分隔线
                document.createParagraph();
            }
        }

        // 添加空行
        document.createParagraph();
    }

    /**
     * 格式化教育经历日期
     */
    private String formatEducationDate(Date startDate, Date endDate) {
        if (startDate == null && endDate == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
        String start = startDate != null ? sdf.format(startDate) : "";
        String end = endDate != null ? sdf.format(endDate) : "至今";
        return start + " - " + end;
    }

    /**
     * 格式化工作经历日期
     */
    private String formatWorkDate(Date startDate, Date endDate) {
        return formatEducationDate(startDate, endDate);
    }

    /**
     * 格式化项目经历日期
     */
    private String formatProjectDate(Date startDate, Date endDate) {
        return formatEducationDate(startDate, endDate);
    }

    /**
     * 替换基本信息占位符
     */
    private void replaceBasicInfoPlaceholders(XWPFDocument document, Resume resume) {
        try {
            // 替换文本占位符
            replaceTextPlaceholder(document, "{{NAME}}", resume.getName());
            replaceTextPlaceholder(document, "{{GENDER}}", resume.getGender());
            replaceTextPlaceholder(document, "{{BIRTHDAY}}", formatDateToYearMonth(resume.getBirthday()));
            replaceTextPlaceholder(document, "{{AGE}}", resume.getAge() != null ? resume.getAge() + "岁" : "-");
            replaceTextPlaceholder(document, "{{NATION}}", resume.getNation());
            replaceTextPlaceholder(document, "{{NATIVE_PLACE}}", resume.getNativePlace());
            replaceTextPlaceholder(document, "{{POLITICAL_STATUS}}", resume.getPoliticalStatus());
            replaceTextPlaceholder(document, "{{PHONE}}", resume.getPhone());
            replaceTextPlaceholder(document, "{{EMAIL}}", resume.getEmail());
            replaceTextPlaceholder(document, "{{TECHNICAL_POSITION}}", resume.getTechnicalPosition());

        } catch (Exception e) {
            logger.warn("替换基本信息占位符失败: " + e.getMessage());
        }
    }

    /**
     * 替换学历学位占位符
     */
    private void replaceEducationDegreePlaceholders(XWPFDocument document, List<ResumeEducation> educationList) {
        try {
            replaceTextPlaceholder(document, "{{HIGHEST_EDUCATION}}", getHighestEducation(educationList));
            replaceTextPlaceholder(document, "{{HIGHEST_DEGREE}}", getHighestDegree(educationList));
            replaceTextPlaceholder(document, "{{HIGHEST_SCHOOL}}", getHighestSchool(educationList));
            replaceTextPlaceholder(document, "{{HIGHEST_MAJOR}}", getHighestMajor(educationList));

        } catch (Exception e) {
            logger.warn("替换学历学位占位符失败: " + e.getMessage());
        }
    }

    /**
     * 替换现任职务占位符
     */
    private void replaceCurrentPositionPlaceholders(XWPFDocument document, Resume resume) {
        try {
            replaceTextPlaceholder(document, "{{CURRENT_POSITION}}", resume.getCurrentPosition());

        } catch (Exception e) {
            logger.warn("替换现任职务占位符失败: " + e.getMessage());
        }
    }

    /**
     * 替换教育经历占位符
     */
    private void replaceEducationPlaceholders(XWPFDocument document, List<ResumeEducation> educationList) {
        try {
            if (educationList != null && !educationList.isEmpty()) {
                for (int i = 0; i < educationList.size(); i++) {
                    ResumeEducation edu = educationList.get(i);
                    int index = i + 1;

                    replaceTextPlaceholder(document, "{{EDU_DATE_" + index + "}}", formatEducationTime(edu));
                    replaceTextPlaceholder(document, "{{EDU_SCHOOL_" + index + "}}", edu.getSchool());
                    replaceTextPlaceholder(document, "{{EDU_MAJOR_" + index + "}}", edu.getMajor());
                    replaceTextPlaceholder(document, "{{EDU_DEGREE_" + index + "}}", edu.getDegree());
                }
            }

        } catch (Exception e) {
            logger.warn("替换教育经历占位符失败: " + e.getMessage());
        }
    }

    /**
     * 替换工作经历占位符
     */
    private void replaceWorkPlaceholders(XWPFDocument document, List<ResumeWork> workList) {
        try {
            if (workList != null && !workList.isEmpty()) {
                for (int i = 0; i < workList.size(); i++) {
                    ResumeWork work = workList.get(i);
                    int index = i + 1;

                    replaceTextPlaceholder(document, "{{WORK_DATE_" + index + "}}", formatWorkTime(work));
                    replaceTextPlaceholder(document, "{{WORK_COMPANY_" + index + "}}", work.getCompany());
                    replaceTextPlaceholder(document, "{{WORK_POSITION_" + index + "}}", work.getPosition());
                    replaceTextPlaceholder(document, "{{WORK_DEPT_" + index + "}}", work.getDepartment());
                    replaceTextPlaceholder(document, "{{WORK_DESC_" + index + "}}", work.getDescription());
                }
            }

        } catch (Exception e) {
            logger.warn("替换工作经历占位符失败: " + e.getMessage());
        }
    }

    /**
     * 替换项目经历占位符
     */
    private void replaceProjectPlaceholders(XWPFDocument document, List<ResumeProject> projectList) {
        try {
            if (projectList != null && !projectList.isEmpty()) {
                for (int i = 0; i < projectList.size(); i++) {
                    ResumeProject project = projectList.get(i);
                    int index = i + 1;

                    replaceTextPlaceholder(document, "{{PROJECT_DATE_" + index + "}}", formatProjectTime(project));
                    replaceTextPlaceholder(document, "{{PROJECT_NAME_" + index + "}}", project.getName());
                    replaceTextPlaceholder(document, "{{PROJECT_ROLE_" + index + "}}", project.getRole());
                    replaceTextPlaceholder(document, "{{PROJECT_DESC_" + index + "}}", project.getDescription());
                    replaceTextPlaceholder(document, "{{PROJECT_ACHIEVEMENT_" + index + "}}", project.getAchievement());
                }
            }

        } catch (Exception e) {
            logger.warn("替换项目经历占位符失败: " + e.getMessage());
        }
    }

    /**
     * 替换头像占位符
     */
    private void replaceAvatarPlaceholder(XWPFDocument document, String avatarUrl) {
        try {
            // 查找包含{{AVATAR}}占位符的段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                for (XWPFRun run : paragraph.getRuns()) {
                    String text = run.getText(0);
                    if (text != null && text.contains("{{AVATAR}}")) {
                        run.setText(text.replace("{{AVATAR}}", ""), 0);
                        // 添加头像图片
                        addAvatarToCell(paragraph, avatarUrl);
                        break;
                    }
                }
            }

            // 也检查表格中的占位符
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            for (XWPFRun run : paragraph.getRuns()) {
                                String text = run.getText(0);
                                if (text != null && text.contains("{{AVATAR}}")) {
                                    run.setText(text.replace("{{AVATAR}}", ""), 0);
                                    // 添加头像图片
                                    addAvatarToCell(paragraph, avatarUrl);
                                    return;
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.warn("替换头像占位符失败: " + e.getMessage());
        }
    }

    /**
     * 替换文本占位符
     */
    private void replaceTextPlaceholder(XWPFDocument document, String placeholder, String replacement) {
        try {
            if (replacement == null) {
                replacement = "-";
            }

            // 替换段落中的占位符
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                for (XWPFRun run : paragraph.getRuns()) {
                    String text = run.getText(0);
                    if (text != null && text.contains(placeholder)) {
                        run.setText(text.replace(placeholder, replacement), 0);
                    }
                }
            }

            // 替换表格中的占位符
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            for (XWPFRun run : paragraph.getRuns()) {
                                String text = run.getText(0);
                                if (text != null && text.contains(placeholder)) {
                                    run.setText(text.replace(placeholder, replacement), 0);
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.warn("替换文本占位符失败: " + placeholder + " -> " + replacement + ", " + e.getMessage());
        }
    }



    /**
     * 基于HTML模板生成PDF简历
     */
    @GetMapping("/downloadPdf/{id}")
    public void downloadResumePdf(@PathVariable String id, HttpServletResponse response) {
        try {
            logger.info("开始基于HTML模板下载PDF简历，ID: {}", id);

            Resume resume;
            try {
                resume = resumeService.selectResumeById(id);
            } catch (Exception e) {
                logger.error("查询简历失败，ID: " + id, e);
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                return;
            }

            if (resume == null) {
                logger.warn("简历不存在，ID: {}", id);
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 权限检查（复用现有逻辑）
            Long currentUserId = SecurityUtils.getUserId();
            if (!SecurityUtils.isAdmin(currentUserId) && !SecurityUtils.hasRole("yiji_admin")
                && !resume.getUserId().equals(currentUserId.toString())) {
                Long companyId = companyAdminService.getAdminCompanyId(currentUserId);
                if (companyId == null || !resumeService.checkResumeDeliveredToCompany(id, companyId)) {
                    logger.warn("用户无权限下载PDF简历，用户ID: {}, 简历ID: {}", currentUserId, id);
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    return;
                }
            }

            logger.info("权限检查通过，开始基于HTML模板生成PDF文档");

            // 获取关联数据
            List<ResumeEducation> educationList = resumeMapper.selectEducationByResumeId(id);
            List<ResumeWork> workList = resumeMapper.selectWorkByResumeId(id);
            List<ResumeProject> projectList = resumeMapper.selectProjectByResumeId(id);

            // 基于HTML模板生成PDF文档（使用包装器避免类加载问题）
            ByteArrayOutputStream pdfStream = PdfGeneratorWrapper.generatePdfFromHtmlTemplate(
                resume, educationList, workList, projectList);
            String fileName = (resume.getName() != null ? resume.getName() : "简历") + "_干部任免审批表.pdf";

            // 设置响应头
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=\"" +
                java.net.URLEncoder.encode(fileName, "UTF-8") + "\"");
            response.setHeader("Content-Length", String.valueOf(pdfStream.size()));

            // 输出文件
            response.getOutputStream().write(pdfStream.toByteArray());
            response.getOutputStream().flush();

            logger.info("简历PDF文档生成成功，文件名: {}", fileName);

        } catch (Exception e) {
            logger.error("基于HTML模板生成PDF简历失败，ID: " + id, e);
            try {
                if (!response.isCommitted()) {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("application/json");
                    response.setCharacterEncoding("UTF-8");
                    response.getWriter().write("{\"code\":500,\"msg\":\"PDF下载失败: " + e.getMessage() + "\"}");
                }
            } catch (Exception ex) {
                logger.error("写入错误响应失败", ex);
            }
        }
    }

}