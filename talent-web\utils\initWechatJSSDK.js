const url = "http://60.188.244.173:18080" + "/qywx/signature?url=http://qw.93co.com/";
export function getWxSignature(context, jsApiList) {
    console.log("url：", url + location.href.split('#')[0]);

    uni.request({
        url: url, //当前页面路劲
        method: 'GET',
        success: res => init(context, res, jsApiList),
        fail: () => context.message.error("获取微信签名失败")
    });
}
// 接口列表
export const wxapis = {
    scanQRCode: "scanQRCode"
}

function init(context, e, jsApiList) {
    const data = e.data;
    console.log("---：", data);
    context.$wx.config({
        debug: false,
        appId: data.appId,
        timestamp: data.timestamp,
        nonceStr: data.nonceStr,
        signature: data.signature,
        jsApiList: jsApiList
    });
    context.$wx.ready(function() {
        console.log("接口开通成功");
    });
}