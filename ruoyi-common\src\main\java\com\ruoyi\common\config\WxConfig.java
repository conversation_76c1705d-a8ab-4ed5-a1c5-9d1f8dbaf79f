package com.ruoyi.common.config;

import lombok.Data;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
@ConfigurationProperties(prefix = "wechat.cp")
@Data
public class WxConfig {
    
    private String corpId;
    private Map<String, AppConfig> apps;
    private static final String APP_AGENT_PREFIX = "wx";
    
    // 缓存不同应用的WxCpService实例
    private final Map<String, WxCpService> serviceMap = new ConcurrentHashMap<>();

    @Data
    public static class AppConfig {
        private String agentId;
        private String secret;
    }

    /**
     * 获取指定应用的WxCpService
     * @param agentId 应用ID
     * @return WxCpService实例
     */
    public WxCpService getWxCpService(String agentId) {
        return serviceMap.computeIfAbsent(agentId, key -> {
            AppConfig appConfig = apps.get(key);
            if (appConfig == null) {
                throw new RuntimeException("未找到agentId为" + key + "的应用配置");
            }
            
            WxCpService wxCpService = new WxCpServiceImpl();
            WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
            config.setAgentId(Integer.parseInt(appConfig.getAgentId()));
            config.setCorpSecret(appConfig.getSecret());
            config.setCorpId(corpId);
            wxCpService.setWxCpConfigStorage(config);
            return wxCpService;
        });
    }
}
