package com.ruoyi.web.controller.renzi;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.config.WxConfig;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.MobileLoginService;
import com.ruoyi.framework.web.service.QywxService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysUserEnterpriseService;
import com.ruoyi.system.service.ISysUserService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpOAuth2Service;
import me.chanjar.weixin.cp.api.WxCpUserService;
import me.chanjar.weixin.cp.api.impl.WxCpOAuth2ServiceImpl;
import me.chanjar.weixin.cp.api.impl.WxCpUserServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.bean.WxCpUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.spring.web.json.Json;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/ws")
public class RenziController {
    private static final Logger log = LoggerFactory.getLogger(TokenService.class);
    @Autowired
    private QywxService qywxService;
    @Resource
    private WxConfig wxConfig;

    private WxCpOAuth2Service auth2Service = null;

    private WxCpUserService wxUserService = null;

    @Autowired(required = false)
    private MobileLoginService loginService;

    @Autowired
    private ISysUserEnterpriseService userEnterpriseServiceImpl;

    @Autowired
    private ISysUserService userService;
    @Autowired
    private TokenService tokenService;
    @Value("${token.secret}")
    private String secret;
    @Autowired
    private RedisCache redisCache;

    private static final String hz_token  = "px1ivx2zv3cvlqoimuliyl";
    private static final String hz_id  = "10000";


    //获取第三方应用token
    @GetMapping(value = "/HXR/GetAccessTokenByID")
    public AjaxResult GetAccessTokenByID(@RequestParam("id") String id,@RequestParam("token") String token) {

        AjaxResult ajax = AjaxResult.success();

        if (hz_id.equals(id) && hz_token.equals(token)){
            ajax.put("code","1");
            ajax.put("content","");
            ajax.put("message","操作成功");
            ajax.put("expirestime","");
            System.out.println("访问WxJsapiSignature=====/system/qywx/signature/" + id + "");
            return ajax;
        }
        ajax.put("code","0");
        ajax.put("content","");
        ajax.put("message","获取ACCESSTOKEN报错。");
        ajax.put("expirestime","");
        return ajax;
    }
    //获取企业微信用户userId
    @GetMapping("/Basic/SyncHRInformation")
    public AjaxResult SyncHRInformation(@RequestBody Map<String, Object> params)throws Exception {

        List<Map<String, Object>> dataList = (List<Map<String, Object>>) params.get("DATA");

        // 遍历 DATA 列表，打印每个对象中的 CONTENT
        for (Map<String, Object> dataItem : dataList) {
            String content = (String) dataItem.get("CONTENT");
            String OPTTYPE = (String) dataItem.get("OPTTYPE");

            System.out.println("Content: " + content);
        }

        /*SysUser user = new SysUser();
        user.setUserName(enterpriseUserId);// 生成16位随机用户名
        user.setPassword(SecurityUtils.encryptPassword("wx20230606@"));
        user.setNickName(userDetail.getName());
        user.setPhonenumber(userDetail2.getMobile());
        user.setAvatar(userDetail2.getAvatar());
        user.setEnterpriseUserId(enterpriseUserId);
        user.setDeptId(100L);
        user.setCreateBy("微信注册");
        //新增 用户
        userService.insertUser(user);
        // 分配角色  可根据需求设置默认角色及权限
        userService.insertUserAuth(user.getUserId(), new Long[]{100L});

        WxCpUser userDetail = wxUserService.getById(wxCpOauth2UserInfo.getUserId());

        System.out.println("moblie===" + userDetail);

        return AjaxResult.success(userDetail);*/
        return AjaxResult.success("");
    }

}