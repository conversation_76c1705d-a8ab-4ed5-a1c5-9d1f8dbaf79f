package com.ruoyi.system.service;

import com.ruoyi.system.domain.Resume;
import com.ruoyi.system.domain.ResumeDelivery;
import com.ruoyi.system.domain.ResumeFamily;
import com.ruoyi.system.domain.ResumeAwards;
import com.ruoyi.system.domain.ResumePosition;
import java.util.List;
import java.util.Map;

public interface IResumeService {
    List<Resume> selectResumeList(Resume resume);

    Resume selectResumeById(String id);

    Resume selectDefaultResume(String userId);

    int insertResume(Resume resume);

    int updateResume(Resume resume);

    int deleteResumeById(String id);

    int setDefaultResume(String id, String userId);

    String uploadAttachment(String resumeId, String fileName, byte[] fileContent);

    ResumeDelivery applyJob(String resumeId, String jobId);

    ResumeDelivery getJobApplyStatus(String jobId, String userId);

    String uploadAvatar(byte[] fileContent, String fileName) throws Exception;

    int getTotalDeliveryCount();

    int getTodayDeliveryCount();

    /**
     * 获取简历总数
     * 
     * @return 简历总数
     */
    int countTotalResumes();

    List<ResumeDelivery> selectDeliveryList(ResumeDelivery delivery);

    ResumeDelivery selectDeliveryById(String id);

    /**
     * 获取用户简历投递总次数
     * 
     * @param userId 用户ID
     * @return 投递总次数
     */
    int getTotalDeliveryCount(String userId);

    /**
     * 获取用户收到的面试邀请总数
     * 
     * @param userId 用户ID
     * @return 面试邀请总数
     */
    int getInterviewInviteCount(String userId);

    /**
     * 上传Word简历
     * 
     * @param fileContent 文件内容
     * @param fileName 文件名
     * @param userId 用户ID
     * @return 简历ID
     * @throws Exception 上传异常
     */
    String uploadWordResume(byte[] fileContent, String fileName, String userId) throws Exception;
    
    /**
     * 检查简历是否投递到指定企业
     *
     * @param resumeId 简历ID
     * @param companyId 企业ID
     * @return 是否已投递
     */
    boolean checkResumeDeliveredToCompany(String resumeId, Long companyId);

    /**
     * 获取最新简历投递记录
     * @param limit 限制数量
     * @return 最新投递记录列表
     */
    List<Map<String, Object>> getRecentDeliveries(int limit);

    /**
     * 获取简历家庭成员信息
     * 
     * @param resumeId 简历ID
     * @return 家庭成员列表
     */
    List<ResumeFamily> getFamilyMembers(String resumeId);

    /**
     * 获取简历奖惩情况
     * 
     * @param resumeId 简历ID
     * @return 奖惩情况
     */
    ResumeAwards getAwards(String resumeId);

    /**
     * 获取简历现任职务信息
     * 
     * @param resumeId 简历ID
     * @return 现任职务信息
     */
    ResumePosition getPosition(String resumeId);

    /**
     * 更新简历家庭成员信息
     * 
     * @param resumeId 简历ID
     * @param familyMembers 家庭成员列表
     */
    void updateFamilyMembers(String resumeId, List<ResumeFamily> familyMembers);

    /**
     * 更新简历奖惩情况
     * 
     * @param resumeId 简历ID
     * @param awards 奖惩情况
     */
    void updateAwards(String resumeId, ResumeAwards awards);

    /**
     * 更新简历现任职务信息
     *
     * @param resumeId 简历ID
     * @param position 现任职务信息
     */
    void updatePosition(String resumeId, ResumePosition position);
}