<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ResumeEducationMapper">
    
    <resultMap type="ResumeEducation" id="EducationResult">
        <id     property="id"          column="id"          />
        <result property="resumeId"    column="resume_id"   />
        <result property="school"      column="school"      />
        <result property="major"       column="major"       />
        <result property="degree"      column="degree"      />
        <result property="startDate"   column="start_date"  />
        <result property="endDate"     column="end_date"    />
        <result property="description" column="description" />
        <result property="isHighest"   column="is_highest"  />
    </resultMap>

    <select id="selectByResumeId" parameterType="String" resultMap="EducationResult">
        select * from resume_education 
        where resume_id = #{resumeId}
        order by start_date desc
    </select>

    <insert id="insert" parameterType="ResumeEducation">
        insert into resume_education (
            id, resume_id, school, major, degree,
            start_date, end_date, description, is_highest
        ) values (
            #{id}, #{resumeId}, #{school}, #{major}, #{degree},
            #{startDate}, #{endDate}, #{description}, #{isHighest}
        )
    </insert>

    <update id="update" parameterType="ResumeEducation">
        update resume_education
        <set>
            <if test="school != null">school = #{school},</if>
            <if test="major != null">major = #{major},</if>
            <if test="degree != null">degree = #{degree},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="description != null">description = #{description},</if>
            <if test="isHighest != null">is_highest = #{isHighest},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="String">
        delete from resume_education where id = #{id}
    </delete>

    <delete id="deleteByResumeId" parameterType="String">
        delete from resume_education where resume_id = #{resumeId}
    </delete>
</mapper> 