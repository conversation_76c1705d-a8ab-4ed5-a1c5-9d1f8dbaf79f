package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

@Data
@EqualsAndHashCode(callSuper = true)
public class Job extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 职位ID */
    private Long id;

    /** 公司ID */
    private Long companyId;

    /** 职位标题 */
    @Excel(name = "职位标题")
    private String title;

    /** 薪资范围 */
    @Excel(name = "薪资范围")
    private String salary;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String company;

    /** 单位 */
    private String unit;

    /** 三四级单位具体名称 */
    @Excel(name = "具体公司名称")
    private String companyName;

    /** 公司Logo */
    private String companyLogo;

    /** 公司描述 */
    private String companyDesc;

    /** 工作地点 */
    @Excel(name = "工作地点")
    private String location;

    /** 工作经验要求 */
    @Excel(name = "工作经验")
    private String experience;

    /** 学历要求 */
    @Excel(name = "学历要求")
    private String education;

    /** 职位描述 */
    private String description;

    /** 岗位要求 */
    private String requirements;

    /** 浏览量 */
    @Excel(name = "浏览量")
    private Integer views;

    /** 投递数量 */
    @Excel(name = "投递数量")
    private Integer applications;

    /** 职位标签 */
    private String tags;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 部门 */
    @Excel(name = "部门")
    private String department;

    /** 其他信息 */
    @Excel(name = "其他")
    private String other;

    /** 所需人数 */
    @Excel(name = "所需人数")
    private Integer headcount;

    /** 目标范围 */
    @Excel(name = "目标范围")
    private String targetRange;

    /** 需求形式 */
    @Excel(name = "需求形式")
    private String demandType;

    /** 关键词搜索 */
    private String keyword;

    /**
     * 获取标签数组
     */
    public String[] getTagArray() {
        return StringUtils.isNotEmpty(tags) ? tags.split(",") : new String[0];
    }

    /**
     * 设置标签数组
     */
    public void setTagArray(String[] tagArray) {
        this.tags = tagArray != null ? String.join(",", tagArray) : null;
    }
    /**
     * 转换为前端所需的JSON格式
     */
    public JobVO toVO() {
        JobVO vo = new JobVO();
        vo.setId(this.getId());
        vo.setTitle(this.getTitle());
        vo.setSalary(this.getSalary());
        vo.setCompany(this.getCompany());
        vo.setCompanyName(this.getCompanyName());
        vo.setLocation(this.getLocation());
        vo.setExperience(this.getExperience());
        vo.setTags(this.getTagArray());
        vo.setViews(this.getViews());
        vo.setApplications(this.getApplications());
        vo.setDepartment(this.getDepartment());
        vo.setOther(this.getOther());
        return vo;
    }

    @Data
    public static class JobVO {
        private Long id;
        private String title;
        private String salary;
        private String company;
        private String companyName;
        private String location;
        private String experience;
        private String[] tags;
        private Integer views;
        private Integer applications;
        private String department;
        private String other;
    }

    public Integer getApplications() {
        return applications;
    }

    public void setApplications(Integer applications) {
        this.applications = applications;
    }

    public String getOther() {
        return other;
    }

    public void setOther(String other) {
        this.other = other;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("salary", getSalary())
            .append("companyId", getCompanyId())
            .append("company", getCompany())
            .append("companyName", getCompanyName())
            .append("location", getLocation())
            .append("experience", getExperience())
            .append("education", getEducation())
            .append("description", getDescription())
            .append("requirements", getRequirements())
            .append("tags", getTags())
            .append("applications", getApplications())
            .append("department", getDepartment())
            .append("headcount", getHeadcount())
            .append("demandType", getDemandType())
            .append("targetRange", getTargetRange())
            .append("other", getOther())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}