import request from '@/utils/request'

// 获取首页统计数据
export function getDashboardStats() {
    return request({
        url: '/talent/dashboard/stats',
        method: 'get'
    })
}

// 获取用户总数
export function getUserCount() {
    return request({
        url: '/system/user/count',
        method: 'get'
    })
}

// 获取企业总数
export function getCompanyCount() {
    return request({
        url: '/talent/company/count',
        method: 'get'
    })
}

// 获取职位总数
export function getJobCount() {
    return request({
        url: '/talent/job/count-total',
        method: 'get'
    })
}

// 获取简历总数
export function getResumeCount() {
    return request({
        url: '/talent/resume/count-total',
        method: 'get'
    })
}

// 获取总浏览数
export function getViewCount() {
    return request({
        url: '/talent/dashboard/view-count',
        method: 'get'
    })
}

// 获取首页动态信息
export function getActivities() {
    return request({
        url: '/talent/dashboard/activities',
        method: 'get'
    })
}