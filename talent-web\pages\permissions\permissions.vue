<template>
  <div class="my-permission-dashboard">
    <header>
      <p>{{ currentYear }}年权限概览</p>
    </header>
    
    <div class="overview-grid">
      <div v-for="(item, index) in permissionOverview" :key="index" class="overview-card">
        <div class="card-icon" :class="item.color">
          <roc-icon-plus type="fas" :name="item.icon" :size="20" :color="item.color" :rotate="90" ></roc-icon-plus>
        </div>
        <div class="card-content">
          <h3>{{ item.title }}</h3>
          <p>{{ item.value }}</p>
        </div>
      </div>
    </div>

    <div class="permission-details">
      <h3>详细权限</h3>
      <table>
        <thead>
          <tr>
            <th>权限名称</th>
            <th>描述</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(permission, index) in permissions" :key="index">
            <td>{{ permission.name }}</td>
            <td>{{ permission.description }}</td>
            <td>
              <span class="status" :class="permission.status">{{ permission.status }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="quick-actions">
      <h3>快速操作</h3>
      <button @click="requestPermission" class="btn primary">申请新权限</button>
      <button @click="viewPermissionLog" class="btn secondary">查看权限日志</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const currentYear = new Date().getFullYear();

const permissionOverview = ref([
  { title: '总权限数', value: '10', icon: 'fas fa-lock', color: 'blue' },
  { title: '活跃权限', value: '8', icon: 'fas fa-unlock', color: 'green' },
  { title: '禁用权限', value: '2', icon: 'fas fa-ban', color: 'red' }
]);

const permissions = ref([
  { name: '查看用户', description: '允许查看用户信息', status: '活跃' },
  { name: '编辑用户', description: '允许编辑用户信息', status: '活跃' },
  { name: '删除用户', description: '允许删除用户信息', status: '禁用' },
  { name: '查看权限', description: '允许查看权限信息', status: '活跃' },
  { name: '编辑权限', description: '允许编辑权限信息', status: '活跃' },
  { name: '删除权限', description: '允许删除权限信息', status: '禁用' }
]);

const requestPermission = () => {
  // 实现申请新权限的逻辑
  console.log('Requesting a new permission');
};

const viewPermissionLog = () => {
  // 实现查看权限日志的逻辑
  console.log('Viewing permission log');
};
</script>

<style scoped>
.my-permission-dashboard {
  font-family: 'Roboto', sans-serif;
  max-width: 1000px;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 15px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

header {
  text-align: center;
  margin-bottom: 1rem;
}

header h1 {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
}

header p {
  color: #7f8c8d;
  font-size: 1rem;
}

.overview-grid {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 1rem;
}

.overview-card {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 1rem;
  flex: 1;
  display: flex;
  align-items: center;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 2rem;
  margin-right: 0.5rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.card-icon.blue { background-color: #e3f2fd; color: #2196f3; }
.card-icon.green { background-color: #e8f5e9; color: #4caf50; }
.card-icon.red { background-color: #ffebee; color: #f44336; }

.card-content h3 {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 0.2rem;
}

.card-content p {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
}

.permission-details {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
}

.permission-details h3 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

th, td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

th {
  font-weight: 600;
  color: #34495e;
}

.status {
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.75rem;
}

.status.活跃 { background-color: #e8f5e9; color: #4caf50; }
.status.禁用 { background-color: #ffebee; color: #f44336; }

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn.primary {
  background-color: #2196f3;
  color: white;
}

.btn.primary:hover {
  background-color: #1976d2;
}

.btn.secondary {
  background-color: #e0e0e0;
  color: #333;
}

.btn.secondary:hover {
  background-color: #bdbdbd;
}

@media (max-width: 768px) {
  .overview-grid {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}

@media (max-width: 300px) {
  .overview-card {
    flex: 1 1 100%;
    max-width: 80px;
    padding: 0.5rem;
  }
  
  .overview-grid {
    justify-content: space-around;
  }
}
</style>