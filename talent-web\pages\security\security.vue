<template>
  <div class="dashboard">
    <header>
      <h3>更新于2024年7月</h3>
    </header>
    
    <section class="overview">
      <div class="card">
        <h3>安全事故总数</h3>
        <p class="highlight">{{ totalIncidents }}起</p>
      </div>
      <div class="card">
        <h3>未处理隐患</h3>
        <p class="highlight">{{ pendingHazards }}个</p>
      </div>
      <div class="card">
        <h3>安全培训覆盖率</h3>
        <p class="highlight">{{ trainingCoverage }}%</p>
      </div>
    </section>

    <section class="charts">
      <div class="chart">
        <h3>事故类型分布</h3>
        <div ref="incidentTypeChart" class="chart-container"></div>
      </div>
      <div class="chart">
        <h3>月度安全事故趋势</h3>
        <div ref="monthlyIncidentTrendChart" class="chart-container"></div>
      </div>
    </section>

    <section class="safety-metrics">
      <h3>安全管理关键指标</h3>
      <div class="metric-grid">
        <div v-for="metric in safetyMetrics" :key="metric.name" class="metric-card">
          <h4>{{ metric.name }}</h4>
          <p>{{ metric.value }}</p>
          <p>同比：<span :class="['trend', metric.trend > 0 ? 'down' : 'up']">
            {{ metric.trend > 0 ? '+' : '' }}{{ metric.trend }}%
          </span></p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const totalIncidents = ref(0)
const pendingHazards = ref(0)
const trainingCoverage = ref(0)
const safetyMetrics = ref([])
const incidentTypeChart = ref(null)
const monthlyIncidentTrendChart = ref(null)

onMounted(() => {
  fetchDashboardData()
  renderCharts()
})

const fetchDashboardData = async () => {
  // 模拟API调用
  totalIncidents.value = 45
  pendingHazards.value = 12
  trainingCoverage.value = 95.5
  safetyMetrics.value = [
    { name: '平均处理时间', value: '2.5天', trend: -10 },
    { name: '安全检查完成率', value: '98%', trend: 5 },
    { name: '员工安全意识评分', value: '88分', trend: 3 },
    { name: '重大事故发生率', value: '0.05%', trend: -20 }
  ]
}

const renderCharts = () => {
  const incidentTypeInstance = echarts.init(incidentTypeChart.value)
  const monthlyIncidentTrendInstance = echarts.init(monthlyIncidentTrendChart.value)

  const incidentTypeOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}起 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '事故类型',
        type: 'pie',
        radius: '70%',
        data: [
          { value: 20, name: '设备故障' },
          { value: 15, name: '操作失误' },
          { value: 5, name: '自然灾害' },
          { value: 3, name: '交通事故' },
          { value: 2, name: '其他' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  }

  const monthlyIncidentTrendOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLabel: {
        fontSize: 8,
        interval: 1,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '事故数',
      nameTextStyle: {
        fontSize: 8
      },
      axisLabel: {
        fontSize: 8
      }
    },
    grid: {
      left: '10%',
      right: '5%',
      bottom: '15%',
      top: '10%'
    },
    series: [
      {
        data: [5, 7, 4, 6, 3, 4, 5, 8, 6, 4, 3, 5],
        type: 'line',
        smooth: true,
        areaStyle: {}
      }
    ]
  }

  incidentTypeInstance.setOption(incidentTypeOption)
  monthlyIncidentTrendInstance.setOption(monthlyIncidentTrendOption)

  window.addEventListener('resize', () => {
    incidentTypeInstance.resize()
    monthlyIncidentTrendInstance.resize()
  })
}
</script>

<style scoped>
.dashboard {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  padding: 0.5rem;
  background-color: #f5f7fa;
  color: #333;
}

header {
  text-align: left;
  padding-top: 0.5rem;
  margin-bottom: 0.5rem; 
  color: blue;
}

h1 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.overview {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.card {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  flex: 1;
  margin: 0 0.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.card:first-child {
  margin-left: 0;
}

.card:last-child {
  margin-right: 0;
}

.highlight {
  color: #3498db;
  font-size: 1rem;
  font-weight: bold;
}

.charts {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.chart {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  margin-bottom: 1rem;
  height: 250px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  height: 100%;
}

.safety-metrics h3 {
  margin-bottom: 0.5rem;
}

.metric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

.metric-card {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metric-card h4 {
  color: #2c3e50;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.trend {
  font-weight: bold;
}

.trend.up {
  color: #2ecc71;
}

.trend.down {
  color: #e74c3c;
}

h3 {
  font-size: 1rem;
  margin-bottom: 0.3rem;
}

p {
  font-size: 0.8rem;
  margin-bottom: 0.2rem;
}

@media (max-width: 480px) {
  .card {
    padding: 0.3rem;
  }

  .card h3 {
    font-size: 0.8rem;
  }

  .highlight {
    font-size: 0.9rem;
  }

  .chart {
    height: 200px;
  }

  .metric-grid {
    grid-template-columns: 1fr 1fr;
  }

  h1 {
    font-size: 1.3rem;
  }

  h3 {
    font-size: 0.9rem;
  }

  p {
    font-size: 0.7rem;
  }
}
</style>