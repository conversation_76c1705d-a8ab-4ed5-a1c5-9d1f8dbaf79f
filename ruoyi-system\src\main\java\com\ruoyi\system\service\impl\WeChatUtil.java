package com.ruoyi.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.MyX509TrustManager;
import com.ruoyi.system.domain.AccessToken;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.security.MessageDigest;

public class WeChatUtil {
    // 微信的请求url
    // 获取access_token的接口地址（GET） 限200（次/天）
    public final static String access_token_url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpId}&corpsecret={corpsecret}";
    //获取jsapi_ticket
    public final static String jsapi_ticket_url = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token={accesstoken}";

    /**
     * 1.发起https请求并获取结果
     *
     * @param requestUrl    请求地址
     * @param requestMethod 请求方式（GET、POST）
     * @param outputStr     提交的数据
     * @return JSONObject(通过JSONObject.get ( key)的方式获取json对象的属性值)
     */
    public static JSONObject httpRequest(String requestUrl, String requestMethod, String outputStr) throws Exception {
        JSONObject jsonObject = null;
        StringBuffer buffer = new StringBuffer();
        InputStream inputStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader bufferedReader = null;
        OutputStream outputStream = null;
        try {
            // 创建SSLContext对象，并使用我们指定的信任管理器初始化
            TrustManager[] tm = {new MyX509TrustManager()};
            SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");
            sslContext.init(null, tm, new java.security.SecureRandom());
            // 从上述SSLContext对象中得到SSLSocketFactory对象
            SSLSocketFactory ssf = sslContext.getSocketFactory();
            URL url = new URL(requestUrl);
            HttpsURLConnection httpUrlConn = (HttpsURLConnection) url.openConnection();
            httpUrlConn.setSSLSocketFactory(ssf);
            httpUrlConn.setDoOutput(true);
            httpUrlConn.setDoInput(true);
            httpUrlConn.setUseCaches(false);
            // 设置请求方式（GET/POST）
            httpUrlConn.setRequestMethod(requestMethod);
            if ("GET".equalsIgnoreCase(requestMethod))
                httpUrlConn.connect();
            // 当有数据需要提交时
            if (null != outputStr) {
                outputStream = httpUrlConn.getOutputStream();
                // 注意编码格式，防止中文乱码
                outputStream.write(outputStr.getBytes("UTF-8"));

            }
            // 将返回的输入流转换成字符串
            inputStream = httpUrlConn.getInputStream();
            inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
            bufferedReader = new BufferedReader(inputStreamReader);
            String str = null;
            while ((str = bufferedReader.readLine()) != null) {
                buffer.append(str);
                httpUrlConn.disconnect();
//                jsonObject = JSONObject.fromObject(buffer.toString());
                jsonObject = JSONObject.parseObject(buffer.toString());
            }

        } catch (Exception e) {
            System.out.println("yichang");
        } finally {
            bufferedReader.close();
            inputStreamReader.close();
            bufferedReader = null;
            inputStreamReader = null;
            // 释放资源
            if (inputStream != null) {
                inputStream.close();
                inputStream = null;
            }
            if (outputStream != null) {
                outputStream.close();
                outputStream = null;
            }

        }
        return jsonObject;
    }

    /**
     * 获取token
     **/
    public static AccessToken getAccessToken(String appid, String appsecret) throws Exception {
        AccessToken accessToken = null;
        String requestUrl = access_token_url.replace("{corpId}", appid).replace("{corpsecret}", appsecret);
        JSONObject jsonObject = httpRequest(requestUrl, "GET", null);
        // 如果请求成功
        if (null != jsonObject) {
            try {
                accessToken = new AccessToken();
                accessToken.setAccessToken(jsonObject.getString("access_token"));
                accessToken.setExpiresIn(jsonObject.getString("expires_in"));
            } catch (Exception e) {
                accessToken = null;
                // 获取token失败
                // log.error("获取token失败 errcode:{} errmsg:{}"+
                // jsonObject.getInt("errcode")+jsonObject.getString("errmsg"));
                return accessToken;
            }
        }
        return accessToken;
    }

    /**
     * 获取jsapi_ticket
     */
    public static JSONObject getJsapiTicket(AccessToken accessToken) throws Exception {
        JSONObject jsapiTicket = new JSONObject();
        String requestUrl = jsapi_ticket_url.replace("{accesstoken}", accessToken.getAccessToken());
        JSONObject jsonObject = httpRequest(requestUrl, "GET", null);
        // 如果请求成功
        System.out.println(jsonObject);
        if (null != jsonObject) {
            try {
                jsapiTicket.put("ticket", jsonObject.getString("ticket"));
                String timestamp = createTimestamp();
                jsapiTicket.put("timestamp", timestamp);
            } catch (Exception e) {
                jsapiTicket = null;
                // 获取jsapiTicket失败
                return jsapiTicket;
            }
        }
        return jsapiTicket;
    }

    //生成时间戳
    public static String createTimestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }

    //获取签名
    public static String getSignature(String tsapiTicket, String nonceStr, String timeStamp, String urlStr) {
        //生成个性签名 需要四个参数
        // noncestr（随机字符串）,jsapi_ticket(是H5应用调用企业微信JS接口的临时票据), timestamp（时间戳）,url（当前网页的URL,不包含#及其后面部分）
        //jsapi_ticket 需要先获取accessToken  根据秘钥获取accesstoken
        //先读取jsapi_ticket  如果jsapi_ticket 为空则重新生成jsapi_ticket 如果不为空则直接使用
        String jsapi_ticket = "jsapi_ticket=" + tsapiTicket + "&";
        String noncestr = "noncestr=" + nonceStr + "&";
        String timestamp = "timestamp=" + timeStamp + "&";
        String url = "url=" + urlStr;
        String signature = getSha1(jsapi_ticket.concat(noncestr).concat(timestamp).concat(url));
        return signature;
    }

    /**
     * sha1算法
     *
     * @param str
     * @return
     */
    private static String getSha1(String str) {
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
            mdTemp.update(str.getBytes("UTF-8"));
            byte[] md = mdTemp.digest();
            int j = md.length;
            char buf[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(buf);
        } catch (Exception e) {
            return null;
        }
    }
}