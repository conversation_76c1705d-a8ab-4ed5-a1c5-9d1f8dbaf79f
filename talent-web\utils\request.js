import config from '../config';

// 统一的请求处理函数
export const createRequest = () => {
    return (options) => {
        const { url, method = 'GET', data, header = {}, isUpload = false, name = 'file', formData = {} } = options;

        // 处理配置
        let currentConfig = config;

        // 获取应用配置
        const appConfig = currentConfig.wx

        // 处理授权头
        const token = uni.getStorageSync('token');
        const headers = {
            ...header,
            'Authorization': token,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };

        // 根据 isUpload 参数选择不同的请求方法
        const requestFunc = isUpload ? uni.uploadFile : uni.request;

        // 根据是否为上传请求构建不同的请求选项
        const requestOptions = isUpload ? {
            url: (currentConfig.baseUrl || 'http://60.188.244.173:18080/') + url,
            header: headers,
            method,
            filePath: data, // 文件路径
            name, // 文件字段名
            formData: formData // 其他表单数据
        } : {
            url: (currentConfig.baseUrl || 'http://60.188.244.173:18080/') + url,
            header: headers,
            method,
            data
        };

        return new Promise((resolve, reject) => {
            requestFunc({
                ...requestOptions,
                success: (res) => {
                    // 处理响应数据
                    let responseData;
                    if (isUpload) {
                        try {
                            responseData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
                        } catch (e) {
                            reject(new Error('解析响应数据失败'));
                            return;
                        }
                    } else {
                        responseData = res.data;
                    }

                    // 处理401未授权
                    if (responseData && responseData.code === 401) {
                        uni.removeStorageSync('token');
                        const redirect_uri = encodeURIComponent(window.location.href);
                        const appid = (currentConfig.wx && currentConfig.wx.appid) || "ww89f73ebdb4f01205";
                        const { agentid, scope } = appConfig;

                        document.location.replace(
                            `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirect_uri}&response_type=code&scope=${scope}&agentid=${agentid}&state=STATE#wechat_redirect`
                        );
                        return;
                    }

                    resolve(responseData);
                },
                fail: (err) => {
                    console.error('请求失败:', err);
                    reject(err);
                }
            });
        });
    };
};

// 为不同应用创建请求实例
export const createHttp = () => {
    const request = createRequest();

    return {
        get: (url, params = {}, header = {}) =>
            request({ url, method: 'GET', data: params, header }),

        post: (url, data = {}, header = {}) =>
            request({ url, method: 'POST', data, header }),

        put: (url, data = {}, header = {}) =>
            request({ url, method: 'PUT', data, header }),

        delete: (url, header = {}) =>
            request({ url, method: 'DELETE', header }),

        upload: (url, filePath, header = {}, name = 'file', formData = {}) =>
            request({
                url,
                method: 'POST',
                data: filePath,
                header,
                isUpload: true,
                name,
                formData
            })
    };
};

// 文件上传配置
const uploadConfig = {
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: ['doc', 'docx', 'pdf'] // 允许的文件类型
}

// 检查文件大小和类型
const checkFile = (file) => {
    if (file.size > uploadConfig.maxSize) {
        uni.showToast({
            title: '文件大小不能超过50MB',
            icon: 'none'
        })
        return false
    }

    const fileType = file.name.split('.').pop().toLowerCase()
    if (!uploadConfig.allowedTypes.includes(fileType)) {
        uni.showToast({
            title: '只支持doc、docx、pdf格式',
            icon: 'none'
        })
        return false
    }

    return true
}

// 上传文件
export const uploadFile = (url, filePath, name = 'file', formData = {}) => {
    return new Promise((resolve, reject) => {
        // 获取文件信息
        uni.getFileInfo({
            filePath,
            success: (res) => {
                if (!checkFile({ size: res.size, name: filePath })) {
                    reject(new Error('文件检查失败'))
                    return
                }

                uni.uploadFile({
                    url: baseURL + url,
                    filePath,
                    name,
                    formData,
                    header: {
                        'Authorization': uni.getStorageSync('token')
                    },
                    success: (res) => {
                        if (res.statusCode === 200) {
                            const data = JSON.parse(res.data)
                            if (data.code === 200) {
                                resolve(data)
                            } else {
                                uni.showToast({
                                    title: data.msg || '上传失败',
                                    icon: 'none'
                                })
                                reject(data)
                            }
                        } else {
                            uni.showToast({
                                title: '上传失败',
                                icon: 'none'
                            })
                            reject(new Error('上传失败'))
                        }
                    },
                    fail: (err) => {
                        uni.showToast({
                            title: '上传失败',
                            icon: 'none'
                        })
                        reject(err)
                    }
                })
            },
            fail: (err) => {
                uni.showToast({
                    title: '获取文件信息失败',
                    icon: 'none'
                })
                reject(err)
            }
        })
    })
}