package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.ArrayList;
import java.util.Map;
import java.util.Date;

import com.ruoyi.system.domain.Job;
import com.ruoyi.system.domain.JobView;
import com.ruoyi.system.domain.dto.JobImportDTO;
import com.ruoyi.system.domain.Company;
import com.ruoyi.system.mapper.JobMapper;
import com.ruoyi.system.mapper.JobViewMapper;
import com.ruoyi.system.service.IJobService;
import com.ruoyi.system.service.IResumeService;
import com.ruoyi.system.service.ICompanyService;
import com.ruoyi.talent.service.ICompanyAdminService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class JobServiceImpl implements IJobService {
    @Autowired
    private JobMapper jobMapper;

    @Autowired
    private JobViewMapper jobViewMapper;

    @Autowired
    private IResumeService resumeService;

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private ICompanyAdminService companyAdminService;

    @Override
    public List<Job> selectJobList(Job job) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        
        // 如果不是管理员或一级管理员，检查是否是企业管理员
        if (!SecurityUtils.isAdmin(userId) && !SecurityUtils.hasRole("yiji_admin")) {
            // 获取企业管理员所管理的企业ID
            Long adminCompanyId = companyAdminService.getAdminCompanyId(userId);
            if (adminCompanyId != null) {
                // 如果是企业管理员，只能查看自己企业的职位
                job.setCompanyId(adminCompanyId);
            }
        }
        return jobMapper.selectJobList(job);
    }

    @Override
    public List<Job> selectPublicJobList(Job job) {
        // 用户端不需要权限检查，直接返回符合条件的职位列表
        return jobMapper.selectJobList(job);
    }

    @Override
    public Job selectJobById(Long id) {
        return jobMapper.selectJobById(id);
    }

    @Override
    public int insertJob(Job job) {
        return jobMapper.insertJob(job);
    }

    @Override
    public int updateJob(Job job) {
        return jobMapper.updateJob(job);
    }

    @Override
    public int deleteJobByIds(Long[] ids) {
        return jobMapper.deleteJobByIds(ids);
    }

    @Override
    public int deleteJobById(Long id) {
        return jobMapper.deleteJobById(id);
    }

    @Override
    @Transactional
    public void recordJobView(JobView jobView) {
        // 保存浏览记录
        jobViewMapper.insertJobView(jobView);
        
        // 更新职位表的浏览量
        jobMapper.updateJobViews(jobView.getJobId());
    }

    @Override
    public void incrementViews(Long id) {
        // 更新职位表的浏览量
        jobMapper.updateJobViews(id);
        
        // 记录浏览详情
        JobView jobView = new JobView();
        jobView.setJobId(id);
        jobViewMapper.insertJobView(jobView);
    }

    @Override
    public int countTotalJobs() {
        Integer count = jobMapper.countTotalJobs();
        return count != null ? count : 0;
    }

    @Override
    public int countTodayNewJobs() {
        Integer count = jobMapper.countTodayNewJobs();
        return count != null ? count : 0;
    }

    @Override
    public int countTodayApplications() {
        Integer count = jobMapper.countTodayApplications();
        return count != null ? count : 0;
    }

    @Override
    public int countTotalViews() {
        Integer count = jobMapper.countTotalViews();
        return count != null ? count : 0;
    }

    @Override
    public void incrementApplications(Long id) {
        jobMapper.updateJobApplications(id);
    }

    @Override
    @Transactional
    public void applyJob(String jobId, String resumeId) {
        // 更新职位投递数
        jobMapper.updateJobApplications(Long.valueOf(jobId));
        
        // 创建投递记录
        resumeService.applyJob(resumeId, jobId);
    }

    @Override
    public String importJobs(List<JobImportDTO> jobList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(jobList) || jobList.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (JobImportDTO importDTO : jobList) {
            try {
                // 处理公司信息并获取公司ID
                Long companyId = handleCompanyData(importDTO, operName);
                
                // 处理职位信息
                Job job = new Job();
                
                // 设置基本信息
                job.setCompanyId(companyId);
                job.setTitle(importDTO.getTitle());
                job.setCompany(importDTO.getCompany());
                job.setCompanyName(importDTO.getCompanyName()); // 设置具体公司名称
                job.setLocation(importDTO.getLocation());
                job.setSalary("0");
                job.setCompanyDesc("无");
                job.setDescription(importDTO.getDescription());
                job.setRequirements(importDTO.getRequirements());
                job.setDepartment(importDTO.getDepartment());
                job.setOther(importDTO.getOther());
                
                // 设置新增字段
                job.setHeadcount(importDTO.getHeadcount());
                job.setTargetRange(importDTO.getTargetRange());
                job.setDemandType(importDTO.getDemandType());
                
                // 从requirements中提取学历要求
                String education = extractEducation(importDTO.getRequirements());
                job.setEducation(education);
                
                // 从requirements中提取工作经验
                String experience = extractExperience(importDTO.getRequirements());
                job.setExperience(experience);
                
                // 设置标签（包含部门和招聘性质）
                List<String> tags = new ArrayList<>();
                if (StringUtils.isNotEmpty(importDTO.getDepartment())) {
                    tags.add(importDTO.getDepartment());
                }
                if (StringUtils.isNotEmpty(importDTO.getRecruitmentType())) {
                    tags.add(importDTO.getRecruitmentType());
                }
                job.setTags(String.join(",", tags));
                
                // 设置其他字段
                job.setStatus("0");
                job.setCreateBy(operName);
                job.setCreateTime(new Date());
                
                // 检查职位是否已存在
//                Job existingJob = jobMapper.selectJobByTitleAndCompany(job.getTitle(), job.getCompany());
//                if (existingJob == null) {
                jobMapper.insertJob(job);
                successNum++;
                successMsg.append("<br/>" + successNum + "、职位 " + job.getTitle() + " 导入成功");
//                } else if (isUpdateSupport) {
//                    job.setId(existingJob.getId());
//                    jobMapper.updateJob(job);
//                    successNum++;
//                    successMsg.append("<br/>" + successNum + "、职位 " + job.getTitle() + " 更新成功");
//                } else {
//                    failureNum++;
//                    failureMsg.append("<br/>" + failureNum + "、职位 " + job.getTitle() + " 已存在");
//                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、职位 " + importDTO.getTitle() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            return successMsg.toString();
        }
    }

    /**
     * 在导入职位数据时，先处理公司信息
     */
    private Long handleCompanyData(JobImportDTO importDTO, String operName) {
        // 检查公司是否已存在
        Company companyQuery = new Company();
        companyQuery.setName(importDTO.getCompany());
        List<Company> existingCompanies = companyService.selectCompanyList(companyQuery);
        
        if (existingCompanies.isEmpty()) {
            // 公司不存在，创建新公司
            Company newCompany = new Company();
            newCompany.setName(importDTO.getCompany());
            newCompany.setLocation(importDTO.getLocation());
            // 使用职位描述作为公司描述
            newCompany.setDescription(importDTO.getDescription());
            newCompany.setStatus("0");
            newCompany.setCreateBy(operName);
            newCompany.setCreateTime(new Date());
            companyService.insertCompany(newCompany);
            return newCompany.getId();
        } else {
            return existingCompanies.get(0).getId();
        }
    }

    /**
     * 从招聘要求中提取学历要求
     */
    private String extractEducation(String requirements) {
        if (StringUtils.isEmpty(requirements)) {
            return null;
        }
        
        // 常见学历关键词
        String[] eduKeywords = {"博士", "硕士", "研究生", "本科", "大专", "专科", "高中"};
        for (String keyword : eduKeywords) {
            if (requirements.contains(keyword)) {
                return keyword;
            }
        }
        return "无要求";
    }

    /**
     * 从招聘要求中提取工作经验要求
     */
    private String extractExperience(String requirements) {
        if (StringUtils.isEmpty(requirements)) {
            return null;
        }
        
        // 匹配工作经验的正则表达式
        Pattern pattern = Pattern.compile("(\\d+[-~]\\d+年|\\d+年以上)");
        Matcher matcher = pattern.matcher(requirements);
        if (matcher.find()) {
            return matcher.group();
        }
        return "无要求";
    }

    @Override
    public List<Map<String, Object>> selectDeliveryList(Long jobId) {
        return jobMapper.selectDeliveryList(jobId);
    }

    @Override
    public List<Map<String, Object>> getRecentJobs(int limit) {
        return jobMapper.selectRecentJobs(limit);
    }

    @Override
    public List<Map<String, Object>> getHotJobs(int limit) {
        return jobMapper.selectHotJobs(limit);
    }
}