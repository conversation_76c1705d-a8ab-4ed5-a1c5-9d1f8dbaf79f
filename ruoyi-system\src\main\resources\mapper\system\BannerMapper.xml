<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BannerMapper">
    
    <resultMap type="Banner" id="BannerResult">
        <id     property="id"       column="id"        />
        <result property="title"     column="title"      />
        <result property="imageUrl"  column="image_url"   />
        <result property="linkUrl"   column="link_url"    />
        <result property="content"   column="content"    />
        <result property="orderNum"  column="order_num"   />
        <result property="status"    column="status"     />
        <result property="delFlag"   column="del_flag"    />
        <result property="createBy"  column="create_by"   />
        <result property="createTime" column="create_time" />
        <result property="updateBy"  column="update_by"   />
        <result property="updateTime" column="update_time" />
        <result property="remark"    column="remark"     />
    </resultMap>

    <sql id="selectBannerVo">
        select id, title, image_url, link_url, content, order_num, status, del_flag, create_by, create_time, update_by, update_time, remark 
        from banner
    </sql>
    
    <select id="selectBannerList" parameterType="Banner" resultMap="BannerResult">
        <include refid="selectBannerVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
        order by order_num asc, create_time desc
    </select>
    
    <select id="selectBannerById" parameterType="Long" resultMap="BannerResult">
        <include refid="selectBannerVo"/>
        where id = #{id} and del_flag = '0'
    </select>
    
    <select id="selectActiveBannerList" resultMap="BannerResult">
        <include refid="selectBannerVo"/>
        where status = '0' and del_flag = '0'
        order by order_num asc, create_time desc
    </select>
        
    <insert id="insertBanner" parameterType="Banner" useGeneratedKeys="true" keyProperty="id">
        insert into banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="linkUrl != null">link_url,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
            del_flag,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
            '0',
         </trim>
    </insert>

    <update id="updateBanner" parameterType="Banner">
        update banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="linkUrl != null">link_url = #{linkUrl},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBannerById" parameterType="Long">
        update banner set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteBannerByIds" parameterType="String">
        update banner set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper> 