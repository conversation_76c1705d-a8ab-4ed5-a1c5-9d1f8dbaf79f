package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class Company extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 公司ID */
    private Long id;

    /** 父级单位ID */
    private Long parentId;

    /** 父级单位名称 */
    @Excel(name = "所属单位")
    private String parentName;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String name;

    /** 公司Logo */
    private String logo;

    /** 公司描述 */
    private String description;

    /** 公司地址 */
    @Excel(name = "公司地址")
    private String location;

    /** 公司规模 */
    @Excel(name = "公司规模")
    private String scale;

    /** 公司行业 */
    @Excel(name = "公司行业")
    private String industry;

    /** 公司性质 */
    @Excel(name = "公司性质")
    private String nature;

    /** 职位数量 */
    private Integer jobCount;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;
} 