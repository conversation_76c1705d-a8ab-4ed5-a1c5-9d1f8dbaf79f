-- 创建投递记录表
CREATE TABLE IF NOT EXISTS `talent_delivery` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '投递ID',
  `job_id` bigint(20) NOT NULL COMMENT '职位ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `resume_id` bigint(20) NOT NULL COMMENT '简历ID',
  `status` char(1) DEFAULT '0' COMMENT '状态（0待处理 1已查看 2已面试 3不合适 4已录用）',
  `feedback` varchar(500) DEFAULT NULL COMMENT '反馈信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='职位投递记录表'; 