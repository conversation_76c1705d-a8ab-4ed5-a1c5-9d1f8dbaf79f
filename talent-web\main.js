import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import wx from 'weixin-js-sdk'
Vue.prototype.$wx = wx

Vue.config.productionTip = false

App.mpType = 'app'
const app = new Vue({
    ...App
})
app.$mount()
    // #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import VConsole from 'vconsole';
import wx from 'weixin-js-sdk';
import authStore from './stores/auth.js';

export function createApp() {
    const app = createSSRApp(App)
        // var vConsole = new VConsole();
    app.config.globalProperties.$wx = wx;

    // 处理onLaunch和onLoad 异步变成同步问题
    app.config.globalProperties.$onLaunched = new Promise((resolve, reject) => {
        app.config.globalProperties.$isResolve = resolve
    })

    // 判断是否为开发环境
    let isDev = process.env.NODE_ENV === 'development'
    if (isDev) {
        //测试使用，正式环境需删掉token
        let token = "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjJkNDk3NDcxLTJiN2QtNDBkMC04MTg3LTRmN2JjMmJhODAxYiJ9.Sqi6JlDvseP_I4iXhYhTRs1ucQ_X4sEu7qoF7esyRrKBFPTQVsZn5PwqEV1410SUHvb_BJQc_Eb2_8Su2NWngg"
            // 使用统一的认证状态管理设置开发环境token
        authStore.token = token;
        uni.setStorageSync('token', token);
    }

    return {
        app
    }
} // #endif