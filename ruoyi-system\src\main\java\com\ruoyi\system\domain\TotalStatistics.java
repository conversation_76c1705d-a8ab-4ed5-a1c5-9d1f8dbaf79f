package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class TotalStatistics extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Integer totalCount;
    private Integer activatedCount;
    private BigDecimal activationRate;
    private Integer activatedNew;
    private Integer totalNew;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date statisticsDate;
}
