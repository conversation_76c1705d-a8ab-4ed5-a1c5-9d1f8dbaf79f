<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ResumeFamilyMapper">
    
    <resultMap type="ResumeFamily" id="FamilyResult">
        <id     property="id"          column="id"          />
        <result property="resumeId"    column="resume_id"   />
        <result property="relation"    column="relation"    />
        <result property="name"        column="name"        />
        <result property="age"         column="age"         />
        <result property="politicalStatus" column="political_status" />
        <result property="workUnit"    column="work_unit"   />
        <result property="createTime"  column="created_at"  />
        <result property="updateTime"  column="updated_at"  />
    </resultMap>

    <select id="selectByResumeId" parameterType="String" resultMap="FamilyResult">
        select * from resume_family 
        where resume_id = #{resumeId}
        order by created_at asc
    </select>

    <insert id="insert" parameterType="ResumeFamily">
        insert into resume_family (
            id, resume_id, relation, name, age, political_status, work_unit, created_at
        ) values (
            #{id}, #{resumeId}, #{relation}, #{name}, #{age}, #{politicalStatus}, #{workUnit}, sysdate()
        )
    </insert>

    <update id="update" parameterType="ResumeFamily">
        update resume_family
        <set>
            <if test="relation != null">relation = #{relation},</if>
            <if test="name != null">name = #{name},</if>
            <if test="age != null">age = #{age},</if>
            <if test="politicalStatus != null">political_status = #{politicalStatus},</if>
            <if test="workUnit != null">work_unit = #{workUnit},</if>
            updated_at = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="String">
        delete from resume_family where id = #{id}
    </delete>

    <delete id="deleteByResumeId" parameterType="String">
        delete from resume_family where resume_id = #{resumeId}
    </delete>
</mapper> 