<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="职位名称" prop="jobId">
        <el-input v-model="queryParams.jobId" placeholder="请输入职位名称" clearable @keyup.enter="handleQuery" style="width: 220px;" />
      </el-form-item>
      <el-form-item label="投递状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择投递状态" clearable style="width: 150px;">
          <el-option label="待处理" value="0" />
          <el-option label="已查看" value="1" />
          <el-option label="邀请面试" value="2" />
          <el-option label="不合适" value="3" />
          <el-option label="已录用" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable style="width: 120px;">
          <el-option label="男" value="男" />
          <el-option label="女" value="女" />
        </el-select>
      </el-form-item>
      <el-form-item label="年龄" prop="ageRange">
        <el-select v-model="queryParams.ageRange" placeholder="请选择年龄范围" clearable style="width: 180px;">
          <el-option label="18-25岁" value="18-25" />
          <el-option label="26-30岁" value="26-30" />
          <el-option label="31-35岁" value="31-35" />
          <el-option label="36-40岁" value="36-40" />
          <el-option label="41岁以上" value="41-100" />
        </el-select>
      </el-form-item>
      <el-form-item label="学历" prop="education">
        <el-select v-model="queryParams.education" placeholder="请选择学历" clearable style="width: 150px;">
          <el-option label="博士" value="博士" />
          <el-option label="硕士" value="硕士" />
          <el-option label="本科" value="本科" />
          <el-option label="大专" value="大专" />
          <el-option label="高中及以下" value="高中" />
        </el-select>
      </el-form-item>
      <el-form-item label="工作年限" prop="experience">
        <el-select v-model="queryParams.experience" placeholder="请选择工作年限" clearable style="width: 180px;">
          <el-option label="应届生" value="应届生" />
          <el-option label="1-3年" value="1-3" />
          <el-option label="3-5年" value="3-5" />
          <el-option label="5-10年" value="5-10" />
          <el-option label="10年以上" value="10+" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 投递列表 -->
    <el-table v-loading="loading" :data="deliveryList">
      <el-table-column label="职位名称" align="center" prop="jobTitle">
        <template #default="scope">
          <el-tooltip
            v-if="scope.row.jobTitle === '职位已删除'"
            content="该职位已被删除，投递记录仍保留用于历史查询"
            placement="top"
          >
            <span class="text-danger">
              <el-icon class="warning-icon">
                <WarningFilled />
              </el-icon>
              {{ scope.row.jobTitle }}
            </span>
          </el-tooltip>
          <span v-else>{{ scope.row.jobTitle }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单位名称" align="center" prop="companyName">
        <template #default="scope">
          <el-tooltip
            v-if="scope.row.companyName === '公司已删除'"
            content="该公司已被删除，投递记录仍保留用于历史查询"
            placement="top"
          >
            <span class="text-danger">
              <el-icon class="warning-icon">
                <WarningFilled />
              </el-icon>
              {{ scope.row.companyName }}
            </span>
          </el-tooltip>
          <span v-else>{{ scope.row.companyName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="resumeName" />
      <el-table-column label="投递简历" align="center" prop="resumeTitle" />
      <el-table-column label="投递时间" align="center" prop="deliveryTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deliveryTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleView(scope.row)"
          >处理</el-button>
          <el-button
            type="text"
            icon="Download"
            @click="handleDownload(scope.row)"
          >下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看详情对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <!-- 职位信息 -->
        <el-descriptions-item label="职位名称" :span="2">
          <el-tooltip
            v-if="form.jobTitle === '职位已删除'"
            content="该职位已被删除，投递记录仍保留用于历史查询"
            placement="top"
          >
            <span class="text-danger">
              <el-icon class="warning-icon">
                <WarningFilled />
              </el-icon>
              {{ form.jobTitle }}
            </span>
          </el-tooltip>
          <span v-else>{{ form.jobTitle }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="单位名称" :span="2">
          <el-tooltip
            v-if="form.companyName === '公司已删除'"
            content="该公司已被删除，投递记录仍保留用于历史查询"
            placement="top"
          >
            <span class="text-danger">
              <el-icon class="warning-icon">
                <WarningFilled />
              </el-icon>
              {{ form.companyName || '-' }}
            </span>
          </el-tooltip>
          <span v-else>{{ form.companyName || '-' }}</span>
        </el-descriptions-item>
        
        <!-- 简历信息 -->
        <el-descriptions-item label="投递人">{{ form.resumeName }}</el-descriptions-item>
        <el-descriptions-item label="投递简历">
          <el-link 
            type="primary" 
            :underline="true"
            @click="handleViewResume(form.resumeId)"
          >
            {{ form.resumeTitle }}
          </el-link>
        </el-descriptions-item>
        
        <!-- 联系信息 -->
        <el-descriptions-item label="联系电话">{{ form.phone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="电子邮箱">{{ form.email || '-' }}</el-descriptions-item>
        
        <!-- 投递状态 -->
        <el-descriptions-item label="投递时间">{{ parseTime(form.deliveryTime) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(form.status)">{{ getStatusText(form.status) }}</el-tag>
        </el-descriptions-item>
        
        <!-- 面试信息 -->
        <el-descriptions-item label="面试时间" :span="2">
          {{ form.interviewTime ? parseTime(form.interviewTime) : '-' }}
        </el-descriptions-item>
        
        <!-- 附件信息 -->
        <el-descriptions-item label="附件" :span="2">
          <template v-if="form.attachments && form.attachments.length > 0">
            <div v-for="(file, index) in form.attachments" :key="index">
              <el-link 
                type="primary" 
                :underline="true"
                :href="file.url"
                target="_blank"
              >
                {{ file.name }}
              </el-link>
            </div>
          </template>
          <span v-else>-</span>
        </el-descriptions-item>
        
        <!-- 反馈信息 -->
        <el-descriptions-item label="反馈意见" :span="2">
          {{ form.feedback || '-' }}
        </el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleViewResume(form.resumeId)">查看完整简历</el-button>
          <el-button 
            type="success" 
            @click="handleInviteInterview" 
            v-if="form.status !== '2' && form.status !== '3' && form.status !== '4'"
          >发送面试邀请</el-button>
          <el-button 
            type="warning" 
            @click="handleCancelInterview" 
            v-if="form.status === '2'"
          >撤销面试邀请</el-button>
          <el-button 
            type="danger" 
            @click="handleReject" 
            v-if="form.status !== '3' && form.status !== '4'"
          >不合适</el-button>
          <el-button 
            type="success" 
            @click="handleAccept" 
            v-if="form.status !== '3' && form.status !== '4'"
          >已录用</el-button>
          <el-button 
            type="primary" 
            @click="handleCancelReject" 
            v-if="form.status === '3'"
          >撤销不合适</el-button>
          <el-button 
            type="primary" 
            @click="handleCancelAccept" 
            v-if="form.status === '4'"
          >撤销已录用</el-button>
          <el-button @click="open = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { listDelivery, getDelivery, updateDeliveryStatus } from "@/api/talent/delivery";
import { sendMessage } from "@/api/talent/message";
import { ElMessage, ElMessageBox } from 'element-plus';
import { WarningFilled } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { getToken } from '@/utils/auth';

const loading = ref(true);
const total = ref(0);
const open = ref(false);
const title = ref("");
const showSearch = ref(true);
const deliveryList = ref([]);
const router = useRouter();

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  jobId: undefined,
  status: undefined,
  gender: undefined,
  ageRange: undefined,
  education: undefined,
  experience: undefined
});

const form = reactive({
  id: undefined,
  jobId: undefined,
  jobTitle: undefined,
  resumeId: undefined,
  resumeTitle: undefined,
  resumeName: undefined,
  phone: undefined,
  email: undefined,
  status: undefined,
  deliveryTime: undefined,
  interviewTime: undefined,
  feedback: undefined,
  attachments: []
});

/** 查询投递列表 */
const getList = async (params) => {
  loading.value = true;
  try {
    // 如果传入了分页参数，更新查询参数
    if (params) {
      queryParams.pageNum = params.page;
      queryParams.pageSize = params.limit;
    }
    const response = await listDelivery(queryParams);
    deliveryList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    console.error("获取投递列表失败:", error);
  }
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.jobId = undefined;
  queryParams.status = undefined;
  queryParams.gender = undefined;
  queryParams.ageRange = undefined;
  queryParams.education = undefined;
  queryParams.experience = undefined;
  handleQuery();
};

/** 查看详情按钮操作 */
const handleView = async (row) => {
  try {
    const response = await getDelivery(row.id);
    Object.assign(form, response.data);
    open.value = true;
    title.value = "查看投递详情";
    
    // 如果状态是待处理，则更新为已查看并发送消息
    if (row.status === '0') {
      try {
        await updateDeliveryStatus(row.id, { status: '1' });
        // 更新列表中的状态
        row.status = '1';
        
        // 发送消息通知
        await sendMessage({
          userId: form.userId,
          title: '简历投递状态更新',
          content: `您投递至【${form.companyName || '企业'}】的【${form.jobTitle || '职位'}】简历已被查看。`,
          type: 'INTERVIEW',
          relatedId: form.id
        });
      } catch (error) {
        console.error("更新投递状态失败:", error);
      }
    }
  } catch (error) {
    console.error("获取投递详情失败:", error);
  }
};

/** 查看简历详情 */
const handleViewResume = (row) => {
  console.log('投递记录数据:', row);
  if (!row) {
    ElMessage.warning('未找到简历ID');
    return;
  }
  router.push({
    path: '/talent/resume/view',
    query: { id: row }
  });
};

/** 下载简历 */
const handleDownload = async (row) => {
  try {
    console.log('下载简历 - 投递记录数据:', row);
    console.log('简历ID:', row.resumeId);

    const token = getToken();

    if (!token) {
      ElMessage.error('请先登录');
      return;
    }

    if (!row.resumeId) {
      ElMessage.error('简历ID不存在');
      console.error('简历ID为空:', row);
      return;
    }

    const response = await fetch(`/dev-api/talent/resume/downloadPdf/${row.resumeId}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });

    if (!response.ok) {
      if (response.status === 403) {
        ElMessage.error('没有权限下载此简历');
      } else if (response.status === 404) {
        ElMessage.error('简历不存在');
      } else {
        ElMessage.error('下载失败');
      }
      return;
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = (row.resumeName || '简历') + '_' + row.resumeId + '.pdf';
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    window.URL.revokeObjectURL(url);

    ElMessage.success("简历下载成功");
  } catch (error) {
    console.error('下载简历失败:', error);
    ElMessage.error('下载简历失败');
  }
};

/** 获取状态显示文本 */
const getStatusText = (status) => {
  const statusMap = {
    '0': '待处理',
    '1': '已查看',
    '2': '邀请面试',
    '3': '不合适',
    '4': '已录用'
  };
  return statusMap[status] || '未知状态';
};

/** 获取状态标签类型 */
const getStatusType = (status) => {
  const typeMap = {
    '0': 'info',
    '1': 'primary',
    '2': 'success',
    '3': 'danger',
    '4': 'warning'
  };
  return typeMap[status] || '';
};

/** 标记为不合适 */
const handleReject = () => {
  ElMessageBox.prompt('请输入不合适的原因', '标记为不合适', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputValidator: (value) => {
      if (!value) {
        return '请输入不合适的原因';
      }
      return true;
    }
  }).then(async ({ value }) => {
    try {
      // 更新投递状态为"不合适"
      await updateDeliveryStatus(form.id, { 
        status: '3',
        feedback: value
      });
      
      // 更新表单和列表中的状态
      form.status = '3';
      form.feedback = value;
      const index = deliveryList.value.findIndex(item => item.id === form.id);
      if (index !== -1) {
        deliveryList.value[index].status = '3';
        deliveryList.value[index].feedback = value;
      }
      
      // 发送消息
      await sendMessage({
        userId: form.userId,
        title: '简历投递结果通知',
        content: `很抱歉，您投递至【${form.companyName || '企业'}】的【${form.jobTitle || '职位'}】简历未能通过筛选。原因：${value}`,
        type: 'INTERVIEW',
        relatedId: form.id
      });
      
      ElMessage.success('已标记为不合适');
    } catch (error) {
      console.error("标记不合适失败:", error);
      ElMessage.error('标记不合适失败');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

/** 标记为已录用 */
const handleAccept = () => {
  ElMessageBox.prompt('请输入录用通知内容', '标记为已录用', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputValidator: (value) => {
      if (!value) {
        return '请输入录用通知内容';
      }
      return true;
    }
  }).then(async ({ value }) => {
    try {
      // 更新投递状态为"已录用"
      await updateDeliveryStatus(form.id, { 
        status: '4',
        feedback: value
      });
      
      // 更新表单和列表中的状态
      form.status = '4';
      form.feedback = value;
      const index = deliveryList.value.findIndex(item => item.id === form.id);
      if (index !== -1) {
        deliveryList.value[index].status = '4';
        deliveryList.value[index].feedback = value;
      }
      
      // 发送消息
      await sendMessage({
        userId: form.userId,
        title: '录用通知',
        content: `恭喜您！您已被【${form.companyName || '企业'}】录用为【${form.jobTitle || '职位'}】。${value}`,
        type: 'INTERVIEW',
        relatedId: form.id
      });
      
      ElMessage.success('已标记为已录用');
    } catch (error) {
      console.error("标记已录用失败:", error);
      ElMessage.error('标记已录用失败');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

/** 发送面试邀请 */
const handleInviteInterview = () => {
  ElMessageBox.prompt('请输入面试时间', '发送面试邀请', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'datetime-local',
    inputValidator: (value) => {
      if (!value) {
        return '请输入面试时间';
      }
      return true;
    }
  }).then(async ({ value }) => {
    try {
      // 将日期格式从 "2025-12-25T10:50" 转换为 "2025-12-25 10:50:00"
      const formattedDate = value.replace('T', ' ') + ':00';
      
      // 更新投递状态为"邀请面试"
      await updateDeliveryStatus(form.id, { 
        status: '2',
        interviewTime: formattedDate
      });
      
      // 更新表单和列表中的状态
      form.status = '2';
      form.interviewTime = formattedDate;
      const index = deliveryList.value.findIndex(item => item.id === form.id);
      if (index !== -1) {
        deliveryList.value[index].status = '2';
        deliveryList.value[index].interviewTime = formattedDate;
      }
      
      // 发送消息
      await sendMessage({
        userId: form.userId,
        title: '面试邀请通知',
        content: `您已收到来自【${form.companyName || '企业'}】的【${form.jobTitle || '职位'}】面试邀请，面试时间：${formattedDate}`,
        type: 'INTERVIEW',
        relatedId: form.id
      });
      
      ElMessage.success('面试邀请发送成功');
    } catch (error) {
      console.error("发送面试邀请失败:", error);
      ElMessage.error('发送面试邀请失败');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

/** 撤销面试邀请 */
const handleCancelInterview = () => {
  ElMessageBox.confirm('确定要撤销面试邀请吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 更新投递状态为"已查看"
      await updateDeliveryStatus(form.id, { 
        status: '1',
        interviewTime: null
      });
      
      // 更新表单和列表中的状态
      form.status = '1';
      form.interviewTime = null;
      const index = deliveryList.value.findIndex(item => item.id === form.id);
      if (index !== -1) {
        deliveryList.value[index].status = '1';
        deliveryList.value[index].interviewTime = null;
      }
      
      // 发送消息
      await sendMessage({
        userId: form.userId,
        title: '面试邀请撤销通知',
        content: `抱歉，【${form.companyName || '企业'}】的【${form.jobTitle || '职位'}】原定的面试邀请已被撤销。`,
        type: 'INTERVIEW',
        relatedId: form.id
      });
      
      ElMessage.success('已撤销面试邀请');
    } catch (error) {
      console.error("撤销面试邀请失败:", error);
      ElMessage.error('撤销面试邀请失败');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

/** 撤销不合适状态 */
const handleCancelReject = () => {
  ElMessageBox.confirm('确定要撤销不合适状态吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 更新投递状态为"已查看"
      await updateDeliveryStatus(form.id, { 
        status: '1',
        feedback: null
      });
      
      // 更新表单和列表中的状态
      form.status = '1';
      form.feedback = null;
      const index = deliveryList.value.findIndex(item => item.id === form.id);
      if (index !== -1) {
        deliveryList.value[index].status = '1';
        deliveryList.value[index].feedback = null;
      }
      
      // 发送消息
      await sendMessage({
        userId: form.userId,
        title: '状态更新通知',
        content: '您的简历投递状态已更新为"已查看"。',
        type: 'SYSTEM',
        relatedId: form.id
      });
      
      ElMessage.success('已撤销不合适状态');
    } catch (error) {
      console.error("撤销不合适状态失败:", error);
      ElMessage.error('撤销不合适状态失败');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

/** 撤销已录用状态 */
const handleCancelAccept = () => {
  ElMessageBox.confirm('确定要撤销已录用状态吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 更新投递状态为"已查看"
      await updateDeliveryStatus(form.id, { 
        status: '1',
        feedback: null
      });
      
      // 更新表单和列表中的状态
      form.status = '1';
      form.feedback = null;
      const index = deliveryList.value.findIndex(item => item.id === form.id);
      if (index !== -1) {
        deliveryList.value[index].status = '1';
        deliveryList.value[index].feedback = null;
      }
      
      // 发送消息
      await sendMessage({
        userId: form.userId,
        title: '状态更新通知',
        content: '您的简历投递状态已更新为"已查看"。',
        type: 'SYSTEM',
        relatedId: form.id
      });
      
      ElMessage.success('已撤销已录用状态');
    } catch (error) {
      console.error("撤销已录用状态失败:", error);
      ElMessage.error('撤销已录用状态失败');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
.el-tag {
  margin-right: 5px;
}
.dialog-footer {
  text-align: center;
}
/* 确保下拉菜单选项文字完整显示 */
:deep(.el-select-dropdown__item) {
  white-space: normal;
  height: auto;
  line-height: 1.5;
  padding: 8px 20px;
}
/* 增加表单项之间的间距 */
.el-form-item {
  margin-right: 10px;
}
/* 已删除数据的红色文本样式 */
.text-danger {
  color: #f56c6c;
  font-weight: 500;
}

/* 警告图标样式 */
.warning-icon {
  margin-right: 4px;
  font-size: 14px;
  color: #f56c6c;
}
</style>
