package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.Job;
import com.ruoyi.system.domain.JobView;
import com.ruoyi.system.domain.dto.JobImportDTO;


public interface IJobService {
    /**
     * 查询职位列表（管理端）
     */
    public List<Job> selectJobList(Job job);

    /**
     * 查询职位列表（用户端）
     */
    public List<Job> selectPublicJobList(Job job);

    public Job selectJobById(Long id);
    public int insertJob(Job job);
    public int updateJob(Job job);
    public int deleteJobByIds(Long[] ids);
    public int deleteJobById(Long id);
    public void incrementViews(Long id);
    public void incrementApplications(Long id);
        /**
     * 统计职位总数
     */
    public int countTotalJobs();

    /**
     * 统计今日新增职位数
     */
    public int countTodayNewJobs();

    /**
     * 统计今日申请数
     */
    public int countTodayApplications();

    /**
     * 统计总浏览量
     */
    public int countTotalViews();

    public void recordJobView(JobView jobView);

    void applyJob(String jobId, String resumeId);

    /**
     * 获取最新岗位
     * @param limit 限制数量
     * @return 最新岗位列表
     */
    List<Map<String, Object>> getRecentJobs(int limit);

    /**
     * 获取热门岗位
     * @param limit 限制数量
     * @return 热门岗位列表
     */
    List<Map<String, Object>> getHotJobs(int limit);

    String importJobs(List<JobImportDTO> jobList, Boolean isUpdateSupport, String operName);

    /**
     * 查询职位投递情况
     * 
     * @param jobId 职位ID
     * @return 投递情况列表
     */
    public List<Map<String, Object>> selectDeliveryList(Long jobId);

}