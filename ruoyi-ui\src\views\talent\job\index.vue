<template>
    <div class="app-container">
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="职位名称" prop="keyword">
          <el-input
            v-model="queryParams.keyword"
            placeholder="请输入职位名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="所属单位" prop="companyId">
          <el-select v-model="queryParams.companyId" placeholder="请选择所属单位" clearable filterable style="width: 240px;">
            <el-option
              v-for="company in companies"
              :key="company.id"
              :label="company.name"
              :value="company.id"
          />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
          <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
  
      <!-- 操作按钮区域 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain :icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain :icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain :icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain :icon="Upload" @click="handleImport">导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain :icon="Download" @click="handleExport">导出</el-button>
        </el-col>
      </el-row>
  
      <!-- 表格 -->
      <el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="职位ID" align="center" prop="id" />
        <el-table-column label="职位名称" align="center" prop="title" />
        <el-table-column label="所属单位" align="center" prop="company" />
        <el-table-column label="工作地点" align="center" prop="location" />
        <el-table-column label="投递数量" align="center">
          <template #default="scope">
            <el-link type="primary" @click="handleViewDelivery(scope.row)">
              {{ scope.row.applications || 0 }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'info'">
              {{ scope.row.status === '0' ? '正常' : '关闭' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button link type="primary" :icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="primary" :icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
            <el-button link type="primary" :icon="View" @click="handleViewDelivery(scope.row)">投递情况</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
  
      <!-- 添加或修改职位对话框 -->
      <el-dialog :title="title" v-model="open" width="900px" append-to-body>
        <el-form ref="jobFormRef" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="职位名称" prop="title">
                <el-input v-model="form.title" placeholder="请输入职位名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属单位" prop="companyId">
                <el-select v-model="form.companyId" placeholder="请选择所属单位" filterable @change="handleCompanyChange" style="width: 100%;">
                  <el-option
                    v-for="company in companies"
                    :key="company.id"
                    :label="company.name"
                    :value="company.id"
                  />
                </el-select>
              </el-form-item>
              

            </el-col>
          </el-row>
  
          <!-- 添加新的公司名称字段 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="公司名称" prop="companyName">
                <el-input v-model="form.companyName" placeholder="请输入三四级单位具体名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="薪资范围" prop="salary">
                <el-input v-model="form.salary" placeholder="请输入薪资范围（选填）" />
              </el-form-item>
            </el-col>
          </el-row>
  
          <el-row>
            <el-col :span="12">
              <el-form-item label="工作地点" prop="location">
                <el-input v-model="form.location" placeholder="请输入工作地点" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作经验" prop="experience">
                <el-select v-model="form.experience" placeholder="请选择">
                  <el-option label="无要求" value="无要求" />
                  <el-option label="应届生" value="应届生" />
                  <el-option label="1-3年" value="1-3年" />
                  <el-option label="3-5年" value="3-5年" />
                  <el-option label="5-10年" value="5-10年" />
                  <el-option label="10年以上" value="10年以上" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
  
          <el-row>
            <el-col :span="12">
              <el-form-item label="学历要求" prop="education">
                <el-select v-model="form.education" placeholder="请选择">
                  <el-option label="不限" value="不限" />
                  <el-option label="本科及以上" value="本科及以上" />
                  <el-option label="硕士及以上" value="硕士及以上" />
                  <el-option label="博士" value="博士" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部门" prop="department">
                <el-input v-model="form.department" placeholder="请输入部门" />
              </el-form-item>
            </el-col>
          </el-row>
  
          <el-row>
            <el-col :span="12">
              <el-form-item label="所需人数" prop="headcount">
                <el-input-number v-model="form.headcount" :min="1" placeholder="请输入所需人数" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="需求形式" prop="demandType">
                <el-select v-model="form.demandType" placeholder="请选择需求形式">
                  <el-option label="内部招聘" value="内部招聘" />
                  <el-option label="人员借调" value="人员借调" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
  
          <el-row>
            <el-col :span="24">
              <el-form-item label="其他" prop="other">
                <el-input
                  v-model="form.other"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入其他补充信息"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- Add hidden input for 目标范围 -->
          <input type="hidden" v-model="form.targetRange" />
  
          <el-row>
            <el-col :span="24">
              <el-form-item label="职位描述" prop="description">
                <editor v-model="form.description" :min-height="192"/>
              </el-form-item>
            </el-col>
          </el-row>
  
          <el-row>
            <el-col :span="24">
              <el-form-item label="岗位要求" prop="requirements">
                <editor v-model="form.requirements" :min-height="192"/>
              </el-form-item>
            </el-col>
          </el-row>
  
          <el-row>
            <el-col :span="24">
              <el-form-item label="职位标签" prop="tagArray">
                <el-select
                  v-model="form.tagArray"
                  multiple
                  filterable
                  allow-create
                  default-first-option
                  placeholder="请选择或输入标签"
                >
                  <el-option
                    v-for="tag in tagOptions"
                    :key="tag"
                    :label="tag"
                    :value="tag"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
  
          <el-row>
            <el-col :span="24">
              <el-form-item label="状态">
                <el-radio-group v-model="form.status">
                  <el-radio label="0">正常</el-radio>
                  <el-radio label="1">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>
  
      <!-- 添加导入对话框 -->
      <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
        <el-upload
          ref="uploadRef"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip text-center">
              <div class="el-upload__tip">
                <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的职位数据
              </div>
              <span>仅允许导入xls、xlsx格式文件。</span>
              <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
            </div>
          </template>
        </el-upload>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
  
      <!-- 投递情况对话框 -->
      <el-dialog title="投递情况" v-model="deliveryDialogVisible" width="80%" append-to-body>
        <div class="delivery-stats">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>总投递数</span>
                  </div>
                </template>
                <div class="card-body">
                  <span class="number">{{ deliveryTotal }}</span>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>待处理</span>
                  </div>
                </template>
                <div class="card-body">
                  <span class="number">{{ getStatusCount('0') }}</span>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>已查看</span>
                  </div>
                </template>
                <div class="card-body">
                  <span class="number">{{ getStatusCount('1') }}</span>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>已面试</span>
                  </div>
                </template>
                <div class="card-body">
                  <span class="number">{{ getStatusCount('2') }}</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <el-table v-loading="deliveryLoading" :data="deliveryList">
          <el-table-column label="投递人" align="center" prop="resumeName" />
          <el-table-column label="投递时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleViewResume(scope.row)">查看简历</el-button>
              <el-button link type="primary" @click="handleUpdateStatus(scope.row)">更新状态</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="deliveryTotal>0"
          :total="deliveryTotal"
          :page.sync="deliveryQueryParams.pageNum"
          :limit.sync="deliveryQueryParams.pageSize"
          @pagination="getDeliveryList"
        />
      </el-dialog>
  
      <!-- 简历查看对话框 -->
      <el-dialog title="简历详情" v-model="resumeDialogVisible" width="80%" append-to-body>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="简历标题" :span="2">{{ resumeForm.resumeTitle }}</el-descriptions-item>
          <el-descriptions-item label="照片" :span="1">
            <el-image 
              v-if="resumeForm.avatar" 
              :src="resumeForm.avatar" 
              style="width: 100px; height: 140px"
              fit="cover"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><picture-filled /></el-icon>
                </div>
              </template>
            </el-image>
            <span v-else>未上传照片</span>
          </el-descriptions-item>
          <el-descriptions-item label="基本信息" :span="1">
            <p>姓名：{{ resumeForm.name }}</p>
            <p>性别：{{ resumeForm.gender=="female"?"女":"男" }}</p>
            <p>出生日期：{{ parseTime(resumeForm.birthday, '{y}-{m}-{d}') }}</p>
            <p>所在地：{{ resumeForm.location }}</p>
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ resumeForm.phone }}</el-descriptions-item>
          <el-descriptions-item label="电子邮箱">{{ resumeForm.email }}</el-descriptions-item>
          
          
          <!-- 教育经历 -->
          <el-descriptions-item label="教育经历" :span="2">
            <div v-for="(edu, index) in resumeForm.education" :key="index">
              <p>{{ edu.school }} - {{ edu.major }} ({{ edu.degree }})</p>
              <p>{{ parseTime(edu.startDate, '{y}-{m}-{d}') }} 至 {{ parseTime(edu.endDate, '{y}-{m}-{d}') }}</p>
            </div>
          </el-descriptions-item>
          
          <!-- 工作经历 -->
          <el-descriptions-item label="工作经历" :span="2">
            <div v-for="(work, index) in resumeForm.work" :key="index">
              <p>{{ work.company }} - {{ work.position }}</p>
              <p>{{ parseTime(work.startDate, '{y}-{m}-{d}') }} 至 {{ parseTime(work.endDate, '{y}-{m}-{d}') }}</p>
              <p>{{ work.description }}</p>
            </div>
          </el-descriptions-item>
          
          <!-- 项目经历 -->
          <el-descriptions-item label="项目经历" :span="2">
            <div v-for="(project, index) in resumeForm.project" :key="index">
              <p>{{ project.name }}</p>
              <p>{{ project.role }}</p>
              <p>{{ project.description }}</p>
              <p>{{ parseTime(project.startDate, '{y}-{m}-{d}') }} 至 {{ parseTime(project.endDate, '{y}-{m}-{d}') }}</p>
            </div>
          </el-descriptions-item>
          
          <!-- 技能特长 -->
          <el-descriptions-item label="技能特长" :span="2">
            <div v-for="(skill, index) in resumeForm.skills" :key="index">
              <p>{{ skill.name }} - {{ skill.level }}</p>
            </div>
          </el-descriptions-item>
          
          <el-descriptions-item label="创建时间">{{ parseTime(resumeForm.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ parseTime(resumeForm.updateTime) }}</el-descriptions-item>
        </el-descriptions>
      </el-dialog>
  
      <!-- 更新状态对话框 -->
      <el-dialog title="更新投递状态" v-model="statusDialogVisible" width="500px" append-to-body>
        <el-form ref="statusFormRef" :model="statusForm" :rules="statusRules" label-width="80px">
          <el-form-item label="状态" prop="status">
            <el-select v-model="statusForm.status" placeholder="请选择状态">
              <el-option label="待处理" value="0" />
              <el-option label="已查看" value="1" />
              <el-option label="已面试" value="2" />
              <el-option label="不合适" value="3" />
              <el-option label="已录用" value="4" />
            </el-select>
          </el-form-item>
          <el-form-item label="反馈" prop="feedback">
            <el-input
              v-model="statusForm.feedback"
              type="textarea"
              placeholder="请输入反馈信息"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitStatusForm">确 定</el-button>
            <el-button @click="statusDialogVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
  
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus, Edit, Delete, Search, Refresh, Upload, Download, UploadFilled, View, PictureFilled } from '@element-plus/icons-vue'
  import { listJob, getJob, delJob, addJob, updateJob } from "@/api/talent/job"
  import { listDelivery, updateDeliveryStatus } from "@/api/talent/delivery"
  import { getResume } from "@/api/talent/resume"
  import { getToken } from "@/utils/auth"
  import { download } from '@/utils/request'
  import { useRouter } from 'vue-router'
  import { listAllCompanies } from "@/api/talent/company"

  
  // 路由实例
  const router = useRouter()
  
  // 遮罩层
  const loading = ref(false)
  // 选中数组
  const ids = ref([])
  // 非单个禁用
  const single = ref(true)
  // 非多个禁用
  const multiple = ref(true)
  // 显示搜索条件
  const showSearch = ref(true)
  // 总条数
  const total = ref(0)
  // 职位表格数据
  const jobList = ref([])
  // 弹出层标题
  const title = ref("")
  // 是否显示弹出层
  const open = ref(false)
  
  // 查询参数
  const queryParams = ref({
    keyword: undefined,
    companyId: undefined,
    pageNum: 1,
    pageSize: 10
  })
  
  // 表单对象
  const form = ref({
    id: undefined,
    title: '',
    salary: '',
    companyId: undefined,
    companyName: '', // 新增公司名称字段
    companyLogo: '',
    companyDesc: '',
    location: '',
    experience: '',
    education: '',
    description: '',
    requirements: '',
    tagArray: [],
    tags: '',
    status: '0',
    department: undefined,
    headcount: 1,
    demandType: undefined,
    targetRange: '公司系统内', // 设置默认值
    other: undefined
  })
  
  // 表单校验规则
  const rules = {
    title: [{ required: true, message: '职位名称不能为空', trigger: 'blur' }],
    companyId: [{ required: true, message: '所属单位不能为空', trigger: 'change' }],
    // 薪资范围改为选填项，移除必填校验
    location: [{ required: true, message: '工作地点不能为空', trigger: 'blur' }],
    experience: [{ required: true, message: '工作经验不能为空', trigger: 'change' }],
    education: [{ required: true, message: '学历要求不能为空', trigger: 'change' }],
    department: [{ required: true, message: "请输入部门", trigger: "blur" }],
    headcount: [{ required: true, message: "请输入所需人数", trigger: "blur" }],
    demandType: [{ required: true, message: "请选择需求形式", trigger: "change" }]
    // 移除 targetRange 的验证规则
  }
  
  // 预设标签选项
  const tagOptions = [
    '财务管理',
    '数字化自动化',
    '企业运行',
    '人力资源',
    '文字写作'
  ]
  
  // 表单ref
  const jobFormRef = ref(null)
  
  // 将 uploadRef 声明移到最外层
  const uploadRef = ref(null)
  
  // 上传参数
  const upload = ref({
    open: false,
    title: '',
    isUploading: false,
    updateSupport: false,
    url: import.meta.env.VITE_APP_BASE_API + '/talent/job/importData',
    headers: { Authorization: 'Bearer ' + getToken() }
  })
  
  // 投递情况相关数据
  const deliveryDialogVisible = ref(false)
  const deliveryLoading = ref(false)
  const deliveryList = ref([])
  const deliveryTotal = ref(0)
  const currentJobId = ref(null)
  const deliveryQueryParams = ref({
    pageNum: 1,
    pageSize: 10,
    jobId: null
  })

  // 状态更新相关数据
  const statusDialogVisible = ref(false)
  const statusForm = ref({
    id: null,
    status: '',
    feedback: ''
  })
  const statusRules = {
    status: [{ required: true, message: '请选择状态', trigger: 'change' }]
  }

  // 简历查看相关数据
  const resumeDialogVisible = ref(false)
  const resumeForm = ref({
    id: '',
    userId: '',
    resumeTitle: '',
    name: '',
    avatar: '',
    gender: '',
    birthday: '',
    phone: '',
    email: '',
    location: '',
    isDefault: false,
    completeness: 0,
    views: 0,
    status: '',
    education: [],
    work: [],
    project: [],
    skills: [],
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    remark: ''
  })

  // 公司列表数据
  const companies = ref([]);

  // 获取所有公司列表
  function getCompanies() {
    listAllCompanies().then(response => {
      companies.value = response.data;
    });
  }

  /** 查询职位列表 */
  const getList = async (params) => {
    loading.value = true;
    // If params provided from pagination component, update the queryParams
    if (params) {
      queryParams.value.pageNum = params.page;
      queryParams.value.pageSize = params.limit;
    }
    try {
      const response = await listJob(queryParams.value);
      jobList.value = response.rows;
      total.value = response.total;
    } catch (error) {
      console.error("获取职位列表失败:", error);
    }
    loading.value = false;
  }
  
  /** 取消按钮 */
  const cancel = () => {
    open.value = false
    reset()
  }
  
  /** 表单重置 */
  const reset = () => {
    form.value = {
      id: undefined,
      title: '',
      salary: '',
      companyId: undefined,
      companyName: '', // 新增公司名称字段
      companyLogo: '',
      companyDesc: '',
      location: '',
      experience: '',
      education: '',
      description: '',
      requirements: '',
      tagArray: [],
      tags: '',
      status: '0',
      department: undefined,
      headcount: 1,
      demandType: undefined,
      targetRange: '公司系统内', // 设置默认值
      other: undefined
    }
  }
  
  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
  }
  
  /** 重置按钮操作 */
  const resetQuery = () => {
    queryParams.value = {
      keyword: undefined,
      companyId: undefined,
      pageNum: 1,
      pageSize: 10
    }
    handleQuery()
  }
  
  /** 新增按钮操作 */
  const handleAdd = () => {
    reset()
    open.value = true
    title.value = "添加职位"
  }
  
  /** 修改按钮操作 */
  const handleUpdate = async (row) => {
    reset()
    const jobId = row.id || ids.value[0]
    const response = await getJob(jobId)
    Object.assign(form.value, response.data)
    // 处理标签
    form.value.tagArray = form.value.tags ? form.value.tags.split(',') : []
    open.value = true
    title.value = "修改职位"
  }
  
  /** 提交按钮 */
  const submitForm = async () => {
    const formEl = jobFormRef.value
    if (!formEl) return
    await formEl.validate(async (valid) => {
      if (valid) {
        // 处理标签
        form.value.tags = form.value.tagArray.join(',')
        
        // 清除HTML标签但保留换行符
        const stripHtml = (html) => {
          return html
          //if (!html) return html
          // 只移除HTML标签，保留换行符
          //return html.replace(/<\/?[^>]+(>|$)/g, '').trim()
        }
        
        // 清除可能包含HTML标签的字段
        form.value.title = stripHtml(form.value.title)
        form.value.description = stripHtml(form.value.description)
        form.value.requirements = stripHtml(form.value.requirements)
        form.value.companyDesc = stripHtml(form.value.companyDesc)
        form.value.department = stripHtml(form.value.department)
        form.value.other = stripHtml(form.value.other)
        form.value.location = stripHtml(form.value.location)
        form.value.companyName = stripHtml(form.value.companyName)
        
        try {
          if (form.value.id) {
            await updateJob(form.value)
          } else {
            await addJob(form.value)
          }
          ElMessage.success('操作成功')
          open.value = false
          getList()
        } catch (error) {
          console.error(error)
          ElMessage.error('操作失败')
        }
      }
    })
  }
  
  /** 删除按钮操作 */
  const handleDelete = async (row) => {
    const jobIds = row.id || ids.value
    try {
      await ElMessageBox.confirm('是否确认删除所选职位？')
      await delJob(jobIds)
      getList()
      ElMessage.success("删除成功")
    } catch (error) {
      console.error(error)
    }
  }
  
  // 多选框选中数据
  const handleSelectionChange = (selection) => {
    ids.value = selection.map(item => item.id)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }
  
  // 文件上传中处理
  const handleFileUploadProgress = (event, file, fileList) => {
    upload.value.isUploading = true
  }
  
  // 文件上传成功处理
  const handleFileSuccess = (response, file, fileList) => {
    upload.value.open = false
    upload.value.isUploading = false
    uploadRef.value.clearFiles()
    if (response.code === 200) {
      ElMessage.success(response.msg)
      getList()
    } else {
      ElMessage.error(response.msg)
    }
  }
  
  // 提交上传文件
  const submitFileForm = () => {
    uploadRef.value.submit()
  }
  
  // 下载模板操作
  const importTemplate = () => {
    download('/talent/job/importTemplate', {}, `职位导入模板.xlsx`)
  }
  
  // 导入按钮操作
  const handleImport = () => {
    upload.value.title = "职位导入"
    upload.value.open = true
  }
  
  // 导出按钮操作
  const handleExport = () => {
    download('/talent/job/export', {
      keyword: queryParams.value.keyword,
      companyId: queryParams.value.companyId
    }, `职位数据_${new Date().getTime()}.xlsx`)
  }
  
  // 获取投递状态类型
  const getStatusType = (status) => {
    const statusMap = {
      '0': 'info',    // 待处理
      '1': 'primary', // 已查看
      '2': 'warning', // 已面试
      '3': 'danger',  // 不合适
      '4': 'success'  // 已录用
    }
    return statusMap[status] || 'info'
  }

  // 获取投递状态文本
  const getStatusText = (status) => {
    const statusMap = {
      '0': '待处理',
      '1': '已查看',
      '2': '已面试',
      '3': '不合适',
      '4': '已录用'
    }
    return statusMap[status] || '未知'
  }

  // 查看投递情况
  const handleViewDelivery = (row) => {
    currentJobId.value = row.id
    deliveryQueryParams.value.jobId = row.id
    deliveryDialogVisible.value = true
    getDeliveryList()
  }

  // 获取投递列表
  const getDeliveryList = async (params) => {
    deliveryLoading.value = true
    // If params provided from pagination component, update the queryParams
    if (params) {
      deliveryQueryParams.value.pageNum = params.page;
      deliveryQueryParams.value.pageSize = params.limit;
    }
    try {
      const response = await listDelivery({
        jobId: currentJobId.value,
        pageNum: deliveryQueryParams.value.pageNum,
        pageSize: deliveryQueryParams.value.pageSize
      })
      console.log("投递列表响应数据:", response)
      deliveryList.value = response.rows
      deliveryTotal.value = response.total
    } catch (error) {
      console.error("获取投递列表失败:", error)
      ElMessage.error("获取投递列表失败")
    }
    deliveryLoading.value = false
  }

  // 查看简历
  const handleViewResume = async (row) => {
    console.log('投递记录数据:', row);
    if (!row.resumeId) {
      ElMessage.warning('未找到简历ID');
      return;
    }
    try {
      const response = await getResume(row.resumeId);
      Object.assign(resumeForm.value, response.data);
      resumeDialogVisible.value = true;
    } catch (error) {
      console.error("获取简历详情失败:", error);
      ElMessage.error("获取简历详情失败");
    }
  }

  // 更新状态
  const handleUpdateStatus = (row) => {
    statusForm.value = {
      id: row.id,
      status: row.status,
      feedback: row.feedback || ''
    }
    statusDialogVisible.value = true
  }

  // 提交状态更新
  const submitStatusForm = async () => {
    try {
      await updateDeliveryStatus(statusForm.value.id, {
        status: statusForm.value.status,
        feedback: statusForm.value.feedback
      })
      ElMessage.success("更新成功")
      statusDialogVisible.value = false
      getDeliveryList()
    } catch (error) {
      console.error("更新状态失败:", error)
      ElMessage.error("更新状态失败")
    }
  }
  
  // 获取状态数量
  const getStatusCount = (status) => {
    return deliveryList.value.filter(item => item.status === status).length;
  };
  
  // 公司名称变化处理
  const handleCompanyChange = (companyId) => {
    if (!companyId) {
      // 清空公司相关信息
      form.value.companyLogo = '';
      form.value.companyDesc = '';
      return;
    }
    
    // 从已加载的公司列表中查找选中的公司
    const selectedCompany = companies.value.find(item => item.id === companyId);
    if (selectedCompany) {
      form.value.companyLogo = selectedCompany.logo || '';
      form.value.companyDesc = selectedCompany.description || '';
    }
  }


  
  onMounted(() => {
    getList()
    getCompanies()
  })
  </script>
  
  <style scoped>
  .mb8 {
    margin-bottom: 8px;
  }

  .delivery-stats {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-body {
    text-align: center;
    padding: 10px 0;
  }

  .number {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
  }

  .el-card {
    margin-bottom: 20px;
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
  }
  </style>