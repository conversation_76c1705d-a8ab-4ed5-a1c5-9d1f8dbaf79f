<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ResumeProjectMapper">
    
    <resultMap type="ResumeProject" id="ProjectResult">
        <id     property="id"          column="id"          />
        <result property="resumeId"    column="resume_id"   />
        <result property="name"        column="name"        />
        <result property="role"        column="role"        />
        <result property="startDate"   column="start_date"  />
        <result property="endDate"     column="end_date"    />
        <result property="description" column="description" />
        <result property="achievement" column="achievement" />
    </resultMap>

    <select id="selectByResumeId" parameterType="String" resultMap="ProjectResult">
        select * from resume_project 
        where resume_id = #{resumeId}
        order by start_date desc
    </select>

    <insert id="insert" parameterType="ResumeProject">
        insert into resume_project (
            id, resume_id, name, role, start_date,
            end_date, description, achievement
        ) values (
            #{id}, #{resumeId}, #{name}, #{role}, #{startDate},
            #{endDate}, #{description}, #{achievement}
        )
    </insert>

    <update id="update" parameterType="ResumeProject">
        update resume_project
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="role != null">role = #{role},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="description != null">description = #{description},</if>
            <if test="achievement != null">achievement = #{achievement},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="String">
        delete from resume_project where id = #{id}
    </delete>

    <delete id="deleteByResumeId" parameterType="String">
        delete from resume_project where resume_id = #{resumeId}
    </delete>
</mapper> 