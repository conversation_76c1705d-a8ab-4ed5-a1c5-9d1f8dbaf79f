<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TalentMessageMapper">

    <resultMap type="TalentMessage" id="MessageResult">
        <id     property="id"       column="id"        />
        <result property="userId"   column="user_id"   />
        <result property="userName" column="user_name" />
        <result property="senderId" column="sender_id" />
        <result property="senderName" column="sender_name" />
        <result property="title"    column="title"     />
        <result property="content"  column="content"   />
        <result property="type"     column="type"      />
        <result property="status"   column="status"    />
        <result property="relatedId" column="related_id"/>
        <result property="readTime" column="read_time" />
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectMessageList" parameterType="TalentMessage" resultMap="MessageResult">
        select m.*, 
               u.nick_name as user_name,
               s.nick_name as sender_name
        from talent_message m
        left join sys_user u on m.user_id = u.user_id
        left join sys_user s on m.sender_id = s.user_id
        <where>
            <if test="userId != null">AND m.user_id = #{userId}</if>
            <if test="type != null and type != ''">AND m.type = #{type}</if>
            <if test="status != null and status != ''">AND m.status = #{status}</if>
        </where>
        order by m.create_time desc
    </select>

    <select id="selectUnreadCount" parameterType="Long" resultType="Integer">
        select count(*) from talent_message
        where user_id = #{userId} and status = 'UNREAD'
    </select>

    <update id="updateMessageRead" parameterType="String">
        update talent_message
        set status = 'READ',
            read_time = sysdate()
        where id = #{id}
    </update>

    <update id="updateAllMessageRead" parameterType="Long">
        update talent_message
        set status = 'READ',
            read_time = sysdate()
        where user_id = #{userId} and status = 'UNREAD'
    </update>

    <insert id="insertMessage" parameterType="TalentMessage">
        insert into talent_message (
            id, user_id, sender_id, title, content, type, status,
            related_id, create_time, update_time
        ) values (
            #{id}, #{userId}, #{senderId}, #{title}, #{content}, #{type}, #{status},
            #{relatedId}, sysdate(), sysdate()
        )
    </insert>
</mapper>