<template>
  <div class="dashboard">
    <header>
      <h3>更新于2024年7月</h3>
    </header>
    
    <section class="overview">
      <div class="card">
        <h3>员工总数</h3>
        <p class="highlight">{{ totalEmployees }}人</p>
      </div>
      <div class="card">
        <h3>本月入职</h3>
        <p class="highlight">{{ newHires }}人</p>
      </div>
      <div class="card">
        <h3>员工流失率</h3>
        <p class="highlight">{{ turnoverRate }}%</p>
      </div>
    </section>

    <section class="charts">
      <div class="chart">
        <h3>部门人员分布</h3>
        <div ref="departmentDistributionChart" class="chart-container"></div>
      </div>
      <div class="chart">
        <h3>月度招聘趋势</h3>
        <div ref="monthlyRecruitmentTrendChart" class="chart-container"></div>
      </div>
    </section>

    <section class="hr-metrics">
      <h3>人力资源关键指标</h3>
      <div class="metric-grid">
        <div v-for="metric in hrMetrics" :key="metric.name" class="metric-card">
          <h4>{{ metric.name }}</h4>
          <p>{{ metric.value }}</p>
          <p>同比：<span :class="['trend', metric.trend > 0 ? 'up' : 'down']">
            {{ metric.trend > 0 ? '+' : '' }}{{ metric.trend }}%
          </span></p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const totalEmployees = ref(0)
const newHires = ref(0)
const turnoverRate = ref(0)
const hrMetrics = ref([])
const departmentDistributionChart = ref(null)
const monthlyRecruitmentTrendChart = ref(null)

onMounted(() => {
  fetchDashboardData()
  renderCharts()
})

const fetchDashboardData = async () => {
  // 模拟API调用
  totalEmployees.value = 1000
  newHires.value = 50
  turnoverRate.value = 5.2
  hrMetrics.value = [
    { name: '人均培训时长', value: '20小时', trend: 10 },
    { name: '员工满意度', value: '85%', trend: 5 },
    { name: '绩效达标率', value: '92%', trend: 3 },
    { name: '人均产值', value: '¥50万', trend: 8 }
  ]
}

const renderCharts = () => {
  const departmentDistributionInstance = echarts.init(departmentDistributionChart.value)
  const monthlyRecruitmentTrendInstance = echarts.init(monthlyRecruitmentTrendChart.value)

  const departmentDistributionOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}人 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '部门分布',
        type: 'pie',
        radius: '70%',
        data: [
          { value: 300, name: '公司总部' },
          { value: 200, name: '钱江水利' },
          { value: 150, name: '华东区域总部' },
          { value: 100, name: '华南区域总部' },
          { value: 250, name: '华北区域总部' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  }

  const monthlyRecruitmentTrendOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLabel: {
        fontSize: 8,
        interval: 1,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '招聘人数',
      nameTextStyle: {
        fontSize: 8
      },
      axisLabel: {
        fontSize: 8
      }
    },
    grid: {
      left: '10%',
      right: '5%',
      bottom: '15%',
      top: '10%'
    },
    series: [
      {
        data: [30, 40, 35, 50, 45, 60, 70, 65, 55, 40, 50, 45],
        type: 'line',
        smooth: true,
        areaStyle: {}
      }
    ]
  }

  departmentDistributionInstance.setOption(departmentDistributionOption)
  monthlyRecruitmentTrendInstance.setOption(monthlyRecruitmentTrendOption)

  window.addEventListener('resize', () => {
    departmentDistributionInstance.resize()
    monthlyRecruitmentTrendInstance.resize()
  })
}
</script>

<style scoped>
.dashboard {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  padding: 0.5rem;
  background-color: #f5f7fa;
  color: #333;
}

header {
  text-align: left;
  padding-top: 0.5rem;
  margin-bottom: 0.5rem; 
  color: blue;
}

h1 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.overview {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.card {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  flex: 1;
  margin: 0 0.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.card:first-child {
  margin-left: 0;
}

.card:last-child {
  margin-right: 0;
}

.highlight {
  color: #3498db;
  font-size: 1rem;
  font-weight: bold;
}

.charts {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.chart {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  margin-bottom: 1rem;
  height: 250px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  height: 100%;
}

.hr-metrics h3 {
  margin-bottom: 0.5rem;
}

.metric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

.metric-card {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metric-card h4 {
  color: #2c3e50;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.trend {
  font-weight: bold;
}

.trend.up {
  color: #2ecc71;
}

.trend.down {
  color: #e74c3c;
}

h3 {
  font-size: 1rem;
  margin-bottom: 0.3rem;
}

p {
  font-size: 0.8rem;
  margin-bottom: 0.2rem;
}

@media (max-width: 480px) {
  .card {
    padding: 0.3rem;
  }

  .card h3 {
    font-size: 0.8rem;
  }

  .highlight {
    font-size: 0.9rem;
  }

  .chart {
    height: 200px;
  }

  .metric-grid {
    grid-template-columns: 1fr 1fr;
  }

  h1 {
    font-size: 1.3rem;
  }

  h3 {
    font-size: 0.9rem;
  }

  p {
    font-size: 0.7rem;
  }
}
</style>