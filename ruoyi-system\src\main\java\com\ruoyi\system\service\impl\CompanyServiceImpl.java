package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CompanyMapper;
import com.ruoyi.system.domain.Company;
import com.ruoyi.system.service.ICompanyService;

@Service
public class CompanyServiceImpl implements ICompanyService {
    @Autowired
    private CompanyMapper companyMapper;

    /**
     * 查询公司列表
     * 
     * @param company 公司信息
     * @return 公司集合
     */
    @Override
    public List<Company> selectCompanyList(Company company) {
        return companyMapper.selectCompanyList(company);
    }

    /**
     * 查询公司信息
     * 
     * @param id 公司ID
     * @return 公司信息
     */
    @Override
    public Company selectCompanyById(Long id) {
        return companyMapper.selectCompanyById(id);
    }

    /**
     * 新增公司
     * 
     * @param company 公司信息
     * @return 结果
     */
    @Override
    public int insertCompany(Company company) {
        // 设置创建时间和更新时间
        company.setCreateTime(new Date());
        company.setUpdateTime(new Date());
        return companyMapper.insertCompany(company);
    }

    /**
     * 修改公司
     * 
     * @param company 公司信息
     * @return 结果
     */
    @Override
    public int updateCompany(Company company) {
        // 设置更新时间
        company.setUpdateTime(new Date());
        return companyMapper.updateCompany(company);
    }

    /**
     * 删除公司信息
     * 
     * @param id 公司ID
     * @return 结果
     */
    @Override
    public int deleteCompanyById(Long id) {
        return companyMapper.deleteCompanyById(id);
    }

    /**
     * 批量删除公司
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteCompanyByIds(String[] ids) {
        return companyMapper.deleteCompanyByIds(ids);
    }
    
    /**
     * 获取公司总数
     * 
     * @return 公司总数
     */
    @Override
    public int countTotalCompanies() {
        return companyMapper.countTotalCompanies();
    }
} 