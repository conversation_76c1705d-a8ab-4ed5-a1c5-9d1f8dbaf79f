<template>
  <view class="main">
    <!-- 我的 -->
    <view class="title-bg">
      <view class="containers" v-if="isLogin">
        <button>
          <image :src="headimgurl" mode="aspectFill"></image> 
          <view>{{ nickname }}</view>
        </button>
      </view>
      
      <view class="containers" v-else>
        <button @click="login()">
          <image src="/static/images/login.png" mode="aspectFill"></image> 
          <view>点击授权登录</view>
        </button>
      </view>
    </view>

	<view class="menu-bottom">
      <view class="menu menu-border-bottom" @click="exitLogin()" v-if="isLogin">
        <view class="menul">
          <image src="/static/images/exit.png" mode="aspectFill"></image>
        </view>
        <view class="menur">
          <view class="menur-text">退出登录</view>
          <view class="menur-img"><image src="/static/images/right.png" mode="aspectFill"></image></view>
        </view>
      </view>
    </view> 
  </view>
</template>

<script setup>

import { ref,getCurrentInstance,onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import config from '@/config';

const isLogin = ref(false);
const nickname = ref('');
const headimgurl = ref('');
const userId = ref('');
let token = null;
const {
	appContext: {
		config: {
			globalProperties: global
		}
	}
} = getCurrentInstance();
// const cns = getCurrentInstance();
onMounted(() => {
  // window.onpageshow = (evt) => {
  //   setTimeout(() => {
  //     if (evt.persisted) {
  //       location.reload(true);
  //     }
  //   });
  // };

  // const res_userId = uni.getStorageSync("userId");
  
  // if (res_userId !== "") {
  //   nickname.value = uni.getStorageSync("nickname");
  //   headimgurl.value = uni.getStorageSync("headimgurl");
  //   userId.value = uni.getStorageSync("userId");
  //   isLogin.value = true;
  // }
});

onShow(() => {
  checkWeChatCode();
});

// 方法:用来提取code
const getUrlCode = (name) => {
  return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1]
    .replace(/\+/g, '%20')) || null;
};

// 检查浏览器地址栏中微信接口返回的code
const checkWeChatCode = () => {
  const code = getUrlCode('code');
  const res_code = uni.getStorageSync("code");
  if (!token) {
    if (code && code !== res_code) {
	  uni.setStorageSync("code",code);
      global.$myRequest({
        url: 'enterpriseLogin',
        method: 'get',
        data: { code }
      })
	 
      .then(res => {
		console.log("-----：",res);
        if (res.data.code === 200) {
          let token = res.data.data.token;
          uni.setStorageSync("token", token);
          
          
          window.history.back();
          // uni.switchTab({ url: './index/index' });
        } else {
          // window.history.back();
          uni.showToast({
            title: '登录失败' + res.data.data,
            icon: 'none',
            duration: 1000
          });
        }
      })
      .catch(err => {
        window.history.back();
        uni.showToast({
          title: '请求异常' + err.errMsg,
          icon: 'none',
          duration: 1500
        });
      });
    } else if (code) {
      uni.showToast({
        title: '已处于登录状态',
        icon: 'none',
        duration: 1500
      });
    }
  }
};

const login = () => {
  const local = "http://qwtest.chinahho.com:18083/#/pages/talent-market/talent-market";
  const appid = config.wx.appid;
  const agentid = config.wx.agentid;
  const scope = config.wx.scope;
  //手动授权
  location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${local}&response_type=code&scope=${scope}&state=STATE&connect_redirect=1&agentid=${agentid}#wechat_redirect`;
  //静默授权
  // location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${local}&response_type=code&scope=snsapi_userinfo&state=STATE&connect_redirect=1#wechat_redirect`;
};

const exitLogin = () => {
  uni.showModal({
    title: "温馨提示",
    content: "你确定退出登录吗？",
    cancelColor: '#999494',
    confirmColor: '#203c42',
    success(res) {
      if (res.confirm) {
        isLogin.value = false;
        uni.removeStorageSync("nickname");
        uni.removeStorageSync("headimgurl");
        uni.removeStorageSync("userId");
        uni.removeStorageSync("code");
        uni.showToast({
          title: '退出成功',
          icon: 'none',
          duration: 1000
        });
        // setTimeout(() => {
        //   uni.switchTab({
        //     url: '../index/index'
        //   });
        // }, 1000);
      }
    }
  });
};
</script>

<style>
page {
  background: #f7f7f7;
}

.title-bg {
  height: 380rpx;
  /* background-image: url(../../static/images/my_bg.png); */
  background-size: 100% 100%;
}

.title-bg .containers {
  margin: auto;
  text-align: center;
  padding-top: 80rpx;
  height: 300rpx;
  backdrop-filter: blur(4rpx);
}

.title-bg .containers button {
  display: block;
  background: none;
  padding: 0;
  border: 0;
  line-height: 0;
  width: 300rpx;	
}

.title-bg .containers button::after { 
  border: none;
}

.title-bg .containers button image {
  width: 170rpx;
  height: 170rpx;
  border-radius: 50%;
}

.title-bg .containers button view {
  margin-top: 10rpx;
  height: 80rpx;
  line-height: 80rpx; 
  font-size: 38rpx;
  color: #222222;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.menu-border-bottom {
  border-bottom: 1px solid #f2f2f2;
}

.main .menu-bottom {
  margin-top: 20rpx;
}

.main .menu-bottom:last-child {
  padding-bottom: 150rpx;
}

.main .quit {
  text-align: center;
  padding: 40rpx 0px;
  color: #90948d;
  margin-top: 20px;
}
</style>