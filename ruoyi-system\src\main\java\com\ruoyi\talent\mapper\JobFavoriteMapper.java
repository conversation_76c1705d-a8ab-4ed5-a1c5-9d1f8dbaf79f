package com.ruoyi.talent.mapper;

import com.ruoyi.talent.domain.JobFavorite;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 职位收藏Mapper接口
 */
public interface JobFavoriteMapper {
    /**
     * 查询用户收藏的职位列表
     */
    public List<JobFavorite> selectJobFavoriteList(JobFavorite jobFavorite);

    /**
     * 新增职位收藏
     */
    public int insertJobFavorite(JobFavorite jobFavorite);

    /**
     * 删除职位收藏
     */
    public int deleteJobFavorite(Long id);

    /**
     * 批量删除职位收藏
     */
    public int deleteJobFavoriteByIds(Long[] ids);

    /**
     * 检查是否已收藏
     */
    public JobFavorite checkIsFavorite(@Param("userId") Long userId, @Param("jobId") Long jobId);
} 