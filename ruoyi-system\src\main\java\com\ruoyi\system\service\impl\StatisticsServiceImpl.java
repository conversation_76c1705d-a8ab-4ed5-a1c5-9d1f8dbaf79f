package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.StatisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TotalStatisticsMapper;
import com.ruoyi.system.mapper.DepartmentStatisticsMapper;
import com.ruoyi.system.service.IStatisticsService;

import java.util.Date;

@Service
public class StatisticsServiceImpl implements IStatisticsService {

    @Autowired
    private TotalStatisticsMapper totalStatisticsMapper;

    @Autowired
    private DepartmentStatisticsMapper departmentStatisticsMapper;

    @Override
    public StatisticsVO getLatestStatistics() {
        StatisticsVO vo = new StatisticsVO();
        vo.setTotalStatistics(totalStatisticsMapper.selectLatestTotalStatistics());
        vo.setDepartmentStatistics(departmentStatisticsMapper.selectLatestDepartmentStatistics());
        return vo;
    }

    @Override
    public StatisticsVO getStatisticsByDate(Date statisticsDate) {
        StatisticsVO vo = new StatisticsVO();
        vo.setTotalStatistics(totalStatisticsMapper.selectTotalStatisticsByDate(statisticsDate));
        vo.setDepartmentStatistics(departmentStatisticsMapper.selectDepartmentStatisticsByDate(statisticsDate));
        return vo;
    }
}
