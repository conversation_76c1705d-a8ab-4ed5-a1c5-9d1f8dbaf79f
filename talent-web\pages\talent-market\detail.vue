<template>
  <view class="job-detail">
    <!-- 职位信息卡片 -->
    <view class="job-card">
      <!-- 职位标题 -->
      <view class="job-title">{{ jobDetail.title }}</view>

      <!-- 基本信息 -->
      <view class="job-info">
        <view class="info-row">
          <text class="info-item">
            <uni-icons type="location" size="14" color="#666"></uni-icons>
            {{ jobDetail.location || '工作地点待定' }}
          </text>
          <text class="info-item">
            <uni-icons type="calendar" size="14" color="#666"></uni-icons>
            {{ jobDetail.experience === '无要求' ? '经验不限' : (jobDetail.experience || '经验不限') }}
          </text>
        </view>
        <view class="info-row">
          <text class="info-item">
            <uni-icons type="staff" size="14" color="#666"></uni-icons>
            {{ jobDetail.education === '无要求' ? '学历不限' : (jobDetail.education || '学历不限') }}
          </text>
          <text class="info-item">
            <uni-icons type="person" size="14" color="#666"></uni-icons>
            {{ jobDetail.headcount || 1 }}人
          </text>
        </view>
      </view>

      <!-- 公司信息 -->
      <view class="company-info">
        <view class="company-name">
          <uni-icons type="home" size="16" color="#333"></uni-icons>
          {{ jobDetail.company }}
        </view>
        <view class="company-tags" v-if="jobDetail.department || jobDetail.demandType">
          <text class="tag" v-if="jobDetail.department">{{ jobDetail.department }}</text>
          <text class="tag" v-if="jobDetail.demandType">{{ jobDetail.demandType }}</text>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="job-stats">
        <text class="stat-item">
          <uni-icons type="eye" size="12" color="#999"></uni-icons>
          {{ jobDetail.views || 0 }}浏览
        </text>
        <text class="stat-item">
          <uni-icons type="paperplane" size="12" color="#999"></uni-icons>
          {{ jobDetail.applications || 0 }}投递
        </text>
        <text class="stat-item">
          <uni-icons type="calendar" size="12" color="#999"></uni-icons>
          {{ formatTime(jobDetail.createTime) }}
        </text>
      </view>
    </view>

    <!-- 职位描述 -->
    <view class="content-card">
      <view class="section-title">
        <uni-icons type="list" size="16" color="#007AFF"></uni-icons>
        职位描述
      </view>
      <rich-text v-if="jobDetail.description && jobDetail.description.trim()" class="content-text" :nodes="processedDescription"></rich-text>
      <text v-else class="no-content">无要求</text>
    </view>

    <!-- 任职要求 -->
    <view class="content-card">
      <view class="section-title">
        <uni-icons type="checkmarkempty" size="16" color="#52c41a"></uni-icons>
        任职要求
      </view>
      <rich-text v-if="jobDetail.requirements && jobDetail.requirements.trim()" class="content-text" :nodes="processedRequirements"></rich-text>
      <text v-else class="no-content">无要求</text>
    </view>

    <!-- 其他信息 -->
    <view class="content-card">
      <view class="section-title">
        <uni-icons type="info" size="16" color="#fa8c16"></uni-icons>
        其他信息
      </view>
      <view class="other-info">
        <view class="other-item">
          <text class="other-label">目标范围：</text>
          <text class="other-value">{{ jobDetail.targetRange || '无要求' }}</text>
        </view>
        <view class="other-item">
          <text class="other-label">备注：</text>
          <rich-text v-if="jobDetail.other && jobDetail.other.trim()" class="other-value" :nodes="processedOther"></rich-text>
          <text v-else class="other-value">无要求</text>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="action-bar">
      <view class="action-left">
        <button class="action-btn collect" @click="toggleCollect">
          <text class="yzb" :class="isCollected ? 'yzb-shoucang1' : 'yzb-shoucang'"></text>
          <text class="btn-text">{{ isCollected ? '已收藏' : '收藏' }}</text>
        </button>
      </view>
      <view class="action-right">
        <button class="apply-button" :class="{ 'is-applied': hasApplied }" @click="hasApplied ? goToDeliveryRecord() : showResumeSelect()">
          <text class="iconfont" :class="hasApplied ? 'icon-check' : 'icon-send'"></text>
          <text class="btn-text">{{ hasApplied ? '查看投递' : '立即投递' }}</text>
        </button>
      </view>
    </view>

    <!-- 简历选择弹窗 -->
    <uni-popup ref="showPopup" type="bottom" :mask-click="true" @change="change">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">选择投递简历</text>
          <text class="popup-close" @click="closePopup">×</text>
        </view>
        
        <view class="popup-body">
          <!-- 空状态展示 -->
          <template v-if="myResumes.length === 0">
            <view class="empty-state">
              <text class="iconfont icon-resume empty-icon"></text>
              <view class="empty-text">
                <text class="empty-title">暂无简历</text>
                <text class="empty-desc">创建一份简历开启求职之旅</text>
              </view>
              <view class="button-group">
                <button class="primary-button" @click="goToCreateResume">
                  <text class="iconfont icon-plus"></text>
                  <text class="btn-text">创建简历</text>
                </button>
                <!-- <button class="upload-button" @click="uploadWordResume">
                  <text class="iconfont icon-upload"></text>
                  <text class="btn-text">上传Word简历</text>
                </button> -->
              </view>
            </view>
          </template>
          
          <!-- 简历列表 -->
          <template v-else>
            <scroll-view scroll-y class="resume-list">
              <view v-for="(item, index) in myResumes" :key="index" class="resume-item" @click="selectResume(item)">
                <view class="resume-info">
                  <text class="resume-title">{{item.resumeTitle}}</text>
                  <text class="resume-completeness">完整度 {{item.completeness}}%</text>
                </view>
                <view class="resume-status" :class="{'is-default': item.isDefault}">
                 确认投递
                </view>
              </view>
            </scroll-view>
          </template>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<style>
/* 基础样式 */
.job-detail {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 16px;
  padding-bottom: 120px;
}

/* 卡片通用样式 */
.job-card, .content-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 职位标题 */
.job-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
}

/* 基本信息 */
.job-info {
  margin-bottom: 12px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-item {
  flex: 1;
  font-size: 14px;
  color: #666;
  margin-right: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-item:last-child {
  margin-right: 0;
}

/* 公司信息 */
.company-info {
  margin-bottom: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.company-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.company-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 12px;
  background: #f0f0f0;
  color: #666;
  border-radius: 16px;
  font-size: 12px;
}

/* 统计信息 */
.job-stats {
  display: flex;
  gap: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  flex-wrap: wrap;
}

.stat-item {
  font-size: 13px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 内容区域 */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 2px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.content-text {
  font-size: 15px;
  color: #666;
  line-height: 1.8;
  white-space: pre-wrap;
}

.no-content {
  font-size: 15px;
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 12px 0;
}

/* 其他信息 */
.other-info {
  margin-top: 12px;
}

.other-item {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
}

.other-label {
  font-size: 14px;
  color: #999;
  min-width: 80px;
  flex-shrink: 0;
}

.other-value {
  font-size: 14px;
  color: #666;
  flex: 1;
}



/* 标签样式 */
/* .job-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 16px;
}

.tag {
  padding: 8px 16px;
  background: linear-gradient(135deg, #f0f2f5 0%, #e6e9f0 100%);
  color: #666;
  border-radius: 8px;
  font-size: 13px;
  transition: all 0.3s ease;
  white-space: nowrap;
} */


/* 元信息样式 */
.job-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 24px 0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;

}

.meta-text {
  color: #666;
  font-size: 14px;
}

/* 公司信息样式 */
.company-info-detail {
  padding: 24px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  margin-top: 24px;
}

.company-header {
  margin-bottom: 24px;
}

.company-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12px;
  display: block;
  background: linear-gradient(90deg, #409EFF 0%, #36CFC9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  padding-left: 12px;
}

.company-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: linear-gradient(180deg, #409EFF 0%, #36CFC9 100%);
  border-radius: 2px;
}

.company-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.company-tag {
  padding: 8px 16px;
  background: linear-gradient(135deg, #f0f2f5 0%, #e6e9f0 100%);
  color: #666;
  border-radius: 8px;
  font-size: 13px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

/* 统计信息样式 */
.job-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 24px;
}

/* .stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
} */

.stat-text {
  color: #666;
  font-size: 13px;
}





/* 底部操作栏样式 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
  z-index: 100;
  height: 60px;
}

.action-left {
  display: flex;
  gap: 16px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 10px;
  font-size: 14px;
  background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
  border: none;
  color: #666;
  height: 44px;
  transition: all 0.3s ease;
  min-width: 100px;
  justify-content: center;
}

.btn-text {
  font-size: 14px;
  font-weight: 500;
}

.apply-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #007AFF 0%, #00C6FF 100%);
  border-radius: 12px;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
}

.apply-button.is-applied {
  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
  box-shadow: 0 4px 12px rgba(52, 199, 89, 0.2);
}

/* 弹窗样式 */
.popup-content {
  background-color: #fff;
  border-radius: 24px 24px 0 0;
  padding-bottom: calc(env(safe-area-inset-bottom) + 50px);
  max-height: 80vh;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  bottom: 50px;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding:5px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 10;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  background: linear-gradient(90deg, #333 0%, #666 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.popup-close {
  font-size: 24px;
  color: #999;
  padding: 10px;
  transition: all 0.3s ease;
}

.popup-body {
  padding: 0 24px;
  max-height: calc(80vh - 120px);
  overflow-y: auto;
}

.resume-list {
  max-height: calc(80vh - 200px);
  padding: 16px 0;
  overflow-y: auto;
}

.resume-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  margin-bottom: 12px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
}

.resume-info {
  flex: 1;
  margin-right: 16px;
}

.resume-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  display: block;
  font-weight: 500;
}

.resume-completeness {
  font-size: 13px;
  color: #666;
  background: rgba(0, 0, 0, 0.02);
  padding: 4px 12px;
  border-radius: 6px;
  display: inline-block;
}

.resume-status {
  font-size: 14px;
  color: #007AFF;
  padding: 8px 16px;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%);
  transition: all 0.3s ease;
  white-space: nowrap;
}

.resume-status.is-default {
  color: #666;
  background: linear-gradient(135deg, #f5f5f5 0%, #f0f0f0 100%);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
  text-align: center;
}

.empty-icon {
  font-size: 80px;
  color: #ccc;
  margin-bottom: 24px;
  opacity: 0.8;
}

.empty-text {
  text-align: center;
  margin-bottom: 32px;
}

.empty-title {
  font-size: 18px;
  color: #333;
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
}

.empty-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  padding: 0 20px;
  margin-top: 24px;
  align-items: center;
}

.primary-button {
  background: linear-gradient(135deg, #007AFF 0%, #00C6FF 100%);
  color: #fff;
  font-size: 16px;
  padding: 12px 32px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
  width: 240px;
  height: 48px;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 200px;
  height: 44px;
  background: #f5f7fa;
  color: #007AFF;
  font-size: 15px;
  border-radius: 10px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 122, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
}

.upload-button:active {
  transform: translateY(2px);
  background: #eef2f7;
  box-shadow: 0 1px 4px rgba(0, 122, 255, 0.1);
}

.upload-button .iconfont {
  font-size: 16px;
  color: #007AFF;
}

.primary-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.15);
}

/* 收藏按钮样式 */
.collect .yzb-shoucang1 {
  color: #ff4d4f;
  font-size: 20px;
}

.collect .yzb-shoucang {
  color: #666;
  font-size: 20px;
}

.collect:active {
  transform: scale(0.95);
}
</style>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { 
  getJobDetail, 
  getMyResumes,
  applyJob, 
  collectJob,
  getJobApplyStatus,
  increaseJobViews,
  checkFavorite,
  toggleFavorite
} from '@/utils/talent-market'
import { createHttp } from '@/utils/request'

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  // 小于1小时，显示"xx分钟前"
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000)
    return `${minutes}分钟前`
  }
  
  // 小于24小时，显示"xx小时前"
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000)
    return `${hours}小时前`
  }
  
  // 小于7天，显示"xx天前"
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000)
    return `${days}天前`
  }
  
  // 超过7天，显示具体日期
  return date.toLocaleDateString('zh-CN')
}

// 处理HTML内容，确保换行符正确显示
const processHtml = (html) => {
  if (!html) return ''
  // 将\n转换为<br>标签，确保换行正确显示
  return html.replace(/\n/g, '<br>')
}

// 响应式数据
const jobDetail = ref({})

// 处理后的HTML内容
const processedDescription = computed(() => processHtml(jobDetail.value.description))
const processedRequirements = computed(() => processHtml(jobDetail.value.requirements))
const processedOther = computed(() => processHtml(jobDetail.value.other))
const hasApplied = ref(false)
const isCollected = ref(false)
const selectedResumeId = ref(null)
const showPopup = ref(null)
const myResumes = ref([])
const activeStep = ref(0)  // 添加投递进度状态
const applicationStatus = ref([  // 添加投递状态时间线
  { title: '简历投递', date: '', active: false },
  { title: 'HR审核', date: '', active: false },
  { title: '安排面试', date: '', active: false },
  { title: '录用', date: '', active: false }
])

// 获取职位详情并增加浏览量
onMounted(async () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const jobId = currentPage.$page?.options?.id
  
  const token = uni.getStorageSync('token')
  try {
    // 先获取职位详情
    const result = await getJobDetail(token, jobId)
    if (result && result.code === 200) {
      jobDetail.value = result.data
      
      // 立即检查收藏状态
      const favoriteResult = await checkFavorite(token, jobId)
      if (favoriteResult && favoriteResult.code === 200) {
        isCollected.value = Boolean(favoriteResult.isFavorite)
      } else {
        // 如果获取收藏状态失败，默认设置为未收藏
        isCollected.value = false
      }
      
      // 检查投递状态
      await checkApplyStatus()
    }
  } catch (error) {
    console.error('获取职位详情失败:', error)
    uni.showToast({
      title: '获取职位详情失败',
      icon: 'none'
    })
  }
})

// 检查是否已收藏
const checkIsFavorite = async () => {
  try {
  const token = uni.getStorageSync('token')
    const jobId = jobDetail.value.id
    if (!jobId) return
    
    const result = await checkFavorite(token, jobId)
    if (result && result.code === 200) {
      isCollected.value = Boolean(result.isFavorite)
      console.log('收藏状态:', isCollected.value)
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error)
    // 默认设置为未收藏
    isCollected.value = false
  }
}

// 切换收藏状态
const toggleCollect = async () => {
  try {
    const token = uni.getStorageSync('token')
    const result = await toggleFavorite(token, jobDetail.value.id)
    if (result.code === 200) {
      // Toggle the local state
    isCollected.value = !isCollected.value
      // 使用 success 样式
      if (isCollected.value) {
    uni.showToast({
          title: '收藏成功',
          icon: 'success',
          duration: 2000
        })
      } else {
        // 取消收藏使用 none 样式
        uni.showToast({
          title: '已取消收藏',
          icon: 'none',
          duration: 2000
        })
      }
    } else {
      // 失败使用 none 样式，因为uni-app不支持error图标
      uni.showToast({
        title: result.msg || '操作失败',
        icon: 'none',
        duration: 2000
    })
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none',
      duration: 2000
    })
  }
}

// 获取我的简历列表
const getResumesList = async () => {
  const token = uni.getStorageSync('token')
  try {
    const result = await getMyResumes(token)  // 使用已有的 API
    if (result && result.code === 200) {
      myResumes.value = result.rows.map(resume => ({
        id: resume.id,
        resumeTitle: resume.resumeTitle,
        completeness: resume.completeness || 0,
        isDefault: resume.isDefault || false
      }))
    } else {
      throw new Error(result?.message || '获取简历列表失败')
    }
  } catch (error) {
    console.error('获取简历列表失败:', error)
    uni.showToast({
      title: '获取简历列表失败',
      icon: 'none'
    })
  }
}

// 显示简历选择弹窗
const showResumeSelect = () => {
  if (hasApplied.value) return
  showPopup.value.open();
  getResumesList() // 获取简历列表
}

// 关闭弹窗
const closePopup = () => {
  if (showPopup.value) {
    showPopup.value.close('bottom')
  }
}

// 选择简历
const selectResume = async (resume) => {
  try {
    const token = uni.getStorageSync('token')
    const result = await applyJob(token, jobDetail.value.id, resume.id)
    
    if (result.code === 200) {
      // 投递成功
      hasApplied.value = true
      uni.showToast({
        title: '投递成功',
        icon: 'success'
      })
      showPopup.value.close()
    } else {
      // 投递失败
      uni.showToast({
        title: result.msg || '投递失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('投递失败:', error)
    uni.showToast({
      title: '投递失败，请重试',
      icon: 'none'
    })
  }
}

// 检查投递状态
const checkApplyStatus = async () => {
  try {
    const token = uni.getStorageSync('token')
    const jobId = jobDetail.value.id
    if (!jobId) return

    console.log('检查投递状态，jobId:', jobId)
    const result = await getJobApplyStatus(token, jobId)
    console.log('投递状态结果:', result)
    
    if (result.code === 200 && result.data) {
      // 根据 deliveryTime 判断是否已投递，但排除已取消的投递（状态为"-1"）
      hasApplied.value = !!result.data.deliveryTime && result.data.status !== '-1'
      console.log('hasApplied 设置为:', hasApplied.value)
      
      // 更新投递进度
      if (result.data.status === 'pending') {
        activeStep.value = 1  // 简历投递
      } else if (result.data.status === 'reviewing') {
        activeStep.value = 2  // HR审核
      } else if (result.data.status === 'interviewing') {
        activeStep.value = 3  // 安排面试
      } else if (result.data.status === 'accepted') {
        activeStep.value = 4  // 录用
      }

      // 更新状态时间
      if (result.data.deliveryTime) {
        applicationStatus.value[0].date = result.data.deliveryTime
        applicationStatus.value[0].active = true
      }
      if (result.data.interviewTime) {
        applicationStatus.value[2].date = result.data.interviewTime
        applicationStatus.value[2].active = true
      }
    }
  } catch (error) {
    console.error('获取投递状态失败:', error)
  }
}

const goToCreateResume = () => {
  if (showPopup.value) {
    showPopup.value.close('bottom')
  }
  // 延迟跳转，等待弹窗关闭动画完成
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/talent-market/resume-edit'
    })
  }, 100)
}

// 跳转到投递记录页面
const goToDeliveryRecord = () => {
  console.log('点击查看投递记录，hasApplied:', hasApplied.value)
  uni.redirectTo({
    url: '/pages/talent-market/delivery-record',
    success: () => {
      console.log('跳转成功')
    },
    fail: (err) => {
      console.error('跳转失败:', err)
      uni.showToast({
        title: '跳转失败，请重试',
        icon: 'none'
      })
    }
  })
}

// 上传Word简历
const uploadWordResume = () => {
  uni.chooseFile({
    count: 1,
    type: 'file',
    extension: ['.doc', '.docx'],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0];
      // 显示上传中提示
      uni.showLoading({
        title: '正在上传...',
        mask: true
      });
      
      // 使用request.js中的upload方法
      const http = createHttp();
      http.upload('/talent/resume/upload/word', tempFilePath)
        .then(result => {
          if (result.code === 200) {
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            });
            // 刷新简历列表
            getResumesList();
            // 关闭弹窗
            if (showPopup.value) {
              showPopup.value.close('bottom');
            }
          } else {
            uni.showToast({
              title: result.msg || '上传失败',
              icon: 'none'
            });
          }
        })
        .catch(error => {
          console.error('上传失败:', error);
          uni.showToast({
            title: '上传失败',
            icon: 'none'
          });
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    fail: (error) => {
      console.error('选择文件失败:', error);
      uni.showToast({
        title: '选择文件失败',
        icon: 'none'
      });
    }
  });
};

</script>