package com.ruoyi.web.controller.talent;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.IResumeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.system.domain.JobApplyRequest;;


@RestController
@RequestMapping("/talent/job")
public class JobDeliveryController extends BaseController {
    
    @Autowired
    private IResumeService resumeService;

    @PostMapping("/apply")
    public AjaxResult apply(@RequestBody JobApplyRequest request) {
        return AjaxResult.success(resumeService.applyJob(request.getResumeId(), request.getJobId()));
    }

    @GetMapping("/apply/status/{jobId}")
    public AjaxResult getApplyStatus(@PathVariable String jobId) {
        return AjaxResult.success(resumeService.getJobApplyStatus(jobId, SecurityUtils.getUserId().toString()));
    }
} 