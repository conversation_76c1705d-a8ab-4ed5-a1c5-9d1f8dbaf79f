package com.ruoyi.web.controller.system;

import java.util.*;

import com.ruoyi.common.config.WxConfig;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.UserDetailsServiceImpl;
import com.ruoyi.system.service.ISysUserService;
import me.chanjar.weixin.cp.api.WxCpOAuth2Service;
import me.chanjar.weixin.cp.api.WxCpUserService;
import me.chanjar.weixin.cp.api.impl.WxCpOAuth2ServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.system.service.ISysMenuService;

import javax.annotation.Resource;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

//    @Autowired
//    private ISysUserEnterpriseService userEnterpriseServiceImpl;

    @Resource
    private WxConfig wxConfig;

    @Autowired
    private ISysUserService userService;

    private WxCpOAuth2Service auth2Service = null;

    private WxCpUserService wxUserService = null;


    /**
     * 企业微信登录
     */
    @GetMapping(value = "/enterpriseLogin")
    @ResponseBody
    @Transactional
    public AjaxResult login(
        @RequestParam("code") String code, 
        @RequestParam(value = "agentId", defaultValue = "1000038") String agentId) {
        AjaxResult ajax = AjaxResult.success();
        try {
            // 用户登录凭证（有效期五分钟）
            if (StringUtils.isEmpty(code)) {
                return AjaxResult.error("登录凭证不能为空");
            }

            if (StringUtils.isEmpty(agentId)) {
                return AjaxResult.error("应用ID不能为空");
            }

            // 使用对应应用的配置初始化服务
            auth2Service = new WxCpOAuth2ServiceImpl(wxConfig.getWxCpService(agentId));
            

            WxCpOauth2UserInfo wxCpOauth2UserInfo = auth2Service.getUserInfo(code);

            if (Objects.nonNull(wxCpOauth2UserInfo)) {
                // 生成令牌，传入 agentId
                String token = loginService.enterpriseLogin(
                    wxCpOauth2UserInfo.getUserId(), 
                    wxCpOauth2UserInfo.getUserTicket(),
                    agentId
                );
                ajax.put(Constants.TOKEN, token);
                log.info("###token####");
                log.info(token);
                return ajax;
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return AjaxResult.error("登录失败");
    }

    /**
     * 登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("/getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
