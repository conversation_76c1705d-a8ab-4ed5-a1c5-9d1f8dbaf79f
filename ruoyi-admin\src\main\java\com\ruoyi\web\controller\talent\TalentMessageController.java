package com.ruoyi.web.controller.talent;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.TalentMessage;
import com.ruoyi.system.service.ITalentMessageService;
import com.ruoyi.system.domain.ResumeDelivery;
import com.ruoyi.system.service.IResumeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/talent/message")
public class TalentMessageController extends BaseController {
    
    @Autowired
    private ITalentMessageService messageService;

    @Autowired
    private IResumeService resumeService;

    @GetMapping("/list")
    public AjaxResult list(TalentMessage message) {
        // 如果不是管理员，则只能查看自己的消息
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            message.setUserId(SecurityUtils.getUserId());
        }
        startPage();
        return AjaxResult.success(messageService.selectMessageList(message));
    }

    @GetMapping("/unread/count")
    public AjaxResult getUnreadCount() {
        Long userId = SecurityUtils.getUserId();
        return AjaxResult.success(messageService.selectUnreadCount(userId));
    }

    @PutMapping("/read/{messageId}")
    public AjaxResult read(@PathVariable String messageId) {
        // 如果是管理员，不允许修改消息状态
        if (SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            return AjaxResult.error("管理员不能修改消息状态");
        }
        return toAjax(messageService.updateMessageRead(messageId));
    }

    @PutMapping("/read/all")
    public AjaxResult readAll() {
        // 如果是管理员，不允许修改消息状态
        if (SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            return AjaxResult.error("管理员不能修改消息状态");
        }
        return toAjax(messageService.updateAllMessageRead(SecurityUtils.getUserId()));
    }

    @PostMapping("/send")
    public AjaxResult send(@RequestBody TalentMessage message) {
        // 检查是否提供了接收者ID
        if (message.getUserId() == null) {
            return AjaxResult.error("消息接收者ID不能为空");
        }
        
        // 设置发送者信息
        Long currentUserId = SecurityUtils.getUserId();
        message.setSenderId(currentUserId);
        message.setSenderName(SecurityUtils.getUsername());
        
        // 生成消息ID
        message.setId(IdUtils.simpleUUID());
        // 设置消息状态为未读
        message.setStatus("UNREAD");

        messageService.sendMessage(message);
        return AjaxResult.success();
    }
} 