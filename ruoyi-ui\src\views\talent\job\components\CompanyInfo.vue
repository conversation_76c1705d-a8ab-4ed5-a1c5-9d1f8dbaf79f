<template>
  <div class="company-info" v-if="companyData">
    <div class="info-header">
      <div class="company-logo">
        <el-avatar 
          v-if="companyData.logo" 
          :src="companyData.logo" 
          :size="64"
        ></el-avatar>
        <el-avatar v-else :size="64" icon="UserFilled"></el-avatar>
      </div>
      <div class="company-basic">
        <h3 class="company-name">{{ companyData.name }}</h3>
        <div class="company-tags">
          <el-tag size="small" type="info" v-if="companyData.scale">{{ companyData.scale }}</el-tag>
          <el-tag size="small" type="success" v-if="companyData.nature">{{ companyData.nature }}</el-tag>
          <el-tag size="small" type="warning" v-if="companyData.industry">{{ companyData.industry }}</el-tag>
        </div>
      </div>
    </div>
    <div class="company-desc" v-if="companyData.description">
      <p>{{ companyData.description }}</p>
    </div>
    <div class="company-location" v-if="companyData.location">
      <i class="el-icon-location"></i>
      <span>{{ companyData.location }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue';
import { getCompany } from "@/api/talent/company";
import { UserFilled } from '@element-plus/icons-vue';

const props = defineProps({
  companyId: {
    type: [Number, String],
    default: null
  }
});

const emit = defineEmits(['update:companyData']);

// 公司数据
const companyData = ref(null);

// 监听公司ID变化
watch(() => props.companyId, (newVal) => {
  if (newVal) {
    fetchCompanyDetails(newVal);
  } else {
    companyData.value = null;
  }
}, { immediate: true });

// 获取公司详情
async function fetchCompanyDetails(id) {
  try {
    const res = await getCompany(id);
    if (res.code === 200) {
      companyData.value = res.data;
      emit('update:companyData', res.data);
    }
  } catch (error) {
    console.error('获取公司详情失败:', error);
  }
}
</script>

<style scoped>
.company-info {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 20px;
  background-color: #f9f9f9;
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.company-logo {
  margin-right: 15px;
}

.company-basic {
  flex: 1;
}

.company-name {
  margin: 0 0 10px 0;
  font-size: 16px;
}

.company-tags {
  display: flex;
  gap: 8px;
}

.company-desc {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.company-location {
  color: #909399;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.company-location i {
  margin-right: 5px;
}
</style> 