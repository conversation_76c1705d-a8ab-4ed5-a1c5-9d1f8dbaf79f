package com.ruoyi.web.controller.talent;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Company;
import com.ruoyi.system.service.ICompanyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.ServletUtils;
import com.github.pagehelper.PageHelper;

@RestController
@RequestMapping("/talent/company")
public class CompanyController extends BaseController {
    @Autowired
    private ICompanyService companyService;

    /**
     * 查询公司列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Company company) {
        // 使用PageHelper.startPage显式设置分页参数，每页显示20条记录
        PageHelper.startPage(ServletUtils.getParameterToInt("pageNum", 1), 
                            ServletUtils.getParameterToInt("pageSize", 20));
        List<Company> list = companyService.selectCompanyList(company);
        return getDataTable(list);
    }

    /**
     * 获取所有公司（不分页，用于下拉选择）
     */
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        Company company = new Company();
        company.setStatus("0"); // 只查询状态正常的公司
        List<Company> list = companyService.selectCompanyList(company);
        return success(list);
    }

    /**
     * 获取公司详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(companyService.selectCompanyById(id));
    }

    /**
     * 新增公司
     */
    @Log(title = "公司管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Company company) {
        return toAjax(companyService.insertCompany(company));
    }

    /**
     * 修改公司
     */
    @Log(title = "公司管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Company company) {
        return toAjax(companyService.updateCompany(company));
    }

    /**
     * 删除公司
     */
    @Log(title = "公司管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        // 将Long[]转换为String[]
        String[] stringIds = new String[ids.length];
        for (int i = 0; i < ids.length; i++) {
            stringIds[i] = String.valueOf(ids[i]);
        }
        return toAjax(companyService.deleteCompanyByIds(stringIds));
    }
    
    /**
     * 获取公司总数
     */
    @GetMapping("/count")
    public AjaxResult count() {
        int count = companyService.countTotalCompanies();
        return success(count);
    }
} 