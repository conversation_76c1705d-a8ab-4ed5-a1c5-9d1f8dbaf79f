package com.ruoyi.web.controller.talent;

import com.ruoyi.system.domain.Resume;
import com.ruoyi.system.domain.ResumeEducation;
import com.ruoyi.system.domain.ResumeWork;
import com.ruoyi.system.domain.ResumeProject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Method;
import java.util.List;

/**
 * PDF生成器包装类
 * 使用反射来避免直接的类依赖，解决DevTools类加载器问题
 */
public class PdfGeneratorWrapper {
    
    private static final Logger logger = LoggerFactory.getLogger(PdfGeneratorWrapper.class);
    
    /**
     * 使用反射调用PDF生成功能
     */
    public static ByteArrayOutputStream generatePdfFromHtmlTemplate(
            Resume resume, 
            List<ResumeEducation> educationList, 
            List<ResumeWork> workList, 
            List<ResumeProject> projectList) throws Exception {
        
        try {
            logger.info("开始使用反射方式生成PDF，简历ID: {}", resume.getId());
            
            // 使用当前线程的类加载器来加载类
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            
            // 加载ResumePdfGenerator类
            Class<?> generatorClass = classLoader.loadClass("com.ruoyi.web.controller.talent.ResumePdfGenerator");
            
            // 获取generatePdfFromHtmlTemplate方法
            Method generateMethod = generatorClass.getDeclaredMethod(
                "generatePdfFromHtmlTemplate", 
                Resume.class, 
                List.class, 
                List.class, 
                List.class
            );
            
            // 调用静态方法
            Object result = generateMethod.invoke(null, resume, educationList, workList, projectList);
            
            if (result instanceof ByteArrayOutputStream) {
                logger.info("PDF生成成功，使用反射方式");
                return (ByteArrayOutputStream) result;
            } else {
                throw new Exception("PDF生成返回类型错误");
            }
            
        } catch (ClassNotFoundException e) {
            logger.error("无法加载ResumePdfGenerator类: {}", e.getMessage());
            throw new Exception("PDF生成功能不可用：类加载失败", e);
        } catch (NoSuchMethodException e) {
            logger.error("无法找到generatePdfFromHtmlTemplate方法: {}", e.getMessage());
            throw new Exception("PDF生成功能不可用：方法不存在", e);
        } catch (Exception e) {
            logger.error("PDF生成过程中发生错误: {}", e.getMessage(), e);
            throw new Exception("PDF生成失败", e);
        }
    }
    
    /**
     * 检查PDF生成功能是否可用
     */
    public static boolean isPdfGenerationAvailable() {
        try {
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            
            // 检查核心类是否可用
            classLoader.loadClass("com.openhtmltopdf.pdfboxout.PdfRendererBuilder");
            classLoader.loadClass("com.ruoyi.web.controller.talent.ResumePdfGenerator");
            
            logger.debug("PDF生成功能检查通过");
            return true;
        } catch (ClassNotFoundException e) {
            logger.warn("PDF生成功能不可用: {}", e.getMessage());
            return false;
        }
    }
}
