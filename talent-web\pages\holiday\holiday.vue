<template>
  <div class="vacation-dashboard">
    <header>
      <p>{{ currentYear }}年度假期概览</p>
    </header>
    
    <div class="dashboard-grid">
      <div v-for="(item, index) in vacationItems" :key="index" class="dashboard-card">
        <div class="card-icon" :class="item.color">
          <!-- <i :class="item.icon"></i> -->
		  <roc-icon-plus type="fas" :name="item.icon" :size="15" :color="item.color" :rotate="90" ></roc-icon-plus>
        </div>
        <div class="card-content">
          <h3>{{ item.title }}</h3>
          <p>{{ item.value }}</p>
        </div>
      </div>
    </div>

    <div class="vacation-progress">
      <h3>年假使用进度</h3>
      <div class="progress-bar">
        <div class="progress" :style="{ width: `${usagePercentage}%` }"></div>
      </div>
      <p>已使用 {{ usagePercentage }}% 的年假</p>
    </div>

    <div class="vacation-records">
      <div class="records-header">
        <h3>年假使用记录</h3>
        <a href="#" class="more-link" @click.prevent="viewMoreRecords">更多</a>
      </div>
      <div class="table-responsive">
        <table>
          <thead>
            <tr>
              <th>开始日期</th>
              <th>结束日期</th>
              <th>天数</th>
              <th>类型</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(record, index) in vacationRecords" :key="index">
              <td>{{ record.startDate }}</td>
              <td>{{ record.endDate }}</td>
              <td>{{ record.days }}</td>
              <td>
                <span class="tag" :class="record.type">{{ record.type }}</span>
              </td>
              <td>
                <span class="status" :class="record.status">{{ record.status }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="quick-actions">
      <h3>快速操作</h3>
      <button @click="applyForVacation" class="btn primary">申请休假</button>
      <button @click="viewYearlyReport" class="btn secondary">查看年度报告</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const currentYear = new Date().getFullYear();

const vacationItems = ref([
  { title: '年假总天数', value: '20 天', icon: 'calendar-alt', color: 'blue' },
  { title: '已用天数', value: '7 天', icon: 'hourglass-half', color: 'orange' },
  { title: '剩余天数', value: '13 天', icon: 'umbrella-beach', color: 'green' },
  { title: '待审批天数', value: '2 天', icon: 'clock', color: 'purple' }
]);

const vacationRecords = ref([
  { startDate: '2023-01-10', endDate: '2023-01-14', days: '5 天', type: '年假', status: '已批准' },
  { startDate: '2023-06-01', endDate: '2023-06-01', days: '1 天', type: '事假', status: '已批准' },
  { startDate: '2023-08-15', endDate: '2023-08-16', days: '2 天', type: '年假', status: '待审批' }
]);

const usagePercentage = computed(() => {
  const totalDays = 20;
  const usedDays = 7;
  return Math.round((usedDays / totalDays) * 100);
});

const applyForVacation = () => {
  // 实现申请休假的逻辑
  console.log('Applying for vacation');
};

const viewYearlyReport = () => {
  // 实现查看年度报告的逻辑
  console.log('Viewing yearly report');
};

const viewMoreRecords = () => {
  // 实现查看更多记录的逻辑
  console.log('Viewing more records');
};
</script>

<style scoped>
.vacation-dashboard {
  font-family: 'Roboto', sans-serif;
  max-width: 1000px;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

header {
  text-align: center;
  margin-bottom: 1.5rem;
}

header h1 {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
}

header p {
  color: #7f8c8d;
  font-size: 1rem;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.dashboard-card {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 1rem;
  display: flex;
  align-items: center;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 1.8rem;
  margin-right: 0.8rem;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.card-icon.blue { background-color: #e3f2fd; color: #2196f3; }
.card-icon.orange { background-color: #fff3e0; color: #ff9800; }
.card-icon.green { background-color: #e8f5e9; color: #4caf50; }
.card-icon.purple { background-color: #f3e5f5; color: #9c27b0; }

.card-content h3 {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 0.3rem;
}

.card-content p {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
}

.vacation-progress, .vacation-records, .quick-actions {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.vacation-progress h3, .vacation-records h3, .quick-actions h3 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 0.8rem;
}

.progress-bar {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress {
  height: 100%;
  background-color: #4caf50;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}

.more-link {
  color: #3498db;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.more-link:hover {
  color: #2980b9;
}

.table-responsive {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

th, td {
  padding-top: 0.8rem;
  padding-bottom: 0.8rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

th {
  font-weight: 600;
  color: #34495e;
}

.tag, .status {
  /* padding: 0.2rem 0.4rem; */
  border-radius: 12px;
  font-size: 0.75rem;
}

.tag.年假 { background-color: #e3f2fd; color: #2196f3; }
.tag.事假 { background-color: #fff3e0; color: #ff9800; }

.status.已批准 { background-color: #e8f5e9; color: #4caf50; }
.status.待审批 { background-color: #fff3e0; color: #ff9800; }

.quick-actions {
  display: flex;

  gap: 0.8rem;
  align-items: center;
   justify-content:center;
}

.btn {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn.primary {
  background-color: #27ae60;
  color: white;
}

.btn.primary:hover {
  background-color: #2ecc71;
}

.btn.secondary {
  background-color: #3498db;
  color: white;
}

.btn.secondary:hover {
  background-color: #2980b9;
}

@media (max-width: 768px) {
  .vacation-dashboard {
    padding: 0.8rem;
  }
  
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .card-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
  }
  
  .card-content h3 {
    font-size: 0.8rem;
  }
  
  .card-content p {
    font-size: 1rem;
  }
  
  table {
    font-size: 0.8rem;
  }
  
  /* th, td {
    padding: 0.6rem;
  } */
  
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}
</style>