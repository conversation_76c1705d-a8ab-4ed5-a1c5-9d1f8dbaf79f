<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CompanyMapper">
    
    <resultMap type="Company" id="CompanyResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="parentName"    column="parent_name"    />
        <result property="name"    column="name"    />
        <result property="logo"    column="logo"    />
        <result property="description"    column="description"    />
        <result property="location"    column="location"    />
        <result property="scale"    column="scale"    />
        <result property="industry"    column="industry"    />
        <result property="nature"    column="nature"    />
        <result property="jobCount"    column="job_count"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCompanyVo">
        select c.id, c.parent_id, p.name as parent_name, c.name, c.logo, c.description, c.location, c.scale, c.industry, c.nature, c.status, c.create_time, c.update_time
        from company c
        left join company p on c.parent_id = p.id
    </sql>

    <select id="selectCompanyList" parameterType="Company" resultMap="CompanyResult">
        select c.id, c.parent_id, p.name as parent_name, c.name, c.logo, c.description, c.location, c.scale, c.industry, c.nature, c.status, c.create_time, c.update_time,
               COUNT(j.id) as job_count
        from company c
        left join company p on c.parent_id = p.id
        left join talent_job j on j.company_id = c.id and j.status = '0'
        <where>
            AND c.status != '1'
            <if test="name != null  and name != ''"> and c.name like concat('%', #{name}, '%')</if>
            <if test="parentId != null"> and c.parent_id = #{parentId}</if>
            <if test="location != null  and location != ''"> and c.location like concat('%', #{location}, '%')</if>
            <if test="scale != null  and scale != ''"> and c.scale = #{scale}</if>
            <if test="industry != null  and industry != ''"> and c.industry = #{industry}</if>
            <if test="nature != null  and nature != ''"> and c.nature = #{nature}</if>
            <if test="status != null  and status != ''"> and c.status = #{status}</if>
        </where>
        group by c.id, c.parent_id, p.name, c.name, c.logo, c.description, c.location, c.scale, c.industry, c.nature, c.status, c.create_time, c.update_time
        order by c.create_time desc
    </select>
    
    <select id="selectCompanyById" parameterType="Long" resultMap="CompanyResult">
        <include refid="selectCompanyVo"/>
        where c.id = #{id}
    </select>
        
    <insert id="insertCompany" parameterType="Company" useGeneratedKeys="true" keyProperty="id">
        insert into company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="name != null">name,</if>
            <if test="logo != null">logo,</if>
            <if test="description != null">description,</if>
            <if test="location != null">location,</if>
            <if test="scale != null">scale,</if>
            <if test="industry != null">industry,</if>
            <if test="nature != null">nature,</if>
            <if test="jobCount != null">job_count,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="name != null">#{name},</if>
            <if test="logo != null">#{logo},</if>
            <if test="description != null">#{description},</if>
            <if test="location != null">#{location},</if>
            <if test="scale != null">#{scale},</if>
            <if test="industry != null">#{industry},</if>
            <if test="nature != null">#{nature},</if>
            <if test="jobCount != null">#{jobCount},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCompany" parameterType="Company">
        update company
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="logo != null">logo = #{logo},</if>
            <if test="description != null">description = #{description},</if>
            <if test="location != null">location = #{location},</if>
            <if test="scale != null">scale = #{scale},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="nature != null">nature = #{nature},</if>
            <if test="jobCount != null">job_count = #{jobCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteCompanyById" parameterType="Long">
        update company set status = '1', update_time = sysdate() where id = #{id}
    </update>

    <update id="deleteCompanyByIds" parameterType="String">
        update company set status = '1', update_time = sysdate() where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    
    <select id="countTotalCompanies" resultType="int">
        select count(*) from company where status = '0'
    </select>
</mapper> 