package com.ruoyi.web.controller.talent;

import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.talent.domain.CompanyAdmin;
import com.ruoyi.talent.service.ICompanyAdminService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 企业管理员关联Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/talent/company-admin")
public class CompanyAdminController extends BaseController
{
    @Autowired
    private ICompanyAdminService companyAdminService;

    @Autowired
    private ISysUserService userService;

    /**
     * 查询企业的管理员列表
     */
    @PreAuthorize("@ss.hasPermi('talent:company:list')")
    @GetMapping("/list/{companyId}")
    public AjaxResult list(@PathVariable("companyId") Long companyId)
    {
        List<CompanyAdmin> list = companyAdminService.selectCompanyAdminList(companyId);
        return success(list);
    }

    /**
     * 搜索用户列表
     */
    @PreAuthorize("@ss.hasPermi('talent:company:list')")
    @GetMapping("/search-users")
    public TableDataInfo searchUsers(SysUser user)
    {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    /**
     * 绑定企业管理员
     */
    @PreAuthorize("@ss.hasPermi('talent:company:bind')")
    @Log(title = "企业管理员", businessType = BusinessType.INSERT)
    @PostMapping("/bind")
    public AjaxResult bind(@RequestBody Map<String, Object> params)
    {
        Long companyId = Long.valueOf(params.get("companyId").toString());
        List<Integer> userIdList = (List<Integer>) params.get("userIds");
        Long[] userIds = userIdList.stream().map(Long::valueOf).toArray(Long[]::new);
        return toAjax(companyAdminService.insertCompanyAdmins(companyId, userIds));
    }

    /**
     * 解绑企业管理员
     */
    @PreAuthorize("@ss.hasPermi('talent:company:bind')")
    @Log(title = "企业管理员", businessType = BusinessType.DELETE)
    @DeleteMapping("/unbind")
    public AjaxResult unbind(@RequestParam Long companyId, @RequestParam Long userId)
    {
        return toAjax(companyAdminService.deleteCompanyAdmin(companyId, userId));
    }

    /**
     * 检查用户是否是企业管理员
     */
    @GetMapping("/check")
    public AjaxResult checkIsAdmin(@RequestParam Long companyId, @RequestParam Long userId)
    {
        return success(companyAdminService.checkIsCompanyAdmin(companyId, userId));
    }
} 