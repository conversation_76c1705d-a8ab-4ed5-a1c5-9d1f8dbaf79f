<template>
  <div class="salary-query">
    <div class="query-header">
      <h3>工资查询</h3>
      <div class="date-picker">
        <select v-model="selectedDate">
          <option value="2018.06">2018.06</option>
          <!-- 可以添加更多日期选项 -->
        </select>
      </div>
    </div>
    
    <div class="summary-card">
      <h3>基本情况</h3>
      <div class="summary">
        <div class="summary-item">
          <div class="icon blue">¥</div>
          <div class="content">
            <div class="amount">6735.34</div>
            <div class="label">应发</div>
          </div>
        </div>
        <div class="summary-item">
          <div class="icon green">¥</div>
          <div class="content">
            <div class="amount">2500.25</div>
            <div class="label">实发</div>
          </div>
        </div>
        <div class="summary-item">
          <div class="icon orange">¥</div>
          <div class="content">
            <div class="amount">-2500.25</div>
            <div class="label">扣款</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="details-card">
      <h3>详细情况</h3>
      <div class="salary-details">
        <div v-for="(item, index) in salaryItems" :key="index" class="salary-item">
          <div class="item-label" :style="{ borderLeftColor: item.color }">{{ item.label }}</div>
          <div class="item-amount">{{ item.amount }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const selectedDate = ref('2018.06');

const salaryItems = ref([
  { label: '岗位工资', amount: '2670.00', color: '#FF4081' },
  { label: '薪级工资', amount: '493.00', color: '#9C27B0' },
  { label: '通讯费', amount: '116.00', color: '#8BC34A' },
  { label: '工龄津贴', amount: '10.00', color: '#00BCD4' },
  { label: '绩效工资', amount: '2089.00', color: '#FFC107' },
  { label: '独生子女费', amount: '2089.00', color: '#009688' },
  { label: '工作补贴', amount: '2089.00', color: '#03A9F4' },
  { label: '补发工资', amount: '2089.00', color: '#FF5722' },
]);

const goBack = () => {
  // 实现返回逻辑
  console.log('Going back');
};
</script>

<style scoped>
.salary-query {
  font-family: Arial, sans-serif;
  background-color: #f0f0f0;
  padding: 20px;
}

.query-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.query-header h3 {
  margin: 0;
  margin-right: 10px;
  font-size: 18px;
  color: #333;
}

.date-picker {
  flex: 1;
}

.date-picker select {
  width: 100%;
  max-width: 200px;
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: white;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="none" stroke="%23333" stroke-width=".75" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 10px;
}

.summary-card, .details-card {
  background-color: white;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-card h3, .details-card h3 {
  margin-bottom: 10px;
  font-size: 18px;
  color: #333;
}

.summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.summary-item {
  background-color: white;
  padding: 5px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  flex: 1 1 calc(30% - 10px);
  margin: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: bold;
  margin-right: 10px;
}

.blue { background-color: #03A9F4; }
.green { background-color: #4CAF50; }
.orange { background-color: #FF9800; }

.amount {
  font-weight: bold;
  font-size: 18px;
}

.label {
  font-size: 12px;
  color: #666;
}

.salary-details {
  background-color: white;
  border-radius: 5px;
  padding: 10px;
}

.salary-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.salary-item:last-child {
  border-bottom: none;
}

.item-label {
  border-left: 3px solid;
  padding-left: 10px;
}

.item-amount {
  color: #03A9F4;
  font-weight: bold;
}

@media (max-width: 300px) {
  .summary-item {
    flex: 1 1 calc(50% - 10px);
  }
}

@media (max-width: 200px) {
  .summary-item {
    flex: 1 1 100%;
  }
}
</style>