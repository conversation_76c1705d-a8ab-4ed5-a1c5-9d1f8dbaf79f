<template>
  <view class="my-center">
    <!-- 顶部蓝色背景和头部信息 -->
    <view class="top-bg">
      <view class="header-bar">
     
        <view class="right-btns">
    
          <view class="setting-btn" @click="toSetting">
            <text class="yzb yzb-shezhi"></text>
          </view>
        </view>
      </view>
      <view class="avatar-block">
        <view class="avatar-circle">
          <text class="avatar-text">{{ getAvatarText }}</text>
        </view>
        <view class="cert-badge">
          <text class="cert-icon">✔</text>
          <text class="cert-text">已认证</text>
        </view>
      </view>
    </view>

    <!-- 宫格功能区 -->
    <view class="grid-box">
      <view class="grid-item" v-for="item in gridList" :key="item.text" @click="item.action && item.action()">
        <view class="grid-icon" :style="{background: item.bg}">
          <text :class="item.icon"></text>
        </view>
        <view class="grid-label">{{ item.text }}</view>
      </view>
    </view>


    <!-- 简历列表 -->
   <!-- <view class="resume-list">
      <view 
        v-for="resume in resumes" 
        :key="resume.id" 
        class="resume-card"
        :class="{ 'is-default': resume.isDefault }"
      >
        <view class="resume-header">
          <view class="resume-title">
            <text class="resume-name">{{ resume.resumeTitle }}</text>
            <text v-if="resume.isDefault" class="default-tag">默认投递</text>
          </view>
          <text class="update-time">更新于 {{ resume.updateTime }}</text>
        </view>

        <view class="resume-actions">
          <button class="action-btn create" @click="showUploadPopup()">
            <text class="yzb yzb-jianli"></text>
            新建
          </button>
          <button class="action-btn edit" @click="navigateToEdit(resume.id)">
            <text class="yzb yzb-bianji"></text>
            编辑
          </button>
          <button class="action-btn delete" @click="handleDelete(resume.id)">
            <text class="yzb yzb-shanchu"></text>
            删除
          </button>
        </view>
      </view>
    </view> -->

    <!-- 技术支持 -->
    <view class="support">技术支持：中国水务</view>

    <!-- 创建方式选择弹窗 -->
    <uni-popup ref="uploadPopup" type="bottom">
      <view class="upload-options">
        <view class="popup-header">
          <text class="popup-title">选择创建方式</text>
          <text class="popup-close" @click="closeUploadPopup">×</text>
        </view>
        
        <view class="option-list">
          <view class="option-item" @click="navigateToCreate">
            <text class="yzb yzb-bianji"></text>
            <text class="option-label">在线创建</text>
            <text class="option-desc">从头开始创建一份全新的简历</text>
          </view>
          <view class="option-item" @click="uploadFile">
            <text class="yzb yzb-shangchuan"></text>
            <text class="option-label">上传文件</text>
            <text class="option-desc">上传本地已有的简历文件</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  getMyResumes,
  deleteResume, 
  uploadResumeFile
} from '@/utils/talent-market'

const userInfo = ref(uni.getStorageSync('userinfo') || {})

// 头像首字母
const getAvatarText = userInfo.value.nickName ? userInfo.value.nickName[0] : '卓'

const resumes = ref([])
const uploadPopup = ref(null)

// "我的简历"点击逻辑
const handleMyResumeClick = () => {
  if (resumes.value.length === 0) {
    // 没有简历，跳转到创建
    uni.navigateTo({ url: '/pages/talent-market/resume-edit?mode=create' })
  } else {
    // 有简历，跳转到第一个简历的编辑
    uni.navigateTo({ url: `/pages/talent-market/resume-edit?id=${resumes.value[0].id}&mode=edit` })
  }
}

const gridList = [
  {
    text: '我的简历',
    icon: 'yzb yzb-jianli',
    bg: '#e6f7ff',
    action: handleMyResumeClick
  },
 
  {
    text: '投递情况',
    icon: 'yzb yzb-toudijianli',
    bg: '#f6ffed',
    action: () => uni.navigateTo({ url: '/pages/talent-market/delivery-record' })
  },
  {
    text: '职位收藏',
    icon: 'yzb yzb-shoucang',
    bg: '#fffbe6',
    action: () => uni.navigateTo({ url: '/pages/talent-market/resume-fav' })
  }
]

// 获取简历列表
const fetchResumeList = async () => {
  try {
    const token = uni.getStorageSync('token')
    const result = await getMyResumes(token)
    if (result && result.code === 200) {
      resumes.value = result.rows || []
    }
  } catch (error) {
    console.error('获取简历列表失败:', error)
    uni.showToast({
      title: '获取简历列表失败',
      icon: 'none'
    })
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchResumeList()
})

// 显示创建方式选择弹窗
const showUploadPopup = () => {
  uploadPopup.value.open()
}

// 关闭创建方式选择弹窗
const closeUploadPopup = () => {
  uploadPopup.value.close()
}

// 跳转到创建页面
const navigateToCreate = () => {
  closeUploadPopup()
  uni.navigateTo({
    url: '/pages/talent-market/resume-edit?mode=create',
    success: () => {
      uni.showToast({
        title: '开始创建简历',
        icon: 'none',
        duration: 2000
      })
    },
    fail: (error) => {
      console.error('跳转失败:', error)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'error'
      })
    }
  })
}

// 跳转到编辑页面
const navigateToEdit = (id) => {
  uni.navigateTo({
    url: `/pages/talent-market/resume-edit?id=${id}&mode=edit`,
    success: () => {
      uni.showToast({
        title: '开始编辑简历',
        icon: 'none',
        duration: 2000
      })
    },
    fail: (error) => {
      console.error('跳转失败:', error)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'error'
      })
    }
  })
}

// 处理删除简历
const handleDelete = (id) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这份简历吗？删除后无法恢复',
    success: async (res) => {
      if (res.confirm) {
        try {
          const token = uni.getStorageSync('token')
          await deleteResume(token, id)
          
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          
          // 刷新简历列表
          fetchResumeList()
        } catch (error) {
          console.error('删除简历失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'error'
          })
        }
      }
    }
  })
}

// 上传文件
const uploadFile = async () => {
  try {
    const res = await uni.chooseFile({
      count: 1,
      type: 'file',
      extension: ['.pdf', '.doc', '.docx'],
    })
    
    if (res.tempFiles && res.tempFiles.length > 0) {
      const file = res.tempFiles[0]
      
      // 检查文件大小（限制为10MB）
      if (file.size > 10 * 1024 * 1024) {
        uni.showToast({
          title: '文件大小不能超过10MB',
          icon: 'none'
        })
        return
      }
      
      // 显示上传中提示
      uni.showLoading({
        title: '上传中...'
      })
      
      const token = uni.getStorageSync('token')
      const result = await uploadResumeFile(token, file)
      
      uni.hideLoading()
      
      if (result && result.code === 200) {
        uni.showToast({
          title: '上传成功',
          icon: 'success'
        })
        // 上传成功后跳转到编辑页面
        navigateToEdit(result.data.resumeId)
        // 刷新简历列表
        fetchResumeList()
      } else {
        uni.showToast({
          title: result?.message || '上传失败',
          icon: 'none'
        })
      }
    }
    
    closeUploadPopup()
  } catch (error) {
    uni.hideLoading()
    console.error('上传文件失败:', error)
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  }
}

// 跳转到设置页面
const toSetting = () => {
  uni.navigateTo({
    url: '/pages/talent-market/setting'
  })
}

// 跳转到登录页面
const toLogin = () => {
  uni.navigateTo({
    url: '/pages/talent-market/login'
  })
}
</script>

<style lang="scss">
.my-center {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 120rpx;
}
.top-bg {
  width: 100vw;
  height: 320rpx;
  background: #2186f7;
  border-bottom-left-radius: 32rpx;
  border-bottom-right-radius: 32rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.header-bar {
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 80rpx;
}
.title {
  color: #fff;
  font-size: 36rpx;
  font-weight: 600;
  margin-top: 40rpx;
}
.right-btns {
  position: absolute;
  right: 32rpx;
  top: 40rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.switch-btn {
  color: #fff;
  font-size: 24rpx;
  margin-right: 16rpx;
}
.setting-btn .yzb {
  color: #fff;
  font-size: 40rpx;
}
.avatar-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10rpx;
}
.avatar-circle {
  width: 120rpx;
  height: 120rpx;
  background: #b3cfff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #fff;
  margin-bottom: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(33,134,247,0.12);
}
.avatar-text {
  font-weight: 600;
}
.cert-badge {
  display: flex;
  align-items: center;
  background: #fff7e6;
  border-radius: 20rpx;
  padding: 0 16rpx;
  height: 36rpx;
  font-size: 22rpx;
  color: #faad14;
  margin-top: 2rpx;
}
.cert-icon {
  margin-right: 6rpx;
}
.cert-text {
  font-weight: 500;
}
.grid-box {
  width: 92vw;
  margin: -40rpx auto 0 auto;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.04);
  display: flex;
  flex-wrap: wrap;
  padding: 32rpx 0 16rpx 0;
  justify-content: space-between;
  z-index: 	999;
}
.grid-item {
  width: 33.33%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
  cursor: pointer;
}
.grid-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  font-size: 36rpx;
}
.grid-label {
  font-size: 26rpx;
  color: #222;
  font-weight: 500;
}
.support {
  width: 100vw;
  text-align: center;
  color: #bbb;
  font-size: 22rpx;
  position: absolute;
  bottom: 32rpx;
  left: 0;
}
.resume-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 120px;
}

/* 头部样式 */
.header {
  background-color: #12ae85;
  width: 100%;
  height: 30vw;
  padding: 0 4%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .userinfo {
    display: flex;
    align-items: center;
    
    .face {
      image {
        width: 150upx;
        height: 150upx;
        border-radius: 75upx;
        border: 4upx solid rgba(255, 255, 255, 0.3);
      }
    }
    
    .info {
      margin-left: 30upx;
      .username {
        color: #fff;
        font-size: 40upx;
        font-weight: 500;
      }
      .integral {
        color: rgba(255, 255, 255, 0.8);
        font-size: 28upx;
        margin-top: 10upx;
      }
    }
  }
  
  .setting {
    .yzb {
      color: #fff;
      font-size: 50upx;
    }
  }
}

/* 简历列表样式 */
.resume-list {
  padding: 20upx 4%;
  display: flex;
  flex-direction: column;
  gap: 20upx;
}

.resume-card {
  background: #fff;
  border-radius: 16upx;
  padding: 30upx;
  box-shadow: 0 4upx 20upx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.resume-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8upx;
  background: #12ae85;
  border-radius: 4upx;
}

.resume-card.is-default {
  background: linear-gradient(135deg, #e6f7f0 0%, #fff 100%);
  border: 1px solid rgba(18, 174, 133, 0.2);
}

.resume-header {
  margin-bottom: 30upx;
}

.resume-title {
  display: flex;
  align-items: center;
  gap: 20upx;
  margin-bottom: 10upx;
}

.resume-name {
  font-size: 36upx;
  font-weight: 600;
  color: #1a1a1a;
}

.default-tag {
  background: #12ae85;
  color: #fff;
  padding: 4upx 20upx;
  border-radius: 24upx;
  font-size: 24upx;
  font-weight: 500;
}

.update-time {
  font-size: 26upx;
  color: #999;
  display: flex;
  align-items: center;
}

.update-time::before {
  content: '';
  display: inline-block;
  width: 8upx;
  height: 8upx;
  background: #999;
  border-radius: 50%;
  margin-right: 16upx;
}

/* 操作按钮 */
.resume-actions {
  display: flex;
  gap: 20upx;
  margin-top: 30upx;
}

.action-btn {
  flex: 1;
  height: 80upx;
  border: none;
  border-radius: 16upx;
  font-size: 28upx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10upx;
  transition: all 0.3s ease;
  font-weight: 500;
}

.action-btn.create {
  background: #12ae85;
  color: #fff;
  box-shadow: 0 4upx 16upx rgba(18, 174, 133, 0.2);
}

.action-btn.edit {
  background: #409EFF;
  color: #fff;
  box-shadow: 0 4upx 16upx rgba(64, 158, 255, 0.2);
}

.action-btn.delete {
  background: #f56c6c;
  color: #fff;
  box-shadow: 0 4upx 16upx rgba(245, 108, 108, 0.2);
}

.action-btn:active {
  transform: scale(0.98);
  box-shadow: none;
}

/* 弹窗样式 */
.upload-options {
  background: #fff;
  border-radius: 32upx 32upx 0 0;
  padding: 40upx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40upx;
  padding-bottom: 20upx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 36upx;
  font-weight: 600;
  color: #1a1a1a;
}

.popup-close {
  font-size: 40upx;
  color: #999;
  padding: 10upx;
}

.option-list {
  display: flex;
  flex-direction: column;
  gap: 30upx;
}

.option-item {
  padding: 30upx;
  border-radius: 24upx;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20upx;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.option-item:active {
  background: #e6f7f0;
  border-color: rgba(18, 174, 133, 0.2);
  transform: scale(0.98);
}

.option-label {
  font-size: 32upx;
  color: #1a1a1a;
  font-weight: 600;
}

.option-desc {
  font-size: 26upx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 图标样式 */
.yzb {
  font-size: 36upx;
}
</style>