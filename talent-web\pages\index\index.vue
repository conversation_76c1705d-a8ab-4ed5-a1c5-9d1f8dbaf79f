<script setup>
import { ref, onMounted,getCurrentInstance } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import {getUrlCode,getWxAuthorize,getUserInfo} from "@/utils/wx.js";
// import { getWxSignature, wxapis } from "@/utils/initWechatJSSDK.js";
const {
	proxy,
	appContext: {
		config: {
			globalProperties: global
		}
	}
} = getCurrentInstance();
const activeTab = ref('employee-services');

function goto(url) {
  if (url !== "") {
    console.log(url);
    uni.navigateTo({
      url: url
    });
  }
}

onLoad( async (param) => {
  // getWxSignature(global, [wxapis.scanQRCode]);
  console.log("11获取信息中..")
  await proxy.$onLaunched;
  
  const token = uni.getStorageSync('token'); 
  console.log("获取信息中..")
  getUserInfo(token).then(function(res){
  	uni.setStorageSync('userinfo',res.data)
  }).catch(function(err){
  	console.log(err)
  });
});
</script>

<template>
  <view class="dashboard">
    <view class="header">
      <text class="title">驾驶舱</text>
    </view>
    <view class="content">
      <view class="data-grid">
        <view class="data-item" @click="goto('/pages/finance/finance')">
          <image class="value-image" src="/static/<EMAIL>" mode="aspectFit" />
          <text class="label">财务</text>
        </view>
        <view class="data-item" @click="goto('/pages/humanresources/humanresources')">
          <image class="value-image" src="/static/<EMAIL>" mode="aspectFit" />
          <text class="label">人资</text>
        </view>
        <view class="data-item" @click="goto('/pages/security/security')">
          <image class="value-image" src="/static/<EMAIL>" mode="aspectFit" />
          <text class="label">安全</text>
        </view>
        <view class="data-item" @click="goto('/pages/investment/investment')">
          <image class="value-image" src="/static/<EMAIL>" mode="aspectFit" />
          <text class="label">投资</text>
        </view>
		<view class="data-item" @click="goto('/pages/tongji/tongji')">
		  <image class="value-image" src="/static/tongji.png" mode="aspectFit" />
		  <text class="label">统计</text>
		</view>
      </view>
    </view>
  </view>
  
<!--  <view class="dashboard">
    <view class="tabs">
      <view class="tab-header">
        <text class="tab-title">员工服务</text>
        <view class="active-tab"></view>
      </view>
      <view class="bottom"></view>
    </view>
    <view class="content">
      <view class="data-grid">
        <view class="data-item" @click="goto('/pages/salary/salary')">
          <image class="value-image" src="/static/<EMAIL>" mode="aspectFit" />
          <text class="label">工资情况</text>
        </view>
        <view class="data-item" @click="goto('/pages/holiday/holiday')">
          <image class="value-image" src="/static/<EMAIL>" mode="aspectFit" />
          <text class="label">年假查询</text>
        </view>
        <view class="data-item" @click="goto('/pages/training/training')">
          <image class="value-image" src="/static/<EMAIL>" mode="aspectFit" />
          <text class="label">培训学时</text>
        </view>
        <view class="data-item" @click="goto('/pages/permissions/permissions')">
          <image class="value-image" src="/static/<EMAIL>" mode="aspectFit" />
          <text class="label">个人权限</text>
        </view>
      </view>
    </view>
  </view> -->
</template>

<style>
.dashboard {
  font-family: Arial, sans-serif;
  color: #333;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-image: url("../static/beijing.png");
  padding: 0 15px;
  height: 44px;
}

.title {
  margin: 0;
  font-size: 16px;
  color: #333333;
}

.content {
  background-color: #fff;
  border-radius: 8px;
}

.data-grid {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-top: 10px;
}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 60px;
  padding: 10px;
}

.value-image {
  width: 40px;
  height: 40px;
  margin-bottom: 5px;
}

.label {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 500;
  font-size: 14px;
  color: #666666;
  line-height: 18px;
  text-align: center;
}

.tabs {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  flex-direction: column;
  padding-left: 10px;
  padding-top: 5px;
}

.tab-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 75px;
}

.tab-title {
  margin: 0;
  padding: 10px 2px;
  font-size: 16px;
  font-weight: 600;
  color: #427CE8;
}

.bottom {
  width: 90%;
  margin-left: 5px;
  margin-right: 5px;
  border: 1px solid #EEEEEE;
  background-color: #EEEEEE;
}

.active-tab {
  width: 30px;
  height: 2px;
  background: #437CE9;
  border-radius: 202px;
}
</style>