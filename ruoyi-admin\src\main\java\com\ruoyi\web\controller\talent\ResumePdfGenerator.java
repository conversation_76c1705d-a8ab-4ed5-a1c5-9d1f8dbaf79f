package com.ruoyi.web.controller.talent;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.ruoyi.system.domain.Resume;
import com.ruoyi.system.domain.ResumeEducation;
import com.ruoyi.system.domain.ResumeWork;
import com.ruoyi.system.domain.ResumeProject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 简历PDF生成器
 * 基于HTML模板生成PDF格式简历
 */
public class ResumePdfGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(ResumePdfGenerator.class);
    
    /**
     * 基于HTML模板生成PDF简历
     */
    public static ByteArrayOutputStream generatePdfFromHtmlTemplate(Resume resume, 
                                                                   List<ResumeEducation> educationList,
                                                                   List<ResumeWork> workList, 
                                                                   List<ResumeProject> projectList) throws Exception {
        
        logger.info("开始基于HTML模板生成PDF简历，简历ID: {}", resume.getId());
        
        // 1. 加载HTML模板
        String htmlTemplate = loadHtmlTemplate();
        
        // 2. 替换占位符
        String processedHtml = replaceAllPlaceholders(htmlTemplate, resume, educationList, workList, projectList);
        
        // 3. 生成PDF
        ByteArrayOutputStream pdfStream = convertHtmlToPdf(processedHtml);
        
        logger.info("PDF简历生成成功，文件大小: {} bytes", pdfStream.size());
        return pdfStream;
    }
    
    /**
     * 加载HTML模板文件
     */
    private static String loadHtmlTemplate() throws IOException {
        String templatePath = "/templates/resume_template.html";

        try (InputStream inputStream = ResumePdfGenerator.class.getResourceAsStream(templatePath)) {
            if (inputStream == null) {
                throw new FileNotFoundException("HTML模板文件不存在: " + templatePath);
            }

            try (InputStreamReader isr = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                 BufferedReader reader = new BufferedReader(isr)) {

                StringBuilder content = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }

                logger.debug("HTML模板加载成功，内容长度: {} 字符", content.length());
                return content.toString();
            }
        }
    }
    
    /**
     * 替换所有占位符
     */
    private static String replaceAllPlaceholders(String htmlTemplate, Resume resume,
                                                List<ResumeEducation> educationList,
                                                List<ResumeWork> workList,
                                                List<ResumeProject> projectList) {
        
        String html = htmlTemplate;
        
        // 替换基本信息
        html = replaceBasicInfo(html, resume);
        
        // 替换学历学位信息
        html = replaceEducationDegree(html, educationList);
        
        // 替换动态内容
        html = replaceEducationHistory(html, educationList);
        html = replaceWorkHistory(html, workList);
        html = replaceProjectHistory(html, projectList);
        
        // 替换头像
        html = replaceAvatar(html, resume);
        
        return html;
    }
    
    /**
     * 替换基本信息占位符
     */
    private static String replaceBasicInfo(String html, Resume resume) {


        html = html.replace("{{NAME}}", resume.getName() != null ? resume.getName() : "");
        html = html.replace("{{GENDER}}", resume.getGender() != null ? resume.getGender() : "");
        html = html.replace("{{BIRTHDAY}}", formatDate(resume.getBirthday()));
        html = html.replace("{{AGE}}", resume.getAge() != null ? resume.getAge().toString() : "");
        html = html.replace("{{NATION}}", resume.getNation() != null ? resume.getNation() : "");
        html = html.replace("{{NATIVE_PLACE}}", resume.getNativePlace() != null ? resume.getNativePlace() : "");
        html = html.replace("{{POLITICAL_STATUS}}", resume.getPoliticalStatus() != null ? resume.getPoliticalStatus() : "");
        html = html.replace("{{PHONE}}", resume.getPhone() != null ? resume.getPhone() : "");
        html = html.replace("{{EMAIL}}", resume.getEmail() != null ? resume.getEmail() : "");
        html = html.replace("{{TECHNICAL_POSITION}}", resume.getTechnicalPosition() != null ? resume.getTechnicalPosition() : "");
        html = html.replace("{{CURRENT_POSITION}}", resume.getCurrentPosition() != null ? resume.getCurrentPosition() : "");

        return html;
    }
    
    /**
     * 替换学历学位信息
     */
    private static String replaceEducationDegree(String html, List<ResumeEducation> educationList) {
        if (educationList != null && !educationList.isEmpty()) {
            // 取最高学历
            ResumeEducation highest = educationList.get(0);
            html = html.replace("{{HIGHEST_EDUCATION}}", highest.getDegree() != null ? highest.getDegree() : "");
            html = html.replace("{{HIGHEST_DEGREE}}", highest.getDegree() != null ? highest.getDegree() : "");
            html = html.replace("{{HIGHEST_SCHOOL}}", highest.getSchool() != null ? highest.getSchool() : "");
            html = html.replace("{{HIGHEST_MAJOR}}", highest.getMajor() != null ? highest.getMajor() : "");
        } else {
            html = html.replace("{{HIGHEST_EDUCATION}}", "");
            html = html.replace("{{HIGHEST_DEGREE}}", "");
            html = html.replace("{{HIGHEST_SCHOOL}}", "");
            html = html.replace("{{HIGHEST_MAJOR}}", "");
        }
        
        return html;
    }
    
    /**
     * 替换教育经历
     */
    private static String replaceEducationHistory(String html, List<ResumeEducation> educationList) {
        // 替换固定的教育经历占位符（最多3条）
        for (int i = 1; i <= 3; i++) {
            if (educationList != null && educationList.size() >= i) {
                ResumeEducation edu = educationList.get(i - 1);
                html = html.replace("{{EDU_DATE_" + i + "}}", formatDateRange(edu.getStartDate(), edu.getEndDate()));
                html = html.replace("{{EDU_SCHOOL_" + i + "}}", edu.getSchool() != null ? edu.getSchool() : "");

                // 处理专业和学位，避免出现多余的 | 符号
                String major = edu.getMajor() != null ? edu.getMajor() : "";
                String degree = edu.getDegree() != null ? edu.getDegree() : "";

                // 智能组合专业和学位，避免多余的分隔符
                String majorDegree = "";
                if (!major.isEmpty() && !degree.isEmpty()) {
                    majorDegree = major + " | " + degree;
                } else if (!major.isEmpty()) {
                    majorDegree = major;
                } else if (!degree.isEmpty()) {
                    majorDegree = degree;
                }

                // 直接替换整个专业|学位组合
                html = html.replace("{{EDU_MAJOR_" + i + "}} | {{EDU_DEGREE_" + i + "}}", majorDegree);
                // 也处理单独的占位符（以防模板格式不同）
                html = html.replace("{{EDU_MAJOR_" + i + "}}", major);
                html = html.replace("{{EDU_DEGREE_" + i + "}}", degree);
            } else {
                // 如果没有数据，清空整个条目
                html = html.replace("{{EDU_DATE_" + i + "}}", "");
                html = html.replace("{{EDU_SCHOOL_" + i + "}}", "");
                html = html.replace("{{EDU_MAJOR_" + i + "}} | {{EDU_DEGREE_" + i + "}}", "");
                html = html.replace("{{EDU_MAJOR_" + i + "}}", "");
                html = html.replace("{{EDU_DEGREE_" + i + "}}", "");
            }
        }

        return html;
    }
    
    /**
     * 替换工作经历
     */
    private static String replaceWorkHistory(String html, List<ResumeWork> workList) {
        // 替换固定的工作经历占位符（最多3条）
        for (int i = 1; i <= 3; i++) {
            if (workList != null && workList.size() >= i) {
                ResumeWork work = workList.get(i - 1);
                html = html.replace("{{WORK_DATE_" + i + "}}", formatDateRange(work.getStartDate(), work.getEndDate()));
                html = html.replace("{{WORK_COMPANY_" + i + "}}", work.getCompany() != null ? work.getCompany() : "");
                html = html.replace("{{WORK_DESC_" + i + "}}", work.getDescription() != null ? work.getDescription() : "");

                // 处理职位和部门，避免出现多余的 | 符号
                String position = work.getPosition() != null ? work.getPosition() : "";
                String department = work.getDepartment() != null ? work.getDepartment() : "";

                // 智能组合职位和部门，避免多余的分隔符
                String positionDept = "";
                if (!position.isEmpty() && !department.isEmpty()) {
                    positionDept = position + " | " + department;
                } else if (!position.isEmpty()) {
                    positionDept = position;
                } else if (!department.isEmpty()) {
                    positionDept = department;
                }

                // 直接替换整个职位|部门组合
                html = html.replace("{{WORK_POSITION_" + i + "}} | {{WORK_DEPT_" + i + "}}", positionDept);
                // 也处理单独的占位符（以防模板格式不同）
                html = html.replace("{{WORK_POSITION_" + i + "}}", position);
                html = html.replace("{{WORK_DEPT_" + i + "}}", department);
            } else {
                // 如果没有数据，清空整个条目
                html = html.replace("{{WORK_DATE_" + i + "}}", "");
                html = html.replace("{{WORK_COMPANY_" + i + "}}", "");
                html = html.replace("{{WORK_POSITION_" + i + "}} | {{WORK_DEPT_" + i + "}}", "");
                html = html.replace("{{WORK_POSITION_" + i + "}}", "");
                html = html.replace("{{WORK_DEPT_" + i + "}}", "");
                html = html.replace("{{WORK_DESC_" + i + "}}", "");
            }
        }

        return html;
    }
    
    /**
     * 替换项目经历
     */
    private static String replaceProjectHistory(String html, List<ResumeProject> projectList) {
        // 替换固定的项目经历占位符（最多3条）
        for (int i = 1; i <= 3; i++) {
            if (projectList != null && projectList.size() >= i) {
                ResumeProject project = projectList.get(i - 1);
                html = html.replace("{{PROJECT_DATE_" + i + "}}", formatDateRange(project.getStartDate(), project.getEndDate()));
                html = html.replace("{{PROJECT_NAME_" + i + "}}", project.getName() != null ? project.getName() : "");
                html = html.replace("{{PROJECT_ROLE_" + i + "}}", project.getRole() != null ? project.getRole() : "");
                html = html.replace("{{PROJECT_DESC_" + i + "}}", project.getDescription() != null ? project.getDescription() : "");
                html = html.replace("{{PROJECT_ACHIEVEMENT_" + i + "}}", project.getAchievement() != null ? project.getAchievement() : "");
            } else {
                // 如果没有数据，清空占位符
                html = html.replace("{{PROJECT_DATE_" + i + "}}", "");
                html = html.replace("{{PROJECT_NAME_" + i + "}}", "");
                html = html.replace("{{PROJECT_ROLE_" + i + "}}", "");
                html = html.replace("{{PROJECT_DESC_" + i + "}}", "");
                html = html.replace("{{PROJECT_ACHIEVEMENT_" + i + "}}", "");
            }
        }

        return html;
    }
    
    /**
     * 替换头像
     */
    private static String replaceAvatar(String html, Resume resume) {
        String avatarHtml = "";
        if (resume.getAvatar() != null && !resume.getAvatar().trim().isEmpty()) {
            avatarHtml = "<img src=\"" + resume.getAvatar() + "\" alt=\"头像\" style=\"max-width: 120px; max-height: 150px;\" />";
        } else {
            avatarHtml = "<div style=\"color: #999; font-size: 14px;\">暂无照片</div>";
        }

        return html.replace("{{AVATAR}}", avatarHtml);
    }
    
    /**
     * 将HTML转换为PDF
     */
    private static ByteArrayOutputStream convertHtmlToPdf(String html) throws Exception {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.withHtmlContent(html, null);
            builder.toStream(outputStream);

            // 尝试添加中文字体支持
            try {
                // 尝试使用系统字体文件
                java.io.File[] fontPaths = {
                    new java.io.File("C:/Windows/Fonts/msyh.ttc"),     // 微软雅黑
                    new java.io.File("C:/Windows/Fonts/simhei.ttf"),   // 黑体
                    new java.io.File("C:/Windows/Fonts/simsun.ttc"),   // 宋体
                    new java.io.File("C:/Windows/Fonts/simkai.ttf")    // 楷体
                };

                boolean fontLoaded = false;
                for (java.io.File fontFile : fontPaths) {
                    if (fontFile.exists()) {
                        try {
                            builder.useFont(fontFile, "ChineseFont", 400,
                                com.openhtmltopdf.pdfboxout.PdfRendererBuilder.FontStyle.NORMAL, true);
                            logger.info("成功加载中文字体: {}", fontFile.getName());
                            fontLoaded = true;
                            break;
                        } catch (Exception e) {
                            logger.warn("加载字体失败 {}: {}", fontFile.getName(), e.getMessage());
                        }
                    }
                }

                if (!fontLoaded) {
                    logger.warn("未找到可用的中文字体，中文字符可能显示为方框");
                }

            } catch (Exception e) {
                logger.warn("配置中文字体时出错: {}", e.getMessage());
            }

            builder.run();

            logger.debug("HTML转PDF成功，PDF大小: {} bytes", outputStream.size());

        } catch (Exception e) {
            logger.error("HTML转PDF失败", e);
            throw new Exception("PDF生成失败: " + e.getMessage(), e);
        }

        return outputStream;
    }
    
    /**
     * 格式化日期
     */
    private static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
        return sdf.format(date);
    }
    
    /**
     * 格式化日期范围
     */
    private static String formatDateRange(Date startDate, Date endDate) {
        String start = formatDate(startDate);
        String end = endDate != null ? formatDate(endDate) : "至今";
        return start + " - " + end;
    }
}
