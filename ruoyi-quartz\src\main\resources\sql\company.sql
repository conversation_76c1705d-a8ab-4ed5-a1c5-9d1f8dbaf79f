-- 创建公司表
create table company (
    id              bigint(20)      not null auto_increment    comment '公司ID',
    name            varchar(100)    not null                   comment '公司名称',
    logo            varchar(255)    default ''                 comment '公司Logo',
    description     varchar(500)    default ''                 comment '公司描述',
    location        varchar(100)    default ''                 comment '公司地址',
    scale           varchar(50)     default ''                 comment '公司规模',
    industry        varchar(50)     default ''                 comment '公司行业',
    nature          varchar(50)     default ''                 comment '公司性质',
    job_count       int(11)         default 0                  comment '职位数量',
    status          char(1)         default '0'                comment '状态（0正常 1停用）',
    create_time     datetime                                   comment '创建时间',
    update_time     datetime                                   comment '更新时间',
    primary key (id)
) engine=innodb auto_increment=100 comment = '公司表'; 