<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ResumeAttachmentMapper">
    
    <resultMap type="ResumeAttachment" id="AttachmentResult">
        <id     property="id"          column="id"          />
        <result property="resumeId"    column="resume_id"   />
        <result property="fileName"    column="file_name"   />
        <result property="fileUrl"     column="file_url"    />
        <result property="fileSize"    column="file_size"   />
        <result property="fileType"    column="file_type"   />
        <result property="createdAt"   column="created_at"  />
    </resultMap>

    <select id="selectByResumeId" parameterType="String" resultMap="AttachmentResult">
        select * from resume_attachment 
        where resume_id = #{resumeId}
        order by created_at desc
    </select>

    <insert id="insert" parameterType="ResumeAttachment">
        insert into resume_attachment (
            id, resume_id, file_name, file_url, file_size,
            file_type, created_at
        ) values (
            #{id}, #{resumeId}, #{fileName}, #{fileUrl}, #{fileSize},
            #{fileType}, sysdate()
        )
    </insert>

    <delete id="deleteById" parameterType="String">
        delete from resume_attachment where id = #{id}
    </delete>

    <delete id="deleteByResumeId" parameterType="String">
        delete from resume_attachment where resume_id = #{resumeId}
    </delete>
</mapper> 