package com.ruoyi.web.controller.talent;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.talent.domain.JobFavorite;
import com.ruoyi.talent.service.IJobFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 职位收藏Controller
 */
@RestController
@RequestMapping("/talent/favorite")
public class JobFavoriteController extends BaseController {
    @Autowired
    private IJobFavoriteService jobFavoriteService;

    /**
     * 查询收藏列表
     */
    @GetMapping("/list")
    public TableDataInfo list(JobFavorite jobFavorite) {
        startPage();
        // 只能查看自己的收藏
        jobFavorite.setUserId(SecurityUtils.getUserId());
        List<JobFavorite> list = jobFavoriteService.selectJobFavoriteList(jobFavorite);
        return getDataTable(list);
    }

    /**
     * 检查是否已收藏
     */
    @GetMapping("/check/{jobId}")
    public AjaxResult checkFavorite(@PathVariable("jobId") Long jobId) {
        boolean isFavorite = jobFavoriteService.checkIsFavorite(SecurityUtils.getUserId(), jobId);
        return AjaxResult.success().put("isFavorite", isFavorite);
    }

    /**
     * 切换收藏状态
     */
    @PostMapping("/toggle/{jobId}")
    public AjaxResult toggleFavorite(@PathVariable("jobId") Long jobId) {
        boolean isFavorite = jobFavoriteService.toggleFavorite(SecurityUtils.getUserId(), jobId);
        return AjaxResult.success().put("isFavorite", isFavorite);
    }

    /**
     * 取消收藏
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(jobFavoriteService.deleteJobFavoriteByIds(ids));
    }
} 