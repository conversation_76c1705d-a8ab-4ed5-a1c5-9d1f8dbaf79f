package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.TalentMessage;
import com.ruoyi.system.mapper.TalentMessageMapper;
import com.ruoyi.system.service.ITalentMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TalentMessageServiceImpl implements ITalentMessageService {
    
    @Autowired
    private TalentMessageMapper messageMapper;

    @Override
    public List<TalentMessage> selectMessageList(TalentMessage message) {
        return messageMapper.selectMessageList(message);
    }

    @Override
    public int selectUnreadCount(Long userId) {
        return messageMapper.selectUnreadCount(userId);
    }

    @Override
    public int updateMessageRead(String messageId) {
        return messageMapper.updateMessageRead(messageId);
    }

    @Override
    public int updateAllMessageRead(Long userId) {
        return messageMapper.updateAllMessageRead(userId);
    }

    @Override
    public void sendMessage(TalentMessage message) {
        message.setStatus("UNREAD");
        messageMapper.insertMessage(message);
    }
} 