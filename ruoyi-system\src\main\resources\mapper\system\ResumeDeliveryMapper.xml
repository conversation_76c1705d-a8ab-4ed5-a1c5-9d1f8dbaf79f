<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ResumeDeliveryMapper">

    <resultMap type="ResumeDelivery" id="DeliveryResult">
        <id     property="id"           column="id"            />
        <result property="resumeId"     column="resume_id"     />
        <result property="jobId"        column="job_id"        />
        <result property="userId"       column="user_id"       />
        <result property="status"       column="status"        />
        <result property="deliveryTime" column="delivery_time" />
        <result property="interviewTime" column="interview_time" />
        <result property="feedback"     column="feedback"      />
        <result property="createTime"   column="created_at"    />
        <result property="updateTime"   column="updated_at"    />
        <result property="jobTitle"     column="job_title"     />
        <result property="resumeTitle"  column="resume_title"  />
        <result property="resumeName"   column="resume_name"   />
        <result property="companyName"  column="company_name"  />
        <result property="companyId"    column="company_id"    />
        <result property="phone"        column="phone"         />
        <result property="email"        column="email"         />
        <result property="gender"       column="gender"        />
        <result property="ageRange"     column="age_range"     />
        <result property="education"    column="education"     />
        <result property="experience"   column="experience"    />
    </resultMap>

    <insert id="insertDelivery" parameterType="ResumeDelivery">
        insert into resume_delivery (
            id, resume_id, job_id, user_id, status,
            delivery_time, created_at, updated_at
        ) values (
                     #{id}, #{resumeId}, #{jobId}, #{userId}, #{status},
                     sysdate(), sysdate(), sysdate()
                 )
    </insert>

    <update id="updateDelivery" parameterType="ResumeDelivery">
        update resume_delivery
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="interviewTime != null">interview_time = #{interviewTime},</if>
            <if test="feedback != null">feedback = #{feedback},</if>
            updated_at = sysdate()
        </set>
        where id = #{id}
    </update>

    <select id="selectDeliveryList" parameterType="ResumeDelivery" resultMap="DeliveryResult">
        SELECT
            rd.*,
            COALESCE(j.title, '职位已删除') as job_title,
            j.company_id,
            COALESCE(c.name, '公司已删除') as company_name,
            COALESCE(r.resume_title, '简历已删除') as resume_title,
            COALESCE(r.name, '未知') as resume_name,
            r.phone,
            r.email,
            r.gender,
            r.birthday,
            rd.resume_id as debug_resume_id,
            CASE
                WHEN TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 18 AND 25 THEN '18-25'
                WHEN TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 26 AND 30 THEN '26-30'
                WHEN TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 31 AND 35 THEN '31-35'
                WHEN TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 36 AND 40 THEN '36-40'
                WHEN TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) >= 41 THEN '41+'
                ELSE NULL
            END as age_range,
            (SELECT MAX(re.degree) FROM resume_education re WHERE re.resume_id = r.id) as education,
            COALESCE(
                (SELECT TIMESTAMPDIFF(YEAR, MIN(rw.start_date), IFNULL(MAX(rw.end_date), CURDATE()))
                 FROM resume_work rw WHERE rw.resume_id = r.id),
                0
            ) as experience
        FROM resume_delivery rd
        LEFT JOIN talent_job j ON rd.job_id = j.id
        LEFT JOIN company c ON j.company_id = c.id
        LEFT JOIN resume r ON rd.resume_id = r.id
        <where>
            <if test="jobId != null">
                AND rd.job_id = #{jobId}
            </if>
            <if test="userId != null">
                AND rd.user_id = #{userId}
            </if>
            <if test="companyId != null">
                AND j.company_id = #{companyId}
            </if>
            <if test="status != null and status != ''">
                AND rd.status = #{status}
            </if>
            <if test="gender != null and gender != ''">
                AND r.gender = #{gender}
            </if>
            <if test="ageRange != null and ageRange != ''">
                <choose>
                    <when test="ageRange == '18-25'">
                        AND TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 18 AND 25
                    </when>
                    <when test="ageRange == '26-30'">
                        AND TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 26 AND 30
                    </when>
                    <when test="ageRange == '31-35'">
                        AND TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 31 AND 35
                    </when>
                    <when test="ageRange == '36-40'">
                        AND TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 36 AND 40
                    </when>
                    <when test="ageRange == '41-100'">
                        AND TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) >= 41
                    </when>
                </choose>
            </if>
            <if test="education != null and education != ''">
                AND EXISTS (
                    SELECT 1 FROM resume_education re 
                    WHERE re.resume_id = r.id AND re.degree = #{education}
                )
            </if>
            <if test="experience != null and experience != ''">
                <choose>
                    <when test="experience == '应届生'">
                        AND NOT EXISTS (SELECT 1 FROM resume_work rw WHERE rw.resume_id = r.id)
                    </when>
                    <when test="experience == '1-3'">
                        AND (SELECT TIMESTAMPDIFF(YEAR, MIN(rw.start_date), IFNULL(MAX(rw.end_date), CURDATE())) 
                             FROM resume_work rw WHERE rw.resume_id = r.id) BETWEEN 1 AND 3
                    </when>
                    <when test="experience == '3-5'">
                        AND (SELECT TIMESTAMPDIFF(YEAR, MIN(rw.start_date), IFNULL(MAX(rw.end_date), CURDATE())) 
                             FROM resume_work rw WHERE rw.resume_id = r.id) BETWEEN 3 AND 5
                    </when>
                    <when test="experience == '5-10'">
                        AND (SELECT TIMESTAMPDIFF(YEAR, MIN(rw.start_date), IFNULL(MAX(rw.end_date), CURDATE())) 
                             FROM resume_work rw WHERE rw.resume_id = r.id) BETWEEN 5 AND 10
                    </when>
                    <when test="experience == '10+'">
                        AND (SELECT TIMESTAMPDIFF(YEAR, MIN(rw.start_date), IFNULL(MAX(rw.end_date), CURDATE())) 
                             FROM resume_work rw WHERE rw.resume_id = r.id) > 10
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY rd.delivery_time DESC
    </select>

    <select id="selectDeliveryById" parameterType="String" resultMap="DeliveryResult">
        SELECT
            rd.*,
            COALESCE(j.title, '职位已删除') as job_title,
            j.company_id,
            COALESCE(c.name, '公司已删除') as company_name,
            COALESCE(r.resume_title, '简历已删除') as resume_title,
            COALESCE(r.name, '未知') as resume_name,
            r.phone,
            r.email,
            r.gender,
            r.birthday,
            CASE
                WHEN TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 18 AND 25 THEN '18-25'
                WHEN TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 26 AND 30 THEN '26-30'
                WHEN TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 31 AND 35 THEN '31-35'
                WHEN TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) BETWEEN 36 AND 40 THEN '36-40'
                WHEN TIMESTAMPDIFF(YEAR, r.birthday, CURDATE()) >= 41 THEN '41+'
                ELSE NULL
            END as age_range,
            (SELECT MAX(re.degree) FROM resume_education re WHERE re.resume_id = r.id) as education,
            COALESCE(
                (SELECT TIMESTAMPDIFF(YEAR, MIN(rw.start_date), IFNULL(MAX(rw.end_date), CURDATE()))
                 FROM resume_work rw WHERE rw.resume_id = r.id),
                0
            ) as experience
        FROM resume_delivery rd
        LEFT JOIN talent_job j ON rd.job_id = j.id
        LEFT JOIN company c ON j.company_id = c.id
        LEFT JOIN resume r ON rd.resume_id = r.id
        WHERE rd.id = #{id}
    </select>

    <select id="selectDeliveryByJobAndUser" resultMap="DeliveryResult">
        select * from resume_delivery
        where job_id = #{jobId} and user_id = #{userId}
            limit 1
    </select>

    <delete id="deleteDeliveryById" parameterType="String">
        delete from resume_delivery where id = #{id}
    </delete>

    <select id="countAllDeliveries" resultType="Integer">
        SELECT COUNT(*) FROM resume_delivery
    </select>

    <select id="countTodayDeliveries" resultType="Integer">
        SELECT COUNT(*)
        FROM resume_delivery
        WHERE DATE(delivery_time) = CURDATE()
    </select>

    <update id="updateDeliveryStatus" parameterType="Map">
        UPDATE resume_delivery
        SET status = #{status},
            feedback = #{feedback},
            updated_at = sysdate()
        WHERE id = #{id}
    </update>

    <select id="selectResumeDeliveryCount" parameterType="ResumeDelivery" resultType="int">
        select count(1) from resume_delivery
        <where>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
    </select>

    <select id="countUserDeliveries" resultType="int">
        SELECT COUNT(*) FROM resume_delivery WHERE user_id = #{userId}
    </select>

    <select id="countUserInterviewInvites" resultType="int">
        SELECT COUNT(*) FROM resume_delivery 
        WHERE user_id = #{userId} AND status = '2'
    </select>
    
    <delete id="deleteDeliveryByResumeId" parameterType="String">
        delete from resume_delivery where resume_id = #{resumeId}
    </delete>
    
    <select id="checkResumeDeliveredToCompany" resultType="int">
        SELECT COUNT(1)
        FROM resume_delivery rd
        JOIN talent_job j ON rd.job_id = j.id
        WHERE rd.resume_id = #{resumeId}
        AND j.company_id = #{companyId}
    </select>

    <select id="selectRecentDeliveries" parameterType="int" resultType="java.util.Map">
        SELECT
            rd.id,
            rd.resume_id,
            rd.job_id,
            rd.user_id,
            rd.status,
            rd.delivery_time as create_time,
            r.name as resume_name,
            r.phone as resume_phone,
            j.title as job_title,
            j.company_name as company,
            j.location,
            j.salary,
            u.user_name,
            CASE rd.status
                WHEN '0' THEN '待处理'
                WHEN '1' THEN '已通过'
                WHEN '2' THEN '已拒绝'
                WHEN '-1' THEN '已取消'
                ELSE '未知状态'
            END as status_text,
            DATEDIFF(NOW(), rd.delivery_time) as days_ago
        FROM resume_delivery rd
        LEFT JOIN resume r ON rd.resume_id = r.id
        LEFT JOIN talent_job j ON rd.job_id = j.id
        LEFT JOIN sys_user u ON rd.user_id = u.user_id
        WHERE rd.status != '-1'
        ORDER BY rd.delivery_time DESC
        LIMIT #{limit}
    </select>
</mapper>