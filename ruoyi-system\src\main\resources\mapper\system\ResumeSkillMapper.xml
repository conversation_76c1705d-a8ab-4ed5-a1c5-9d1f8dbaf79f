<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ResumeSkillMapper">
    
    <resultMap type="ResumeSkill" id="SkillResult">
        <id     property="id"          column="id"          />
        <result property="resumeId"    column="resume_id"   />
        <result property="name"        column="name"        />
        <result property="level"       column="level"       />
        <result property="description" column="description" />
    </resultMap>

    <select id="selectByResumeId" parameterType="String" resultMap="SkillResult">
        select * from resume_skill 
        where resume_id = #{resumeId}
        order by level desc
    </select>

    <insert id="insert" parameterType="ResumeSkill">
        insert into resume_skill (
            id, resume_id, name, level, description
        ) values (
            #{id}, #{resumeId}, #{name}, #{level}, #{description}
        )
    </insert>

    <update id="update" parameterType="ResumeSkill">
        update resume_skill
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="level != null">level = #{level},</if>
            <if test="description != null">description = #{description},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="String">
        delete from resume_skill where id = #{id}
    </delete>

    <delete id="deleteByResumeId" parameterType="String">
        delete from resume_skill where resume_id = #{resumeId}
    </delete>
</mapper> 