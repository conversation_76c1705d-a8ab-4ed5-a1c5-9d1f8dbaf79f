package com.ruoyi.system.service;

import com.ruoyi.system.domain.ResumeDelivery;
import java.util.List;

public interface IResumeDeliveryService {
    /**
     * 查询投递列表
     */
    List<ResumeDelivery> selectDeliveryList(ResumeDelivery delivery);

    /**
     * 查询投递详细
     */
    ResumeDelivery selectDeliveryById(String id);

    /**
     * 更新投递状态
     */
    int updateDeliveryStatus(ResumeDelivery delivery);
}
