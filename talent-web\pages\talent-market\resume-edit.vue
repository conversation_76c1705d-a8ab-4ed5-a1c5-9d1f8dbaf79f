<template>
  <view class="resume-edit">
    <!-- 顶部进度条 -->
    <view class="progress-bar">
      <view 
        v-for="(step, index) in steps" 
        :key="step.key"
        class="step-item"
        :class="{ 'active': currentStep >= index, 'completed': currentStep > index }"
      >
        <view class="step-number">{{ index + 1 }}</view>
        <text class="step-title">{{ step.title }}</text>
      </view>
    </view>

    <!-- 表单内容区 -->
    <scroll-view class="form-container" scroll-y>
      <!-- 基本信息 -->
      <view v-show="currentStep === 0" class="form-section">
        <view class="section-header">
          <text class="section-title">基本信息</text>
          <text class="section-desc">完善您的个人基本信息</text>
        </view>

        <uni-forms ref="basicForm" :model="formData" :rules="basicRules">
          <view class="avatar-container">
            <text class="avatar-label">证件照 <text class="required-mark">*</text></text>
            <view class="avatar-upload" @click="chooseAvatar">
              <image 
                v-if="formData.avatar" 
                :src="formData.avatar" 
                mode="aspectFill"
                class="avatar-image"
              ></image>
              <view v-else class="upload-placeholder">
                <text class="add-icon">+</text>
                <text class="upload-text">添加证件照</text>
              </view>
            </view>
            <view class="avatar-tips">
              <text>请上传正式证件照，建议尺寸2寸（413x626像素）</text>
            </view>
          </view>

          <uni-forms-item label="姓名" required name="name">
            <uni-easyinput v-model="formData.name" placeholder="请输入姓名"/>
          </uni-forms-item>

          <uni-forms-item label="性别" required name="gender">
            <uni-data-select
              v-model="formData.gender"
              :localdata="genderTypes"
              placeholder="请选择性别"
            />
          </uni-forms-item>

          <uni-forms-item label="出生日期" required name="birthday">
            <!-- 使用uni-app内置picker组件 -->
            <view class="birthday-container">
              <picker mode="date" :value="formatPickerDate(formData.birthday)" @change="onBirthdayChange" :end="new Date().toISOString().split('T')[0]">
                <view class="custom-date-picker compact">
                  <text class="date-text" :class="{ placeholder: !formData.birthday }">
                    {{ formatDisplayDate(formData.birthday) || '请选择出生日期' }}
                  </text>
                  <uni-icons type="calendar" size="16" color="#999"></uni-icons>
                </view>
              </picker>
            </view>
          </uni-forms-item>
          

          <uni-forms-item label="民族" required name="nation">
            <uni-data-select
              v-model="formData.nation"
              :localdata="nationOptions"
              placeholder="请选择民族"
            />
          </uni-forms-item>
          
          <uni-forms-item label="籍贯" required name="nativePlace">
            <uni-easyinput v-model="formData.nativePlace" placeholder="请输入籍贯，如：浙江杭州"/>
          </uni-forms-item>
          
          <uni-forms-item label="政治面貌" required name="politicalStatus">
            <uni-data-select
              v-model="formData.politicalStatus"
              :localdata="politicalStatusOptions"
              placeholder="请选择政治面貌"
            />
          </uni-forms-item>
          
          <uni-forms-item label="专业技术职务" name="technicalPosition">
            <uni-easyinput v-model="formData.technicalPosition" placeholder="请输入专业技术职务（选填）"/>
          </uni-forms-item>

          <uni-forms-item label="手机号码" required name="phone">
            <uni-easyinput v-model="formData.phone" placeholder="请输入手机号码"/>
          </uni-forms-item>

          <uni-forms-item label="邮箱" required name="email">
            <uni-easyinput v-model="formData.email" placeholder="请输入邮箱"/>
          </uni-forms-item>

          <uni-forms-item label="所在地点" required name="location">
            <uni-easyinput v-model="formData.location" placeholder="请输入所在地点"/>
          </uni-forms-item>

          <uni-forms-item label="现任职务" required name="currentPosition">
            <uni-easyinput v-model="formData.currentPosition" placeholder="请输入现任职务"/>
          </uni-forms-item>

        </uni-forms>
      </view>

      <!-- 教育经历 -->
      <view v-show="currentStep === 1" class="form-section">
        <view class="section-header">
          <text class="section-title">教育经历</text>
          <text class="section-desc">填写您的学习经历</text>
        </view>

        <uni-forms ref="educationForm" :model="formData" :rules="educationRules">
          <view 
            v-for="(edu, index) in formData.education" 
            :key="index"
            class="education-item"
          >
            <view class="item-header">
              <text class="item-title">教育经历 {{ index + 1 }}</text>
              <text class="delete-btn" @click="deleteEducation(index)">删除</text>
            </view>

            <uni-forms-item :label="'学校名称'" required :name="`education.${index}.school`">
              <uni-easyinput v-model="edu.school" placeholder="请输入学校名称"/>
            </uni-forms-item>

            <uni-forms-item :label="'专业'" required :name="`education.${index}.major`">
              <uni-easyinput v-model="edu.major" placeholder="请输入专业名称"/>
            </uni-forms-item>

            <uni-forms-item :label="'学历'" required :name="`education.${index}.degree`">
              <uni-data-select
                v-model="edu.degree"
                :localdata="degreeTypes"
                placeholder="请选择学历"
              />
            </uni-forms-item>

            <uni-forms-item :label="'起止时间'" required :name="`education.${index}.startDate`">
              <view class="date-range">
                <view class="birthday-container">
                  <picker mode="date" :value="formatPickerDate(edu.startDate)" @change="(e) => onEducationStartDateChange(e, index)" :end="formatPickerDate(edu.endDate) || new Date().toISOString().split('T')[0]">
                    <view class="custom-date-picker compact">
                      <text class="date-text" :class="{ placeholder: !edu.startDate }">
                        {{ formatDisplayDate(edu.startDate) || '开始日期' }}
                      </text>
                      <uni-icons type="calendar" size="16" color="#999"></uni-icons>
                    </view>
                  </picker>
                </view>
                <text class="date-separator">至</text>
                <view class="birthday-container">
                  <picker mode="date" :value="formatPickerDate(edu.endDate)" @change="(e) => onEducationEndDateChange(e, index)" :start="formatPickerDate(edu.startDate)" :end="new Date().toISOString().split('T')[0]">
                    <view class="custom-date-picker compact">
                      <text class="date-text" :class="{ placeholder: !edu.endDate }">
                        {{ formatDisplayDate(edu.endDate) || '结束日期' }}
                      </text>
                      <uni-icons type="calendar" size="16" color="#999"></uni-icons>
                    </view>
                  </picker>
                </view>
              </view>
            </uni-forms-item>
            
            <uni-forms-item :label="'是否最高学历'" :name="`education.${index}.isHighest`">
              <switch 
                :checked="Boolean(edu.isHighest)" 
                @change="(e) => handleHighestEducation(e, index)"
              />
            </uni-forms-item>
          </view>
        </uni-forms>

        <button class="add-btn" @click="addEducation">
          <text class="iconfont icon-plus"></text>
          添加教育经历
        </button>
      </view>

      <!-- 工作经历 -->
      <view v-show="currentStep === 2" class="form-section">
        <view class="section-header">
          <text class="section-title">工作经历</text>
          <text class="section-desc">填写您的工作经验</text>
        </view>

        <uni-forms ref="workForm" :model="formData" :rules="workRules">
          <view 
            v-for="(work, index) in formData.work" 
            :key="index"
            class="work-item"
          >
            <view class="item-header">
              <text class="item-title">工作经历 {{ index + 1 }}</text>
              <text class="delete-btn" @click="deleteWork(index)">删除</text>
            </view>

            <uni-forms-item label="公司名称" required :name="`work.${index}.company`">
              <uni-easyinput 
                v-model="work.company" 
                placeholder="请输入公司名称"
                trim
              />
            </uni-forms-item>

            <uni-forms-item label="职位名称" required :name="`work.${index}.position`">
              <uni-easyinput 
                v-model="work.position" 
                placeholder="请输入职位名称"
                trim
              />
            </uni-forms-item>

            <uni-forms-item label="所在部门" :name="`work.${index}.department`">
              <uni-easyinput 
                v-model="work.department" 
                placeholder="请输入所在部门"
                trim
              />
            </uni-forms-item>

            <uni-forms-item label="起止时间" required :name="`work.${index}.startDate`">
              <view class="date-range">
                <view class="birthday-container">
                  <picker mode="date" :value="formatPickerDate(work.startDate)" @change="(e) => onWorkStartDateChange(e, index)" :end="formatPickerDate(work.endDate) || new Date().toISOString().split('T')[0]">
                    <view class="custom-date-picker compact">
                      <text class="date-text" :class="{ placeholder: !work.startDate }">
                        {{ formatDisplayDate(work.startDate) || '开始日期' }}
                      </text>
                      <uni-icons type="calendar" size="16" color="#999"></uni-icons>
                    </view>
                  </picker>
                </view>
                <text class="date-separator">至</text>
                <view class="birthday-container">
                  <picker mode="date" :value="formatPickerDate(work.endDate)" @change="(e) => onWorkEndDateChange(e, index)" :start="formatPickerDate(work.startDate)" :end="new Date().toISOString().split('T')[0]">
                    <view class="custom-date-picker compact">
                      <text class="date-text" :class="{ placeholder: !work.endDate }">
                        {{ formatDisplayDate(work.endDate) || '结束日期' }}
                      </text>
                      <uni-icons type="calendar" size="16" color="#999"></uni-icons>
                    </view>
                  </picker>
                </view>
              </view>
            </uni-forms-item>

            <uni-forms-item label="工作描述" :name="`work.${index}.description`">
              <uni-easyinput 
                v-model="work.description" 
                type="textarea"
                placeholder="请描述您的工作职责和成就"
                :maxlength="500"
                :autoHeight="true"
              />
            </uni-forms-item>
          </view>
        </uni-forms>

        <button class="add-btn" @click="addWork">
          <text class="iconfont icon-plus"></text>
          添加工作经历
        </button>
      </view>

      <!-- 项目经历 -->
      <view v-show="currentStep === 3" class="form-section">
        <view class="section-header">
          <text class="section-title">项目经历</text>
          <text class="section-desc">展示您参与的重要项目</text>
        </view>

        <uni-forms ref="projectForm" :model="formData" :rules="projectRules">
          <view 
            v-for="(project, index) in formData.project" 
            :key="index"
            class="project-item"
          >
            <view class="item-header">
              <text class="item-title">项目经历 {{ index + 1 }}</text>
              <text class="delete-btn" @click="deleteProject(index)">删除</text>
            </view>

            <uni-forms-item label="项目名称" required :name="`project.${index}.name`">
              <uni-easyinput 
                v-model="project.name" 
                placeholder="请输入项目名称"
                trim
              />
            </uni-forms-item>

            <uni-forms-item label="担任角色" required :name="`project.${index}.role`">
              <uni-easyinput 
                v-model="project.role" 
                placeholder="请输入您在项目中担任的角色"
                trim
              />
            </uni-forms-item>

            <uni-forms-item label="起止时间" required :name="`project.${index}.startDate`">
              <view class="date-range">
                <view class="birthday-container">
                  <picker mode="date" :value="formatPickerDate(project.startDate)" @change="(e) => onProjectStartDateChange(e, index)" :end="formatPickerDate(project.endDate) || new Date().toISOString().split('T')[0]">
                    <view class="custom-date-picker compact">
                      <text class="date-text" :class="{ placeholder: !project.startDate }">
                        {{ formatDisplayDate(project.startDate) || '开始日期' }}
                      </text>
                      <uni-icons type="calendar" size="16" color="#999"></uni-icons>
                    </view>
                  </picker>
                </view>
                <text class="date-separator">至</text>
                <view class="birthday-container">
                  <picker mode="date" :value="formatPickerDate(project.endDate)" @change="(e) => onProjectEndDateChange(e, index)" :start="formatPickerDate(project.startDate)" :end="new Date().toISOString().split('T')[0]">
                    <view class="custom-date-picker compact">
                      <text class="date-text" :class="{ placeholder: !project.endDate }">
                        {{ formatDisplayDate(project.endDate) || '结束日期' }}
                      </text>
                      <uni-icons type="calendar" size="16" color="#999"></uni-icons>
                    </view>
                  </picker>
                </view>
              </view>
            </uni-forms-item>

            <uni-forms-item label="项目描述">
              <uni-easyinput 
                v-model="project.description" 
                type="textarea"
                placeholder="请描述项目的背景、目标和主要功能"
                :maxlength="1000"
                :autoHeight="true"
              />
            </uni-forms-item>

            <uni-forms-item label="项目成果">
              <uni-easyinput 
                v-model="project.achievement" 
                type="textarea"
                placeholder="请描述您在项目中取得的成果和贡献"
                :maxlength="1000"
                :autoHeight="true"
              />
            </uni-forms-item>
          </view>
        </uni-forms>

        <button class="add-btn" @click="addProject">
          <text class="iconfont icon-plus"></text>
          添加项目经历
        </button>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="action-bar">
      <button 
        v-if="currentStep > 0" 
        class="prev-btn" 
        @click="prevStep"
      >上一步</button>
      <button 
        v-if="currentStep < steps.length - 1" 
        class="next-btn" 
        @click="nextStep"
      >下一步</button>
      <button 
        v-else 
        class="submit-btn" 
        @click="nextStep"
      >保存简历</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, watch, reactive, nextTick } from 'vue'
import { getResumeDetail, saveResume as saveResumeAPI, uploadAvatar } from '@/utils/talent-market'
import config from '../../config'
// 获取组件实例
const { proxy } = getCurrentInstance()



// 步骤配置
const steps = [
  { key: 'basic', title: '基本信息' },
  { key: 'education', title: '教育经历' },
  { key: 'work', title: '工作经历' },
  { key: 'project', title: '项目经历' }
]

const currentStep = ref(0)
const resumeId = ref(null)
const mode = ref('create')

// 日期选择器组件切换 - 默认使用picker组件
const usePickerComponent = ref(true)

// 使用 reactive 来管理表单数据
const formData = ref({
  avatar: '',
  resumeTitle: '',
  name: '',
  gender: '',
  birthday: '',
  age: '',
  nation: '',
  nativePlace: '',
  politicalStatus: '',
  technicalPosition: '',
  phone: '',
  email: '',
  location: '',
  // 现任职务相关字段
  currentPosition: '',
  // 学历和毕业院校字段
  highestDegree: '',
  highestSchool: '',
  // 标记字段是否被手动修改过
  highestDegreeManuallySet: false,
  highestSchoolManuallySet: false,
  education: [],
  work: [],
  project: []
})

// 监听教育经历变化，自动更新最高学历信息
watch(() => formData.value.education, (newEducation) => {
  // 检查是否有标记为最高学历的记录
  const highestEdu = newEducation.find(edu => edu.isHighest);
  if (highestEdu) {
    // 只有在用户没有手动修改过的情况下才自动更新
    if (!formData.value.highestDegreeManuallySet) {
      formData.value.highestDegree = highestEdu.degree || '';
    }
    if (!formData.value.highestSchoolManuallySet) {
      formData.value.highestSchool = highestEdu.school || '';
    }
  }
}, { deep: true })

// 监听手动修改最高学历和毕业院校
watch(() => formData.value.highestDegree, () => {
  formData.value.highestDegreeManuallySet = true;
})

watch(() => formData.value.highestSchool, () => {
  formData.value.highestSchoolManuallySet = true;
})

// 选择并上传头像
const chooseAvatar = async () => {
  try {
    // 选择图片
    const [tempFilePath] = await new Promise((resolve, reject) => {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => resolve(res.tempFilePaths),
        fail: reject
      })
    })

    // 获取图片信息，检查尺寸
    const imageInfo = await new Promise((resolve, reject) => {
      uni.getImageInfo({
        src: tempFilePath,
        success: resolve,
        fail: reject
      })
    })
    
    // 检查图片比例是否接近2寸证件照比例 (413:626 ≈ 2:3)
    const ratio = imageInfo.width / imageInfo.height
    if (ratio < 0.6 || ratio > 0.7) {
      uni.showModal({
        title: '提示',
        content: '建议上传2寸证件照，宽高比例约为2:3，当前图片比例不符合要求，是否继续上传？',
        success: async (res) => {
          if (res.confirm) {
            await uploadAvatarImage(tempFilePath)
          }
        }
      })
    } else {
      await uploadAvatarImage(tempFilePath)
    }
  } catch (error) {
    console.error('头像选择失败:', error)
    uni.showToast({
      title: error.message || '选择失败',
      icon: 'none'
    })
  }
}

// 上传头像图片
const uploadAvatarImage = async (tempFilePath) => {
  try {
    // 显示上传中
    uni.showLoading({
      title: '上传中...',
      mask: true
    })

    const token = uni.getStorageSync('token')
    
    // 直接使用uni.uploadFile替代uploadAvatar函数
    const result = await new Promise((resolve, reject) => {
      uni.uploadFile({
        url: config.baseUrl + 'talent/resume/avatar',
        filePath: tempFilePath,
        name: 'file', // 确保与后端参数名匹配
        header: {
          'Authorization': token
        },
        success: (uploadRes) => {
          try {
            const data = JSON.parse(uploadRes.data)
            resolve(data)
          } catch (e) {
            reject(new Error('解析响应数据失败'))
          }
        },
        fail: (err) => {
          console.error('上传请求失败:', err)
          reject(err)
        }
      })
    })
    
    console.log('上传结果:', result)

    if (result && result.code === 200) {
      const imagePath = result.msg // 使用msg字段而不是data字段
      console.log("config.baseUrl",config.baseUrl);
      const baseUrl = config.baseUrl.endsWith('/') ? config.baseUrl.slice(0, -1) : config.baseUrl
      const path = imagePath.startsWith('/') ? imagePath : '/' + imagePath
      
      formData.value.avatar = baseUrl + path
      
      uni.showToast({
        title: '上传成功',
        icon: 'success'
      })
    } else {
      throw new Error(result?.msg || '上传失败')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    uni.showToast({
      title: error.message || '上传失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

// 监听头像变化
watch(() => formData.avatar, (newVal) => {
  console.log('头像已更新:', newVal)
}, { immediate: true })

// 表单验证规则
const basicRules = {
  avatar: {
    rules: [
      { required: true, errorMessage: '请上传证件照' }
    ]
  },
  name: {
    rules: [
      { required: true, errorMessage: '请输入姓名' }
    ]
  },
  gender: {
    rules: [
      { required: true, errorMessage: '请选择性别' }
    ]
  },
  birthday: {
    rules: [
      { required: true, errorMessage: '请选择出生日期' }
    ]
  },
  phone: {
    rules: [
      { required: true, errorMessage: '请输入手机号' },
      { pattern: /^1[3-9]\d{9}$/, errorMessage: '请输入正确的手机号' }
    ]
  },
  email: {
    rules: [
      { required: true, errorMessage: '请输入邮箱' },
      { format: 'email', errorMessage: '请输入正确的邮箱格式' }
    ]
  },
  nation: {
    rules: [
      { required: true, errorMessage: '请选择民族' }
    ]
  },
  nativePlace: {
    rules: [
      { required: true, errorMessage: '请输入籍贯' }
    ]
  },
  politicalStatus: {
    rules: [
      { required: true, errorMessage: '请选择政治面貌' }
    ]
  },
  highestDegree: {
    rules: [
      { required: true, errorMessage: '请选择学历' }
    ]
  },
  highestSchool: {
    rules: [
      { required: true, errorMessage: '请输入毕业院校' }
    ]
  }
}

// 添加教育经历
const addEducation = () => {
  formData.value.education.push({
    school: '',
    major: '',
    degree: '',
    startDate: '',
    endDate: '',
    isHighest: false // 确保新添加的教育经历默认为非最高学历
  })
}

// 删除教育经历
const deleteEducation = (index) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条教育经历吗？',
    success: (res) => {
      if (res.confirm) {
        formData.value.education.splice(index, 1)
      }
    }
  })
}

// 添加工作经历
const addWork = () => {
  formData.value.work.push({
    company: '',
    position: '',
    department: '',
    startDate: '',
    endDate: '',
    description: ''
  })
}

// 删除工作经历
const deleteWork = (index) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条工作经历吗？',
    success: (res) => {
      if (res.confirm) {
        formData.value.work.splice(index, 1)
      }
    }
  })
}

// 添加项目经历
const addProject = () => {
  formData.value.project.push({
    name: '',
    role: '',
    startDate: '',
    endDate: '',
    description: '',
    achievement: ''
  })
}

// 删除项目经历
const deleteProject = (index) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条项目经历吗？',
    success: (res) => {
      if (res.confirm) {
        formData.value.project.splice(index, 1)
      }
    }
  })
}

// 处理最高学历切换
const handleHighestEducation = (e, index) => {
  console.log('Switch事件详情:', e)
  console.log('Switch事件detail:', e.detail)
  console.log('Switch事件value:', e.detail.value)
  
  const isChecked = Boolean(e.detail.value)
  console.log(`切换最高学历，索引: ${index}, 新状态: ${isChecked}`)
  
  // 如果选中了当前项为最高学历，则其他项都设为非最高学历
  if (isChecked) {
    formData.value.education.forEach((item, idx) => {
      item.isHighest = idx === index
      console.log(`教育经历 ${idx}: isHighest = ${item.isHighest}`)
    })
  } else {
    formData.value.education[index].isHighest = false
    console.log(`取消最高学历设置，索引: ${index}`)
  }
}

// 同步最高学历信息
const syncHighestEducation = () => {
  // 如果已经手动设置了最高学历和毕业院校，则不需要自动同步
  if (formData.value.highestDegree && formData.value.highestSchool) {
    return;
  }
  
  // 查找标记为最高学历的记录
  const highestEdu = formData.value.education.find(edu => edu.isHighest);
  
  if (highestEdu) {
    // 如果找到了标记为最高学历的记录，使用它
    formData.value.highestDegree = highestEdu.degree || '';
    formData.value.highestSchool = highestEdu.school || '';
  } else if (formData.value.education.length > 0) {
    // 如果没有标记最高学历但有教育经历，使用第一条记录
    formData.value.highestDegree = formData.value.education[0].degree || '';
    formData.value.highestSchool = formData.value.education[0].school || '';
  }
}


// 现任职务验证规则添加到基本信息中
basicRules.currentPosition = {
  rules: [
    { required: true, errorMessage: '请输入现任职务' }
  ]
}

// 验证当前步骤
const validateCurrentStep = async () => {
  try {
    let formRef = null
    switch (currentStep.value) {
      case 0:
        formRef = proxy.$refs.basicForm
        break
      case 1:
        formRef = proxy.$refs.educationForm
        break
      case 2:
        formRef = proxy.$refs.workForm
        break
      case 3:
        formRef = proxy.$refs.projectForm
        break
    }

    if (!formRef) {
      console.warn('未找到表单实例')
      return true
    }

    // 使用 uni-forms 的验证方法
    const valid = await formRef.validate()
    console.log('表单验证结果:', valid)
    
    return valid
  } catch (error) {
    console.error('表单验证错误:', error)
    return false
  }
}

// 验证所有表单
const validateAllForms = async () => {
  try {
    // 验证基本信息
    const basicValid = await proxy.$refs.basicForm.validate();
    if (!basicValid) {
      currentStep.value = 0; // 跳转到基本信息页
      uni.showToast({
        title: '请完善基本信息',
        icon: 'none'
      });
      return false;
    }
    
    // 验证教育经历
    if (formData.value.education.length === 0) {
      currentStep.value = 1; // 跳转到教育经历页
      uni.showToast({
        title: '请至少添加一条教育经历',
        icon: 'none'
      });
      return false;
    }
    
    const educationValid = await proxy.$refs.educationForm.validate();
    if (!educationValid) {
      currentStep.value = 1; // 跳转到教育经历页
      uni.showToast({
        title: '请完善教育经历信息',
        icon: 'none'
      });
      return false;
    }
    
    // 验证工作经历
    if (formData.value.work.length === 0) {
      currentStep.value = 2; // 跳转到工作经历页
      uni.showToast({
        title: '请至少添加一条工作经历',
        icon: 'none'
      });
      return false;
    }
    
    const workValid = await proxy.$refs.workForm.validate();
    if (!workValid) {
      currentStep.value = 2; // 跳转到工作经历页
      uni.showToast({
        title: '请完善工作经历信息',
        icon: 'none'
      });
      return false;
    }
    
    // 验证项目经历
    if (formData.value.project.length === 0) {
      currentStep.value = 3; // 跳转到项目经历页
      uni.showToast({
        title: '请至少添加一条项目经历',
        icon: 'none'
      });
      return false;
    }
    
    const projectValid = await proxy.$refs.projectForm.validate();
    if (!projectValid) {
      currentStep.value = 3; // 跳转到项目经历页
      uni.showToast({
        title: '请完善项目经历信息',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('表单验证失败:', error);
    return false;
  }
}

// 下一步
const nextStep = async () => {
  try {
    let formRef = null
    switch (currentStep.value) {
      case 0: // 基本信息
        formRef = proxy.$refs.basicForm
        break
      case 1: // 教育经历
        formRef = proxy.$refs.educationForm
        if (formData.value.education.length === 0) {
          uni.showToast({
            title: '请至少添加一条教育经历',
            icon: 'none'
          })
          return
        }
        break
      case 2: // 工作经历
        formRef = proxy.$refs.workForm
        if (formData.value.work.length === 0) {
          uni.showToast({
            title: '请至少添加一条工作经历',
            icon: 'none'
          })
          return
        }
        break
      case 3: // 项目经历
        formRef = proxy.$refs.projectForm
        if (formData.value.project.length === 0) {
          uni.showToast({
            title: '请至少添加一条项目经历',
            icon: 'none'
          })
          return
        }
        break
    }

    if (!formRef) {
      console.warn('未找到表单实例')
      return
    }

    // 执行表单验证
    const valid = await formRef.validate()
    if (!valid) {
      uni.showToast({
        title: '请完善必填信息',
        icon: 'none'
      })
      return
    }

    // 验证通过，进入下一步或保存
    if (currentStep.value < steps.length - 1) {
      currentStep.value++
    } else {
      await handleSaveResume()
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    uni.showToast({
      title: '请完善必填信息',
      icon: 'none'
    })
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 表单引用
const basicForm = ref(null)
const intentionForm = ref(null)
const educationForm = ref(null)
const workForm = ref(null)
const projectForm = ref(null)

// 获取简历详情
onMounted(async () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const { id, mode: pageMode } = currentPage.$page?.options || {}
  
  resumeId.value = id
  mode.value = pageMode || 'create'
  
  if (id) {
    // 编辑模式：获取服务器数据
    const token = uni.getStorageSync('token')
    try {
      const result = await getResumeDetail(token, id)
      if (result && result.data) {
        const data = result.data
        
        // 确保教育经历中的isHighest字段正确转换为布尔值
        if (data.education && Array.isArray(data.education)) {
          data.education.forEach((edu, index) => {
            console.log(`原始教育经历 ${index + 1}:`, {
              school: edu.school,
              isHighest: edu.isHighest,
              type: typeof edu.isHighest,
              rawValue: edu.isHighest
            })
            
            // 确保isHighest字段为布尔值
            if (typeof edu.isHighest === 'string') {
              edu.isHighest = edu.isHighest === 'true' || edu.isHighest === '1'
            } else if (typeof edu.isHighest === 'number') {
              edu.isHighest = edu.isHighest === 1
            } else if (edu.isHighest === null || edu.isHighest === undefined) {
              edu.isHighest = false
            }
            
            // 强制转换为布尔值
            edu.isHighest = Boolean(edu.isHighest)
            
            console.log(`转换后教育经历 ${index + 1}:`, {
              school: edu.school,
              isHighest: edu.isHighest,
              type: typeof edu.isHighest,
              booleanValue: Boolean(edu.isHighest)
            })
          })
        }
        
        // 使用服务器返回的数据
        formData.value = {
          ...data
        }
        
        console.log('加载的教育经历数据:', formData.value.education)
        
        // 调试：检查每个教育经历的isHighest状态
        if (formData.value.education && Array.isArray(formData.value.education)) {
          formData.value.education.forEach((edu, index) => {
            console.log(`教育经历 ${index + 1}:`, {
              school: edu.school,
              isHighest: edu.isHighest,
              type: typeof edu.isHighest,
              booleanValue: Boolean(edu.isHighest)
            })
          })
        }
      }
    } catch (error) {
      console.error('获取简历详情失败:', error)
      uni.showToast({
        title: '获取简历详情失败',
        icon: 'none'
      })
    }
  } else {
    // 创建模式：设置默认值
    try {
      const userInfo = uni.getStorageSync('userinfo')
      console.log('当前用户信息:', userInfo) // 调试用，查看实际的数据结构

      if (userInfo) {
        Object.assign(formData.value, {
          avatar: userInfo.avatar || userInfo.avatarUrl || '',  // 增加 avatarUrl 判断
          resumeTitle: '我的简历', // 设置默认简历标题
          name: userInfo.nickName || userInfo.name || '',      // 增加 name 判断
          gender: userInfo.sex === '0' || userInfo.gender === '0' ? '女' : '男',
          nation: '汉族', // 默认民族
          nativePlace: '', // 默认籍贯为空
          politicalStatus: '群众', // 默认政治面貌
          technicalPosition: '', // 默认专业技术职务为空
          phone: userInfo.phonenumber || userInfo.phone || userInfo.mobile || '',  // 增加更多手机号字段判断
          email: userInfo.email || '',
          location: userInfo.dept?.deptName || userInfo.department || ''  // 增加 department 判断
        })

        // 打印赋值后的结果
        console.log('表单数据:', formData.value)
      } else {
        console.warn('未找到用户信息')
      }
    } catch (error) {
      console.error('初始化表单数据失败:', error)
    }
  }


})

// 出生日期选择器
const showBirthdayPicker = () => {
  console.log('showBirthdayPicker clicked, current birthday:', formData.birthday)

  showDatePicker({
    currentValue: formData.birthday,
    maxDate: new Date(),
    onSuccess: (date) => {
      console.log('Birthday date selected:', date)
      formData.birthday = date
    }
  })
}

const showEducationStartDatePicker = (index) => {
  const edu = formData.education[index]
  showDatePicker({
    currentValue: edu.startDate,
    maxDate: edu.endDate || new Date(),
    onSuccess: (date) => {
      edu.startDate = date
    }
  })
}

// 通用日期选择器函数
const showDatePicker = (options) => {
  const { currentValue, minDate, maxDate, onSuccess } = options

  console.log('showDatePicker called with options:', options)

  // 检测当前运行环境
  const isH5 = typeof window !== 'undefined' && typeof document !== 'undefined'

  if (isH5) {
    // H5端使用原生input[type="date"]
    console.log('Using H5 native date picker')

    const input = document.createElement('input')
    input.type = 'date'
    input.style.position = 'fixed'
    input.style.top = '50%'
    input.style.left = '50%'
    input.style.transform = 'translate(-50%, -50%)'
    input.style.zIndex = '99999'
    input.style.opacity = '0'
    input.style.pointerEvents = 'auto'
    input.style.width = '1px'
    input.style.height = '1px'

    // 设置日期范围
    if (minDate) {
      const minDateStr = typeof minDate === 'string' ? minDate : minDate.toISOString().split('T')[0]
      input.min = minDateStr
      console.log('Set min date:', minDateStr)
    }
    if (maxDate) {
      const maxDateStr = typeof maxDate === 'string' ? maxDate : maxDate.toISOString().split('T')[0]
      input.max = maxDateStr
      console.log('Set max date:', maxDateStr)
    }

    // 设置当前值
    if (currentValue) {
      const currentDateStr = typeof currentValue === 'string' ? currentValue : currentValue.toISOString().split('T')[0]
      input.value = currentDateStr
      console.log('Set current value:', currentDateStr)
    }

    console.log('Adding input to DOM')
    document.body.appendChild(input)

    input.addEventListener('change', function() {
      console.log('Date selected:', this.value)
      if (this.value && onSuccess) {
        onSuccess(this.value)
      }
      if (document.body.contains(this)) {
        document.body.removeChild(this)
        console.log('Input removed from DOM')
      }
    })

    input.addEventListener('blur', function() {
      console.log('Input blur event')
      setTimeout(() => {
        if (document.body.contains(this)) {
          document.body.removeChild(this)
          console.log('Input removed from DOM (blur)')
        }
      }, 100)
    })

    input.addEventListener('cancel', function() {
      console.log('Date picker cancelled')
      if (document.body.contains(this)) {
        document.body.removeChild(this)
        console.log('Input removed from DOM (cancel)')
      }
    })

    // 多种方式尝试触发日期选择器
    console.log('Attempting to trigger date picker')

    // 方法1：立即触发
    try {
      input.focus()
      input.click()
      console.log('Method 1: Direct click attempted')
    } catch (e) {
      console.error('Method 1 failed:', e)
    }

    // 方法2：延迟触发
    setTimeout(() => {
      try {
        input.focus()
        input.click()
        console.log('Method 2: Delayed click attempted')
      } catch (e) {
        console.error('Method 2 failed:', e)
      }
    }, 50)

    // 方法3：模拟用户交互
    setTimeout(() => {
      try {
        const event = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        })
        input.dispatchEvent(event)
        console.log('Method 3: Simulated click attempted')
      } catch (e) {
        console.error('Method 3 failed:', e)
      }
    }, 100)

  } else {
    // 小程序端使用uni.showDatePicker
    console.log('Using uni.showDatePicker')

    const pickerOptions = {
      success: (res) => {
        console.log('Date selected:', res.date)
        if (onSuccess) {
          onSuccess(res.date)
        }
      },
      fail: (err) => {
        console.error('Date picker failed:', err)
      }
    }

    if (currentValue) {
      pickerOptions.current = new Date(currentValue)
    }
    if (minDate) {
      pickerOptions.start = new Date(minDate)
    }
    if (maxDate) {
      pickerOptions.end = new Date(maxDate)
    }

    uni.showDatePicker(pickerOptions)
  }
}

const showEducationEndDatePicker = (index) => {
  const edu = formData.education[index]
  showDatePicker({
    currentValue: edu.endDate,
    minDate: edu.startDate,
    maxDate: new Date(),
    onSuccess: (date) => {
      edu.endDate = date
    }
  })
}

const showWorkStartDatePicker = (index) => {
  const work = formData.work[index]
  showDatePicker({
    currentValue: work.startDate,
    maxDate: work.endDate || new Date(),
    onSuccess: (date) => {
      work.startDate = date
    }
  })
}

const showWorkEndDatePicker = (index) => {
  const work = formData.work[index]
  showDatePicker({
    currentValue: work.endDate,
    minDate: work.startDate,
    maxDate: new Date(),
    onSuccess: (date) => {
      work.endDate = date
    }
  })
}

const showProjectStartDatePicker = (index) => {
  const project = formData.project[index]
  showDatePicker({
    currentValue: project.startDate,
    maxDate: project.endDate || new Date(),
    onSuccess: (date) => {
      project.startDate = date
    }
  })
}

const showProjectEndDatePicker = (index) => {
  const project = formData.project[index]
  showDatePicker({
    currentValue: project.endDate,
    minDate: project.startDate,
    maxDate: new Date(),
    onSuccess: (date) => {
      project.endDate = date
    }
  })
}

// 格式化显示日期
const formatDisplayDate = (date) => {
  console.log('formatDisplayDate input:', date, 'type:', typeof date)
  if (!date) return ''
  if (typeof date === 'string') {
    // 如果是ISO格式的日期字符串，提取日期部分
    if (date.includes('T')) {
      const result = date.split('T')[0]
      console.log('formatDisplayDate ISO result:', result)
      return result
    }
    console.log('formatDisplayDate string result:', date)
    return date
  }
  if (date instanceof Date) {
    const result = date.toISOString().split('T')[0]
    console.log('formatDisplayDate Date result:', result)
    return result
  }
  console.log('formatDisplayDate fallback:', date)
  return String(date)
}

// 格式化picker组件使用的日期值
const formatPickerDate = (date) => {
  console.log('formatPickerDate input:', date, 'type:', typeof date)
  if (!date) return ''
  if (typeof date === 'string') {
    // 如果是ISO格式的日期字符串，提取日期部分
    if (date.includes('T')) {
      const result = date.split('T')[0]
      console.log('formatPickerDate ISO result:', result)
      return result
    }
    console.log('formatPickerDate string result:', date)
    return date
  }
  if (date instanceof Date) {
    const result = date.toISOString().split('T')[0]
    console.log('formatPickerDate Date result:', result)
    return result
  }
  console.log('formatPickerDate empty result for:', date)
  return ''
}

// 调试日期选择器函数
const testDatePickerDebug = () => {
  console.log('Debug button clicked!')
  uni.showToast({
    title: '调试按钮被点击',
    icon: 'success'
  })

  // 直接测试原生日期选择器
  if (typeof document !== 'undefined') {
    const input = document.createElement('input')
    input.type = 'date'
    input.style.position = 'absolute'
    input.style.left = '-9999px'
    input.style.opacity = '0'

    document.body.appendChild(input)

    input.addEventListener('change', function() {
      console.log('Debug date selected:', this.value)
      uni.showToast({
        title: '日期选择成功: ' + this.value,
        icon: 'success'
      })
      formData.birthday = this.value
      document.body.removeChild(this)
    })

    input.addEventListener('blur', function() {
      setTimeout(() => {
        if (document.body.contains(this)) {
          document.body.removeChild(this)
        }
      }, 100)
    })

    input.click()
    console.log('Debug date picker triggered')
  } else {
    console.log('Document not available, trying uni.showDatePicker')
    uni.showDatePicker({
      success: (res) => {
        console.log('Debug uni date selected:', res.date)
        formData.birthday = res.date
        uni.showToast({
          title: '日期选择成功: ' + res.date,
          icon: 'success'
        })
      }
    })
  }
}

// picker组件的日期选择处理
const onBirthdayChange = (e) => {
  console.log('Picker birthday change - full event:', e)
  const value = e.detail?.value || e.target?.value || e
  console.log('Extracted value:', value)
  formData.value.birthday = value

  // 强制触发响应式更新
  nextTick(() => {
    console.log('Birthday updated to:', formData.value.birthday)
    console.log('Display should show:', formatDisplayDate(formData.value.birthday))
  })
}

// 教育经历日期选择处理
const onEducationStartDateChange = (e, index) => {
  console.log('Education start date change - full event:', e, 'index:', index)
  const value = e.detail?.value || e.target?.value || e
  console.log('Extracted value:', value)
  if (formData.value.education && formData.value.education[index]) {
    formData.value.education[index].startDate = value
  }
}

const onEducationEndDateChange = (e, index) => {
  console.log('Education end date change - full event:', e, 'index:', index)
  const value = e.detail?.value || e.target?.value || e
  console.log('Extracted value:', value)
  if (formData.value.education && formData.value.education[index]) {
    formData.value.education[index].endDate = value
  }
}

// 工作经历日期选择处理
const onWorkStartDateChange = (e, index) => {
  console.log('Work start date change - full event:', e, 'index:', index)
  const value = e.detail?.value || e.target?.value || e
  console.log('Extracted value:', value)
  if (formData.value.work && formData.value.work[index]) {
    formData.value.work[index].startDate = value
  }
}

const onWorkEndDateChange = (e, index) => {
  console.log('Work end date change - full event:', e, 'index:', index)
  const value = e.detail?.value || e.target?.value || e
  console.log('Extracted value:', value)
  if (formData.value.work && formData.value.work[index]) {
    formData.value.work[index].endDate = value
  }
}

// 项目经历日期选择处理
const onProjectStartDateChange = (e, index) => {
  console.log('Project start date change - full event:', e, 'index:', index)
  const value = e.detail?.value || e.target?.value || e
  console.log('Extracted value:', value)
  if (formData.value.project && formData.value.project[index]) {
    formData.value.project[index].startDate = value
  }
}

const onProjectEndDateChange = (e, index) => {
  console.log('Project end date change - full event:', e, 'index:', index)
  const value = e.detail?.value || e.target?.value || e
  console.log('Extracted value:', value)
  if (formData.value.project && formData.value.project[index]) {
    formData.value.project[index].endDate = value
  }
}

// 切换日期选择器组件
const togglePickerComponent = () => {
  usePickerComponent.value = !usePickerComponent.value
  console.log('Switched to:', usePickerComponent.value ? 'picker component' : 'custom component')
  uni.showToast({
    title: usePickerComponent.value ? '已切换到picker组件' : '已切换到自定义组件',
    icon: 'success'
  })
}



// 保存简历
const handleSaveResume = async () => {
  try {
    // 验证所有表单
    const isValid = await validateAllForms();
    if (!isValid) {
      return;
    }
    
    // 验证是否有最高学历
    const hasHighestEducation = formData.value.education.some(edu => edu.isHighest);
    if (!hasHighestEducation && formData.value.education.length > 0) {
      // 如果没有设置最高学历，默认将第一条设为最高学历
      formData.value.education[0].isHighest = true;
    }
    
    // 确保所有教育经历的isHighest字段为布尔值
    if (formData.value.education && Array.isArray(formData.value.education)) {
      formData.value.education.forEach(edu => {
        if (typeof edu.isHighest === 'string') {
          edu.isHighest = edu.isHighest === 'true' || edu.isHighest === '1'
        } else if (typeof edu.isHighest === 'number') {
          edu.isHighest = edu.isHighest === 1
        } else if (edu.isHighest === null || edu.isHighest === undefined) {
          edu.isHighest = false
        }
      })
    }
    
    // 计算年龄（如果有出生日期）
    if (formData.value.birthday) {
      formData.value.age = calculateAge(formData.value.birthday);
    }
    
    // 确保简历标题有默认值
    if (!formData.value.resumeTitle) {
      formData.value.resumeTitle = '我的简历';
    }
    
    // 同步最高学历信息
    syncHighestEducation();
    
    const token = uni.getStorageSync('token')
    const userInfo = uni.getStorageSync('userinfo') // 获取用户信息
    const userId = userInfo ? userInfo.userId : null // 提取 userId

    const resumeData = {
      ...formData.value,
      userId // 将 userId 添加到简历数据中
    }
    
    const result = await saveResumeAPI(token, resumeData, resumeId.value)
    
    if (result && result.code === 200) {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
      
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: result?.message || '保存失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('保存简历失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}

// 如果需要重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    avatar: '',
    resumeTitle: '我的简历', // 保持默认标题
    name: '',
    gender: '',
    birthday: '',
    nation: '',
    nativePlace: '',
    politicalStatus: '',
    technicalPosition: '',
    phone: '',
    email: '',
    location: '',
    // 现任职务相关字段
    currentPosition: '',
    // 学历和毕业院校字段
    highestDegree: '',
    highestSchool: '',
    // 标记字段是否被手动修改过
    highestDegreeManuallySet: false,
    highestSchoolManuallySet: false,
    jobIntention: {
      jobType: '',
      expectedSalary: '',
      locations: [],
      status: ''
    },
    education: [],
    work: [],
    project: []
  })
}

// 工作经历验证规则
const workRules = {
  'work.0.company': {
    rules: [{ required: true, errorMessage: '请输入公司名称' }]
  },
  'work.0.position': {
    rules: [{ required: true, errorMessage: '请输入职位名称' }]
  },
  'work.0.startDate': {
    rules: [{ required: true, errorMessage: '请选择开始时间' }]
  },
  'work.0.endDate': {
    rules: [{ required: true, errorMessage: '请选择结束时间' }]
  }
}

// 项目经历验证规则
const projectRules = {
  'project.0.name': {
    rules: [{ required: true, errorMessage: '请输入项目名称' }]
  },
  'project.0.role': {
    rules: [{ required: true, errorMessage: '请输入担任角色' }]
  },
  'project.0.startDate': {
    rules: [{ required: true, errorMessage: '请选择开始时间' }]
  },
  'project.0.endDate': {
    rules: [{ required: true, errorMessage: '请选择结束时间' }]
  }
}

// 1. 添加教育经历验证规则
const educationRules = {
  'education.0.school': {
    rules: [{ required: true, errorMessage: '请输入学校名称' }]
  },
  'education.0.major': {
    rules: [{ required: true, errorMessage: '请输入专业名称' }]
  },
  'education.0.degree': {
    rules: [{ required: true, errorMessage: '请选择学历' }]
  },
  'education.0.startDate': {
    rules: [{ required: true, errorMessage: '请选择开始时间' }]
  },
  'education.0.endDate': {
    rules: [{ required: true, errorMessage: '请选择结束时间' }]
  }
}

// 添加学历选项数据
const degreeTypes = [
  { text: '博士', value: '博士' },
  { text: '硕士', value: '硕士' },
  { text: '本科', value: '本科' },
  { text: '大专', value: '大专' },
  { text: '高中', value: '高中' },
  { text: '初中及以下', value: '初中及以下' }
]

// 添加日期格式化函数
const formatDate = (date) => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 计算年龄
const calculateAge = (birthday) => {
  if (!birthday) return '未知';
  const birthDate = new Date(birthday);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
};

// 民族选项
const nationOptions = [
  { text: '汉族', value: '汉族' },
  { text: '蒙古族', value: '蒙古族' },
  { text: '回族', value: '回族' },
  { text: '藏族', value: '藏族' },
  { text: '维吾尔族', value: '维吾尔族' },
  { text: '苗族', value: '苗族' },
  { text: '彝族', value: '彝族' },
  { text: '壮族', value: '壮族' },
  { text: '布依族', value: '布依族' },
  { text: '朝鲜族', value: '朝鲜族' },
  { text: '满族', value: '满族' },
  { text: '侗族', value: '侗族' },
  { text: '瑶族', value: '瑶族' },
  { text: '白族', value: '白族' },
  { text: '土家族', value: '土家族' },
  { text: '哈尼族', value: '哈尼族' },
  { text: '哈萨克族', value: '哈萨克族' },
  { text: '傣族', value: '傣族' },
  { text: '黎族', value: '黎族' },
  { text: '傈僳族', value: '傈僳族' },
  { text: '佤族', value: '佤族' },
  { text: '畲族', value: '畲族' },
  { text: '高山族', value: '高山族' },
  { text: '拉祜族', value: '拉祜族' },
  { text: '水族', value: '水族' },
  { text: '东乡族', value: '东乡族' },
  { text: '纳西族', value: '纳西族' },
  { text: '景颇族', value: '景颇族' },
  { text: '柯尔克孜族', value: '柯尔克孜族' },
  { text: '土族', value: '土族' },
  { text: '达斡尔族', value: '达斡尔族' },
  { text: '仫佬族', value: '仫佬族' },
  { text: '羌族', value: '羌族' },
  { text: '布朗族', value: '布朗族' },
  { text: '撒拉族', value: '撒拉族' },
  { text: '毛南族', value: '毛南族' },
  { text: '仡佬族', value: '仡佬族' },
  { text: '锡伯族', value: '锡伯族' },
  { text: '阿昌族', value: '阿昌族' },
  { text: '普米族', value: '普米族' },
  { text: '塔吉克族', value: '塔吉克族' },
  { text: '怒族', value: '怒族' },
  { text: '乌孜别克族', value: '乌孜别克族' },
  { text: '俄罗斯族', value: '俄罗斯族' },
  { text: '鄂温克族', value: '鄂温克族' },
  { text: '德昂族', value: '德昂族' },
  { text: '保安族', value: '保安族' },
  { text: '裕固族', value: '裕固族' },
  { text: '京族', value: '京族' },
  { text: '塔塔尔族', value: '塔塔尔族' },
  { text: '独龙族', value: '独龙族' },
  { text: '鄂伦春族', value: '鄂伦春族' },
  { text: '赫哲族', value: '赫哲族' },
  { text: '门巴族', value: '门巴族' },
  { text: '珞巴族', value: '珞巴族' },
  { text: '基诺族', value: '基诺族' }
];

// 政治面貌选项
const politicalStatusOptions = [
  { text: '中共党员', value: '中共党员' },
  { text: '中共预备党员', value: '中共预备党员' },
  { text: '共青团员', value: '共青团员' },
  { text: '民革党员', value: '民革党员' },
  { text: '民盟盟员', value: '民盟盟员' },
  { text: '民建会员', value: '民建会员' },
  { text: '民进会员', value: '民进会员' },
  { text: '农工党党员', value: '农工党党员' },
  { text: '致公党党员', value: '致公党党员' },
  { text: '九三学社社员', value: '九三学社社员' },
  { text: '无党派人士', value: '无党派人士' },
  { text: '群众', value: '群众' }
];

// 性别选项数据
const genderTypes = [
  { text: '男', value: '男' },
  { text: '女', value: '女' }
];

// 家庭成员关系选项
const relationshipOptions = [
  { text: '父亲', value: '父亲' },
  { text: '母亲', value: '母亲' },
  { text: '配偶', value: '配偶' },
  { text: '子女', value: '子女' },
  { text: '兄弟姐妹', value: '兄弟姐妹' },
  { text: '其他', value: '其他' }
];

// 奖惩情况选项
const awardsOptions = [
  { text: '奖励', value: '奖励' },
  { text: '处分', value: '处分' },
  { text: '其他', value: '其他' }
];

// 年度考核选项
const annualReviewOptions = [
  { text: '优秀', value: '优秀' },
  { text: '称职', value: '称职' },
  { text: '基本称职', value: '基本称职' },
  { text: '不称职', value: '不称职' },
  { text: '其他', value: '其他' }
];
</script>

<style lang="scss" scoped>
.resume-edit {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 120px;
  position: relative;
}

/* 全局日期选择器弹出框样式覆盖 */
:deep(.uni-datetime-picker-popup) {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 999999 !important; /* 大幅提高z-index，确保在所有按钮之上 */
  width: 90% !important;
  max-width: 400px !important;
  background-color: #fff !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
  overflow: hidden !important;
  max-height: 70vh !important;
}

/* 全局日期选择器遮罩层 */
:deep(.uni-datetime-picker-mask) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 999998 !important; /* 提高遮罩层z-index，但低于弹出框 */
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* 防止页面滚动时弹出框跟随滚动 */
:deep(.uni-datetime-picker-popup-content) {
  position: relative !important;
  max-height: none !important;
  overflow: visible !important;
}

/* 日期选择器输入框样式 */
:deep(.uni-datetime-picker-timebox) {
  border: 1px solid #dcdfe6 !important;
  border-radius: 8px !important;
  background-color: #fff !important;
  height: 44px !important;
  line-height: 44px !important;
  padding: 0 12px !important;
  font-size: 14px !important;
  color: #333 !important;
  transition: all 0.3s ease !important;
}

:deep(.uni-datetime-picker-timebox:hover) {
  border-color: #409eff !important;
}

:deep(.uni-datetime-picker-timebox:focus) {
  border-color: #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1) !important;
}

/* 进度条样式 */
.progress-bar {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      top: 12px;
      right: -50%;
      width: 100%;
      height: 2px;
      background: #ddd;
      z-index: 0;
    }

    &.active::after {
      background: #409eff;
    }

    &.completed::after {
      background: #67c23a;
    }
  }

  .step-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ddd;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    margin-bottom: 8px;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
  }

  .step-item.active .step-number {
    background: #409eff;
    transform: scale(1.1);
  }

  .step-item.completed .step-number {
    background: #67c23a;
  }

  .step-title {
    font-size: 12px;
    color: #666;
    transition: color 0.3s ease;
  }

  .step-item.active .step-title {
    color: #409eff;
    font-weight: 500;
  }
}

/* 表单容器 */
.form-container {

  .form-section {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    /* 移除不必要的悬停动画效果 */
    /* 确保日期选择器弹出框不被遮挡 */
    overflow: visible !important;
    position: relative;
    /* 移除z-index，避免创建层叠上下文影响子元素 */
  }

  .section-header {
    margin-bottom: 24px;
    border-bottom: 1px solid #eee;
    padding-bottom: 16px;

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
    }

    .section-desc {
      font-size: 14px;
      color: #999;
    }
  }
}

/* 头像上传 */
.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fb;
  border-radius: 8px;
}

.avatar-label {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  align-self: flex-start;
}

.required-mark {
  color: #f56c6c;
  margin-left: 4px;
}

.avatar-upload {
  width: 120px;
  height: 150px; /* 调整为2寸证件照的比例 */
  border-radius: 0px; /* 完全方形，无圆角 */
  margin: 0 auto 10px;
  overflow: hidden;
  position: relative;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: #409eff;
    transform: scale(1.02);
    background-color: #f0f9ff;
  }

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0px; /* 图片也设为方形 */
  }

  .upload-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;

    .add-icon {
      font-size: 36px;
      font-weight: 300;
      color: #d9d9d9;
      margin-bottom: 8px;
      line-height: 1;
    }

    .upload-text {
      font-size: 12px;
      color: #999;
      text-align: center;
    }
  }
}

.avatar-tips {
  text-align: center;
  font-size: 12px;
  color: #909399;
  margin-bottom: 20px;
  padding: 0 20px;
  line-height: 1.4;
}

/* 表单项样式 */
:deep(.uni-forms-item) {
  margin-bottom: 24px;
  /* 确保日期选择器弹出框不被遮挡 */
  overflow: visible !important;
  position: relative;

  .uni-forms-item__label {
    font-size: 15px;
    color: #333;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .uni-easyinput__content,
  .uni-data-checklist {
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
    }

    &:focus-within {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

/* uni-forms 组件样式修复 */
:deep(.uni-forms) {
  overflow: visible !important;
  position: relative;
  /* 移除z-index，避免创建层叠上下文 */
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 16px 24px;
  background: #fff;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.05);
  z-index: 99;

  .prev-btn, .next-btn, .submit-btn {
    flex: 1;
    margin: 0 8px;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .prev-btn {
    background: #f5f7fa;
    color: #666;
    border: 1px solid #dcdfe6;

    &:hover {
      background: #ecf5ff;
      color: #409eff;
      border-color: #409eff;
    }
  }

  .next-btn, .submit-btn {
    background: #409eff;
    color: #fff;
    border: none;

    &:hover {
      background: #66b1ff;
      transform: translateY(-1px);
    }

    &:active {
      background: #3a8ee6;
      transform: translateY(0);
    }
  }
}

/* 添加按钮样式 */
.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 44px;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  color: #409eff;
  font-size: 15px;
  margin-top: 16px;
  transition: all 0.3s ease;
  background: #fff;
  /* 移除position和z-index，避免创建层叠上下文 */

  &:hover {
    border-color: #409eff;
    background: #ecf5ff;
  }

  .iconfont {
    margin-right: 6px;
    font-size: 18px;
  }
}

/* 删除按钮样式 */
.delete-btn {
  color: #f56c6c;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 4px;
  /* 移除position和z-index，避免创建层叠上下文 */

  &:hover {
    background: #fff1f1;
    color: #f78989;
  }
}

/* 教育经历项样式 */
.education-item {
  background: #f8f9fb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  /* 确保日期选择器弹出框不被遮挡 */
  overflow: visible !important;
  position: relative;
  /* 移除z-index，避免创建层叠上下文影响子元素 */

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .item-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
}

/* 日期选择器间隔 */
.date-separator {
  margin: 0 8px;
  color: #999;
}

/* 技能评分样式 */
.skills-container {
  .skill-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    background: #f8f9fb;
    padding: 16px;
    border-radius: 8px;

    .skill-input {
      flex: 1;
      margin-right: 16px;
    }

    :deep(.uni-rate) {
      margin: 0 16px;
    }
  }
}

/* 工作经历项样式 */
.work-item {
  background: #f8f9fb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  /* 移除不必要的悬停动画效果 */

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;

    .item-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;

  :deep(.uni-datetime-picker) {
    flex: 1;
  }

  .date-separator {
    color: #999;
    padding: 0 8px;
  }
}

/* 文本域样式 */
:deep(.uni-easyinput__content-textarea) {
  min-height: 100px;
  padding: 10px;
}

/* 删除按钮悬浮效果 */
.delete-btn {
  color: #f56c6c;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 4px;
  /* 移除position和z-index，避免创建层叠上下文 */

  &:hover {
    background: #fff1f1;
    color: #f78989;
  }
}

/* 项目经历项样式 */
.project-item {
  background: #f8f9fb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  /* 移除不必要的悬停动画效果 */

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;

    .item-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  /* 文本域样式优化 */
  :deep(.uni-easyinput__content-textarea) {
    min-height: 100px;
    padding: 12px;
    line-height: 1.6;
    background: #fff;
    border-radius: 4px;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

/* 日期选择器容器 */
.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 30px; /* 增加底部间距，防止弹出框被按钮遮挡 */
  /* 确保日期选择器弹出框不被遮挡 */
  overflow: visible !important;
  position: relative;
  /* 移除z-index，避免创建层叠上下文 */

  :deep(.uni-datetime-picker) {
    flex: 1;
    /* 确保日期选择器本身不被遮挡 */
    overflow: visible !important;
    position: relative;
    /* 移除z-index，避免创建层叠上下文 */

    /* 确保弹出框在顶层显示 */
    .uni-datetime-picker-popup {
      position: fixed !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      z-index: 9999 !important;
      width: 90% !important;
      max-width: 400px !important;
      background-color: #fff !important;
      border-radius: 12px !important;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
      overflow: hidden !important;
    }

    /* 弹出框遮罩层 */
    .uni-datetime-picker-mask {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      z-index: 9998 !important;
      background-color: rgba(0, 0, 0, 0.5) !important;
    }

    /* 弹出框内容区域 */
    .uni-datetime-picker-popup-content {
      max-height: 70vh !important;
      overflow-y: auto !important;
    }

    /* 弹出框头部 */
    .uni-datetime-picker-popup-header {
      padding: 16px !important;
      border-bottom: 1px solid #f0f0f0 !important;
      background-color: #fff !important;
    }

    /* 弹出框底部按钮 */
    .uni-datetime-picker-popup-footer {
      padding: 16px !important;
      border-top: 1px solid #f0f0f0 !important;
      background-color: #fff !important;
      display: flex !important;
      justify-content: space-between !important;
      gap: 12px !important;
    }

    /* 确认和取消按钮 */
    .uni-datetime-picker-btn {
      flex: 1 !important;
      height: 40px !important;
      border-radius: 8px !important;
      font-size: 16px !important;
      border: none !important;
      cursor: pointer !important;
    }

    .uni-datetime-picker-btn-confirm {
      background-color: #409eff !important;
      color: #fff !important;
    }

    .uni-datetime-picker-btn-cancel {
      background-color: #f5f7fa !important;
      color: #666 !important;
      border: 1px solid #dcdfe6 !important;
    }
  }

  .date-separator {
    color: #999;
    padding: 0 8px;
  }
}

/* 添加按钮样式 */
.add-btn {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  color: #409eff;
  font-size: 15px;
  transition: all 0.3s ease;
  margin-top: 40px; /* 增加顶部间距，避免遮挡日期选择器 */
  position: relative;
  z-index: 1; /* 确保按钮层级低于日期选择器弹出框 */

  &:hover {
    border-color: #409eff;
    background: #ecf5ff;
  }

  .iconfont {
    margin-right: 6px;
    font-size: 18px;
  }
}

/* 技能特长样式 */
.skills-container {
  .skill-item {
    background: #f8f9fb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    /* 移除不必要的悬停动画效果 */

    .skill-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #eee;

      .skill-index {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .level-container {
      display: flex;
      align-items: center;
      gap: 12px;

      .level-text {
        color: #666;
        font-size: 14px;
        min-width: 40px;
      }
    }

    /* 文本域样式 */
    :deep(.uni-easyinput__content-textarea) {
      min-height: 80px;
      padding: 12px;
      line-height: 1.6;
      background: #fff;
      border-radius: 4px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }
    }
  }
}

/* 删除按钮样式 */
.delete-btn {
  color: #f56c6c;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 4px;
  /* 移除position和z-index，避免创建层叠上下文 */

  &:hover {
    background: #fff1f1;
    color: #f78989;
  }
}

/* 添加按钮样式 */
.add-btn {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  color: #409eff;
  font-size: 15px;
  transition: all 0.3s ease;
  margin-top: 16px;
  /* 移除position和z-index，避免创建层叠上下文 */

  &:hover {
    border-color: #409eff;
    background: #ecf5ff;
  }

  .iconfont {
    margin-right: 6px;
    font-size: 18px;
  }
}

/* 星级评分样式优化 */
:deep(.uni-rate) {
  .uni-rate-icon {
    margin-right: 4px;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }
  }
}

/* 表单内容区域优化 */
.form-section {
  padding: 16px;
  background: #fff;
  border-radius: 12px;
  margin: 12px;

  .section-header {
    margin-bottom: 20px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .section-desc {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }
  }

  /* 表单项样式优化 */
  :deep(.uni-forms-item) {
    margin-bottom: 16px;
    
    .uni-forms-item__label {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
      padding: 0;
    }

    .uni-forms-item__content {
      padding: 0;
    }

    /* 输入框样式 */
    .uni-easyinput__content {
      min-height: 42px;
      border-radius: 8px;
      border: 1px solid #dcdfe6;
      background: #f5f7fa;

      .uni-easyinput__placeholder-class {
        font-size: 13px;
      }
    }

    /* 文本域样式 */
    .uni-easyinput__content-textarea {
      min-height: 80px;
      padding: 10px;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  /* 日期选择器样式 */
  .date-range {
    display: flex;
    flex-direction: column;
    gap: 8px;

    :deep(.uni-datetime-picker) {
      width: 100%;

      .uni-datetime-picker-timebox {
        border-radius: 8px;
        height: 42px;
      }
    }

    .date-separator {
      text-align: center;
      color: #999;
      font-size: 13px;
      padding: 4px 0;
    }
  }

  /* 单选/多选框样式 */
  :deep(.uni-data-checkbox) {
    .checkbox__inner {
      border-radius: 8px;
      padding: 8px 12px;
    }
  }

  /* 技能评分样式 */
  .level-container {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;

    :deep(.uni-rate) {
      transform: scale(0.9);
      transform-origin: left center;
    }

    .level-text {
      font-size: 13px;
      color: #666;
      min-width: 40px;
    }
  }
}

/* 工作/项目/技能条目样式 */
.work-item,
.project-item,
.skill-item {
  background: #f8f9fb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;

    .item-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    .delete-btn {
      font-size: 12px;
      color: #f56c6c;
      background: #fff1f1;
      padding: 4px 8px;
      border-radius: 4px;
      /* 移除position和z-index，避免创建层叠上下文 */
    }
  }
}

/* 添加按钮样式 */
.add-btn {
  width: 100%;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  color: #409eff;
  font-size: 14px;
  margin-top: 12px;
  /* 移除position和z-index，避免创建层叠上下文 */

  .iconfont {
    margin-right: 4px;
    font-size: 16px;
  }
}

/* 头像上传样式 - 已移除重复定义 */

.purpose-item {
  margin-top: 20px;  /* 增加与头像的间距 */
  margin-bottom: 20px;  /* 增加与下方表单项的间距 */
}

/* 移除之前的 padding */
.purpose-item :deep(.uni-forms-item__content) {
  padding: 0;  /* 确保内容区域没有额外的内边距 */
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.section-desc {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

/* 自定义日期选择器样式 */
.custom-date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  min-height: 44px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.custom-date-picker:active {
  background: #e9ecef;
  transform: scale(0.98);
}

/* 紧凑模式的日期选择器 */
.custom-date-picker.compact {
  padding: 8px 12px;
  min-height: 36px;
  max-width: 160px;
  width: 160px;
  font-size: 14px;
  flex-shrink: 0;
}

.date-text {
  flex: 1;
  font-size: 16px;
  color: #333;
  line-height: 1.4;
}

.custom-date-picker.compact .date-text {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.date-text.placeholder {
  color: #999;
}

.custom-date-picker .uni-icons {
  margin-left: 8px;
  opacity: 0.6;
}

.custom-date-picker.compact .uni-icons {
  margin-left: 6px;
}

/* 出生日期容器 */
.birthday-container {
  display: block;
  width: 100%;
  max-width: 300px;
  min-width: 180px;

  /* PC端优化 */
  @media (min-width: 768px) {
    max-width: 350px;
    min-width: 220px;
  }
}

/* 强制紧凑模式不超出容器 */
.birthday-container .custom-date-picker.compact {
  width: 100%;
  box-sizing: border-box;
  padding: 8px 12px;
  min-height: 36px;
  font-size: 14px;
  text-align: left;

  /* PC端优化 */
  @media (min-width: 768px) {
    padding: 10px 14px;
    min-height: 40px;
    font-size: 15px;
  }
}

/* 紧凑模式下的文本处理 */
.birthday-container .custom-date-picker.compact .date-text {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  flex: 1;
  text-align: left;

  /* PC端优化 */
  @media (min-width: 768px) {
    font-size: 15px;
  }
}

/* 所有表单项中的日期选择器容器 */
.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 30px; /* 增加底部间距，防止弹出框被按钮遮挡 */

  :deep(.uni-datetime-picker) {
    flex: 1;
  }

  .date-separator {
    color: #999;
    padding: 0 8px;
  }
}

/* 添加按钮样式 */
.add-btn {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  color: #409eff;
  font-size: 15px;
  transition: all 0.3s ease;
  margin-top: 40px; /* 增加顶部间距，避免遮挡日期选择器 */
  position: relative;
  z-index: 1; /* 确保按钮层级低于日期选择器弹出框 */

  &:hover {
    border-color: #409eff;
    background: #ecf5ff;
  }

  .iconfont {
    margin-right: 6px;
    font-size: 18px;
  }
}
</style> 