<template>
  <text
    class="roc-icon-plus"
    :class="[faType, faName, faRotate, faAnimationType]"
    :style="{
      fontSize: String(size).includes('px') ? size : `${size}px`,
      color: color,
    }"
  ></text>
</template>

<script>
/**
 * roc-icon-plus
 * @description 本组件提供 fontawesome6.5.1 图标
 * @doc https://doc.rocyuan.top/roc-icon-plus.html
 *
 * @property {String}           type             类型fas，far，fab 默认 fas
 * @property {String}           name             名字fa-xxx，去掉fa-，只传 xxx
 * @property {Number | String}  size             大小 单位px （默认 16）
 * @property {String}           color            颜色 （默认 #606266）
 * @property {Number | String}  rotate           旋转，仅支持 90 180 270
 * @property {Boolean}          animationType    动画类型 - spin(平滑旋转) pulse(非平滑旋转) beat(心跳) fade(渐隐渐显) beat-fade(心跳+渐隐渐显) bounce(弹起) flip(翻转) shake(抖动)
 */
export default {
  name: 'roc-icon-plus',
  data() {
    return {}
  },
  computed: {
    faType() {
      return `${this.type}`
    },
    faName() {
      return `fa-${this.name}`
    },
    faRotate() {
      return `fa-rotate-${this.rotate}`
    },
    faAnimationType() {
      return `fa-${this.animationType}`
    },
  },
  props: {
    type: {
      type: String,
      default: 'fas',
    },
    name: {
      type: String,
      default: '',
      required: true,
    },
    size: {
      type: [Number, String],
      default: 16,
    },
    color: {
      type: String,
      default: '#606266',
    },
    rotate: {
      type: [Number, String],
      default: 0,
    },
    animationType: {
      type: String,
      default: '',
    },
  },
}
</script>

<style lang="scss" scoped>
/* #ifdef APP-PLUS || H5 */
$fa-font-path: 'styles/fonts';
/* #endif */
/* #ifndef APP-PLUS || H5 */
$fa-font-path: '//cdn.bootcdn.net/ajax/libs/font-awesome/6.5.1/webfonts';
/* #endif */
@import 'styles/scss/fontawesome.scss';
@import 'styles/scss/brands.scss';
@import 'styles/scss/solid.scss';
@import 'styles/scss/regular.scss';
</style>
