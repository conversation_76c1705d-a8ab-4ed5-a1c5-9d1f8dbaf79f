package com.ruoyi.system.service.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileTypeUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.system.domain.Resume;
import com.ruoyi.system.domain.ResumeEducation;
import com.ruoyi.system.domain.ResumeWork;
import com.ruoyi.system.domain.ResumeProject;
import com.ruoyi.system.domain.ResumeSkill;
import com.ruoyi.system.domain.ResumeDelivery;
import com.ruoyi.system.domain.TalentMessage;
import com.ruoyi.system.domain.ResumeAttachment;
import com.ruoyi.system.domain.Job;
import com.ruoyi.system.mapper.ResumeMapper;
import com.ruoyi.system.mapper.ResumeDeliveryMapper;
import com.ruoyi.system.mapper.TalentMessageMapper;
import com.ruoyi.system.mapper.JobMapper;
import com.ruoyi.system.service.IResumeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.beans.factory.annotation.Value;
import com.ruoyi.common.utils.DateUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import com.ruoyi.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.talent.service.ICompanyAdminService;
import com.ruoyi.system.mapper.ResumeFamilyMapper;
import com.ruoyi.system.mapper.ResumeAwardsMapper;
import com.ruoyi.system.mapper.ResumePositionMapper;
import com.ruoyi.system.domain.ResumeFamily;
import com.ruoyi.system.domain.ResumeAwards;
import com.ruoyi.system.domain.ResumePosition;

import java.util.List;
import java.io.File;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;

@Service
public class ResumeServiceImpl implements IResumeService {

    private static final Logger logger = LoggerFactory.getLogger(ResumeServiceImpl.class);

    @Autowired
    private ResumeMapper resumeMapper;

    @Autowired
    private ResumeDeliveryMapper deliveryMapper;

    @Autowired
    private TalentMessageMapper messageMapper;

    @Autowired
    private JobMapper jobMapper;

    @Autowired
    private ICompanyAdminService companyAdminService;

    @Autowired
    private ResumeFamilyMapper resumeFamilyMapper;

    @Autowired
    private ResumeAwardsMapper resumeAwardsMapper;

    @Autowired
    private ResumePositionMapper resumePositionMapper;

    @Value("${ruoyi.profile}")
    private String uploadPath;

    @Override
    public List<Resume> selectResumeList(Resume resume) {
        return resumeMapper.selectResumeList(resume);
    }

    @Override
    public Resume selectResumeById(String id) {
        Resume resume = resumeMapper.selectResumeById(id);
        if (resume == null) {
            throw new ServiceException("简历不存在");
        }

        // 查询关联数据
        resume.setEducation(resumeMapper.selectEducationByResumeId(id));
        resume.setWork(resumeMapper.selectWorkByResumeId(id));
        resume.setProject(resumeMapper.selectProjectByResumeId(id));
        resume.setSkills(resumeMapper.selectSkillByResumeId(id));

        return resume;
    }

    @Override
    public Resume selectDefaultResume(String userId) {
        return resumeMapper.selectDefaultResume(userId);
    }

    @Override
    @Transactional
    public int insertResume(Resume resume) {
        // 验证必要字段
        if (StringUtils.isEmpty(resume.getUserId())) {
            throw new ServiceException("用户ID不能为空");
        }
        if (StringUtils.isEmpty(resume.getResumeTitle())) {
            throw new ServiceException("简历标题不能为空");
        }
        
        // 设置UUID
        resume.setId(UUID.fastUUID().toString());

        // 如果设置为默认简历，清除其他默认状态
        if (Boolean.TRUE.equals(resume.getIsDefault())) {
            resumeMapper.clearDefaultStatus(resume.getUserId());
        }

        // 插入主表数据
        int rows = resumeMapper.insertResume(resume);

        // 插入关联数据
        if (resume.getEducation() != null && !resume.getEducation().isEmpty()) {
            for (ResumeEducation education : resume.getEducation()) {
                education.setId(UUID.fastUUID().toString());
                education.setResumeId(resume.getId());
                resumeMapper.insertEducation(education);
            }
        }

        // 插入工作经历
        if (resume.getWork() != null && !resume.getWork().isEmpty()) {
            for (ResumeWork work : resume.getWork()) {
                work.setId(UUID.fastUUID().toString());
                work.setResumeId(resume.getId());
                resumeMapper.insertWork(work);
            }
        }

        // 插入项目经历
        if (resume.getProject() != null && !resume.getProject().isEmpty()) {
            for (ResumeProject project : resume.getProject()) {
                project.setId(UUID.fastUUID().toString());
                project.setResumeId(resume.getId());
                resumeMapper.insertProject(project);
            }
        }

        // 插入技能
        if (resume.getSkills() != null && !resume.getSkills().isEmpty()) {
            for (ResumeSkill skill : resume.getSkills()) {
                skill.setId(UUID.fastUUID().toString());
                skill.setResumeId(resume.getId());
                resumeMapper.insertSkill(skill);
            }
        }

        return rows;
    }

    @Override
    @Transactional
    public int updateResume(Resume resume) {
        if (resume == null || StringUtils.isEmpty(resume.getId())) {
            throw new ServiceException("参数错误");
        }

        Resume dbResume = resumeMapper.selectResumeById(resume.getId());
        if (dbResume == null) {
            throw new ServiceException("简历不存在");
        }

        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        String currentUserIdStr = currentUserId.toString();

        // 权限检查：管理员或简历所有者可以修改
        boolean hasPermission = false;

        // 1. 检查是否为系统管理员
        if (SecurityUtils.isAdmin(currentUserId)) {
            hasPermission = true;
        }
        // 2. 检查是否为简历所有者
        else if (dbResume.getUserId() != null && dbResume.getUserId().equals(currentUserIdStr)) {
            hasPermission = true;
        }
        // 3. 检查是否有管理员角色
        else if (SecurityUtils.hasRole("admin") || SecurityUtils.hasRole("yiji_admin")) {
            hasPermission = true;
        }

        if (!hasPermission) {
            throw new ServiceException("无权修改此简历");
        }

        // 设置更新人ID
        resume.setUserId(currentUserIdStr);

        // 更新主表
        int rows = resumeMapper.updateResume(resume);

        // 更新教育经历
        if (resume.getEducation() != null) {
            // 先删除旧数据
            resumeMapper.deleteEducationByResumeId(resume.getId());
            // 插入新数据
            for (ResumeEducation education : resume.getEducation()) {
                education.setId(UUID.fastUUID().toString());
                education.setResumeId(resume.getId());
                resumeMapper.insertEducation(education);
            }
        }

        // 更新工作经历
        if (resume.getWork() != null) {
            resumeMapper.deleteWorkByResumeId(resume.getId());
            for (ResumeWork work : resume.getWork()) {
                work.setId(UUID.fastUUID().toString());
                work.setResumeId(resume.getId());
                resumeMapper.insertWork(work);
            }
        }

        // 更新项目经历
        if (resume.getProject() != null) {
            resumeMapper.deleteProjectByResumeId(resume.getId());
            for (ResumeProject project : resume.getProject()) {
                project.setId(UUID.fastUUID().toString());
                project.setResumeId(resume.getId());
                resumeMapper.insertProject(project);
            }
        }

        // 更新技能
        if (resume.getSkills() != null) {
            resumeMapper.deleteSkillByResumeId(resume.getId());
            for (ResumeSkill skill : resume.getSkills()) {
                skill.setId(UUID.fastUUID().toString());
                skill.setResumeId(resume.getId());
                resumeMapper.insertSkill(skill);
            }
        }

        return rows;
    }

    @Override
    @Transactional
    public int deleteResumeById(String id) {
        Resume resume = resumeMapper.selectResumeById(id);
        if (resume == null) {
            throw new ServiceException("简历不存在");
        }

        // 软删除简历，不删除投递记录，保持数据完整性
        return resumeMapper.deleteResumeById(id);
    }

    @Override
    @Transactional
    public int setDefaultResume(String id, String userId) {
        Resume resume = resumeMapper.selectResumeById(id);
        if (resume == null) {
            throw new ServiceException("简历不存在");
        }

        if (!resume.getUserId().equals(userId)) {
            throw new ServiceException("无权操作此简历");
        }

        resumeMapper.clearDefaultStatus(userId);
        return resumeMapper.setDefaultStatus(id);
    }

    @Override
    public String uploadAttachment(String resumeId, String fileName, byte[] fileContent) {
        // TODO: 实现文件上传逻辑
        return null;
    }

    @Override
    @Transactional
    public ResumeDelivery applyJob(String resumeId, String jobId) {
        logger.info("开始处理简历投递请求: resumeId={}, jobId={}", resumeId, jobId);
        
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        logger.info("当前用户ID: {}", userId);

        // 检查是否已经投递过（排除已取消的投递）
        ResumeDelivery existDelivery = deliveryMapper.selectDeliveryByJobAndUser(Long.parseLong(jobId), userId);
        if (existDelivery != null && !"-1".equals(existDelivery.getStatus())) {
            logger.warn("用户已投递过该职位: userId={}, jobId={}, status={}", userId, jobId, existDelivery.getStatus());
            ResumeDelivery errorDelivery = new ResumeDelivery();
            errorDelivery.setStatus("error");
            errorDelivery.setRemark("您已经投递过该职位");
            return errorDelivery;
        }

        // 检查简历是否存在且属于当前用户
        Resume resume = resumeMapper.selectResumeById(resumeId);
        if (resume == null || !resume.getUserId().equals(userId.toString())) {
            logger.warn("简历不存在或不属于当前用户: resumeId={}, userId={}", resumeId, userId);
            ResumeDelivery errorDelivery = new ResumeDelivery();
            errorDelivery.setStatus("error");
            errorDelivery.setRemark("简历不存在或无权使用");
            return errorDelivery;
        }

        logger.info("创建投递记录...");
        // 创建投递记录
        ResumeDelivery delivery = new ResumeDelivery();
        delivery.setId(IdUtils.simpleUUID());
        delivery.setResumeId(resumeId);
        delivery.setJobId(Long.parseLong(jobId));
        delivery.setUserId(userId);
        delivery.setStatus("0"); // 待处理状态

        int deliveryResult = deliveryMapper.insertDelivery(delivery);
        logger.info("投递记录创建结果: {}", deliveryResult);
        
        // 更新职位投递数量
        try {
            logger.info("开始更新职位投递数量: jobId={}", jobId);
            Long jobIdLong = Long.parseLong(jobId);
            int updateResult = jobMapper.updateJobApplications(jobIdLong);
            logger.info("职位投递数量更新结果: {}", updateResult);
            
            if (updateResult <= 0) {
                logger.error("职位投递数量更新失败: jobId={}", jobId);
            }
        } catch (Exception e) {
            logger.error("更新职位投递数量时发生异常: jobId={}, error={}", jobId, e.getMessage(), e);
            throw e;
        }

        // 创建系统消息
        logger.info("创建系统消息...");
        TalentMessage message = new TalentMessage();
        message.setId(IdUtils.simpleUUID());
        message.setUserId(userId);
        
        // 获取职位信息，包括公司名称和职位标题
        Job job = jobMapper.selectJobById(Long.parseLong(jobId));
        String jobTitle = job != null ? job.getTitle() : "未知职位";
        String companyName = job != null ? job.getCompany() : "未知企业";
        
        message.setTitle("简历投递成功");
        message.setContent("您的简历已成功投递至【" + companyName + "】的【" + jobTitle + "】职位，请耐心等待企业反馈。");
        message.setType("RESUME");
        message.setStatus("UNREAD");
        message.setRelatedId(delivery.getId());

        int messageResult = messageMapper.insertMessage(message);

        logger.info("简历投递处理完成: deliveryId={}", delivery.getId());
        return delivery;
    }

    @Override
    public ResumeDelivery getJobApplyStatus(String jobId, String userId) {
        return deliveryMapper.selectDeliveryByJobAndUser(Long.parseLong(jobId), Long.parseLong(userId));
    }

    @Override
    public String uploadAvatar(byte[] fileContent, String fileName) throws Exception {
        // 构建年月日目录
        String datePath = DateUtils.datePath();
        // 构建文件保存路径
        String path = uploadPath + "/avatar/" + datePath;

        // 生成新文件名
        String extension = FilenameUtils.getExtension(fileName);
        String newFileName = IdUtils.fastUUID() + "." + extension;

        File file = new File(path, newFileName);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }

        // 写入文件
        FileUtils.writeByteArrayToFile(file, fileContent);

        // 返回访问路径
        return "/profile/avatar/" + datePath + "/" + newFileName;
    }

    @Override
    public int getTotalDeliveryCount() {
        return deliveryMapper.countAllDeliveries();
    }

    @Override
    public int getTodayDeliveryCount() {
        return deliveryMapper.countTodayDeliveries();
    }

    @Override
    public List<ResumeDelivery> selectDeliveryList(ResumeDelivery delivery) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        
        // 如果不是管理员，检查是否是企业管理员
        if (!SecurityUtils.isAdmin(userId)) {
            // 获取企业管理员所管理的企业ID
            Long companyId = companyAdminService.getAdminCompanyId(userId);
            if (companyId != null) {
                // 如果是企业管理员，只能查看自己企业的投递记录
                delivery.setCompanyId(companyId);
            }
        }
        return deliveryMapper.selectDeliveryList(delivery);
    }

    @Override
    public ResumeDelivery selectDeliveryById(String id) {
        return deliveryMapper.selectDeliveryById(id);
    }

    @Override
    public int getTotalDeliveryCount(String userId) {
        return deliveryMapper.countUserDeliveries(userId);
    }

    @Override
    public int getInterviewInviteCount(String userId) {
        return deliveryMapper.countUserInterviewInvites(userId);
    }

    @Override
    public int countTotalResumes() {
        Integer count = resumeMapper.countTotalResumes();
        return count != null ? count : 0;
    }

    @Override
    public String uploadWordResume(byte[] fileContent, String fileName, String userId) throws Exception {
        // 实现Word简历上传逻辑
        return null;
    }
    
    /**
     * 检查简历是否投递到指定企业
     * 
     * @param resumeId 简历ID
     * @param companyId 企业ID
     * @return 是否已投递
     */
    @Override
    public boolean checkResumeDeliveredToCompany(String resumeId, Long companyId) {
        // 查询该简历是否投递到了该企业的任何职位
        return deliveryMapper.checkResumeDeliveredToCompany(resumeId, companyId) > 0;
    }

    @Override
    public List<ResumeFamily> getFamilyMembers(String resumeId) {
        return resumeFamilyMapper.selectByResumeId(resumeId);
        }

    @Override
    public ResumeAwards getAwards(String resumeId) {
        return resumeAwardsMapper.selectByResumeId(resumeId);
    }

    @Override
    public ResumePosition getPosition(String resumeId) {
        return resumePositionMapper.selectByResumeId(resumeId);
    }

    @Override
    @Transactional
    public void updateFamilyMembers(String resumeId, List<ResumeFamily> familyMembers) {
        // 先删除旧数据
        resumeFamilyMapper.deleteByResumeId(resumeId);
        
        // 插入新数据
        if (familyMembers != null && !familyMembers.isEmpty()) {
            for (ResumeFamily family : familyMembers) {
                family.setId(UUID.fastUUID().toString());
                family.setResumeId(resumeId);
                resumeFamilyMapper.insert(family);
            }
        }
    }

    @Override
    @Transactional
    public void updateAwards(String resumeId, ResumeAwards awards) {
        ResumeAwards existingAwards = resumeAwardsMapper.selectByResumeId(resumeId);
        
        if (existingAwards != null) {
            // 更新现有记录
            awards.setId(existingAwards.getId());
            awards.setResumeId(resumeId);
            resumeAwardsMapper.update(awards);
        } else {
            // 插入新记录
            awards.setId(UUID.fastUUID().toString());
            awards.setResumeId(resumeId);
            resumeAwardsMapper.insert(awards);
        }
    }

    @Override
    @Transactional
    public void updatePosition(String resumeId, ResumePosition position) {
        ResumePosition existingPosition = resumePositionMapper.selectByResumeId(resumeId);

        if (existingPosition != null) {
            // 更新现有记录
            position.setId(existingPosition.getId());
            position.setResumeId(resumeId);
            resumePositionMapper.update(position);
        } else {
            // 插入新记录
            position.setId(UUID.fastUUID().toString());
            position.setResumeId(resumeId);
            resumePositionMapper.insert(position);
        }
    }

    @Override
    public List<Map<String, Object>> getRecentDeliveries(int limit) {
        return deliveryMapper.selectRecentDeliveries(limit);
    }
}