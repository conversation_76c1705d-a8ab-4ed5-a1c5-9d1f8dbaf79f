<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="消息类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择消息类型" clearable>
          <el-option label="系统通知" value="SYSTEM" />
          <el-option label="简历状态" value="RESUME" />
          <el-option label="面试通知" value="INTERVIEW" />
        </el-select>
      </el-form-item>
      <el-form-item label="消息状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择消息状态" clearable>
          <el-option label="未读" value="UNREAD" />
          <el-option label="已读" value="READ" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="Check" :disabled="multiple || isAdmin" @click="handleReadAll">全部标记为已读</el-button>
      </el-col>
      <right-toolbar v-model="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 消息列表 -->
    <el-table v-loading="loading" :data="messageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :selectable="row => !isAdmin" />
      <el-table-column label="消息类型" align="center" prop="type" width="100">
        <template #default="scope">
          <el-tag :type="getMessageTypeTag(scope.row.type)">
            {{ getMessageTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="投递人" align="center" prop="userName" width="120" />
      <el-table-column label="标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="内容" align="center" prop="content" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'UNREAD' ? 'warning' : 'success'">
            {{ scope.row.status === 'UNREAD' ? '未读' : '已读' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发送时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            type="text"
            icon="Check"
            v-if="scope.row.status === 'UNREAD' && !isAdmin"
            @click="handleRead(scope.row)"
          >标记已读</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 消息详情对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="消息类型" :span="2">
          <el-tag :type="getMessageTypeTag(form.type)">
            {{ getMessageTypeText(form.type) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="标题" :span="2">{{ form.title }}</el-descriptions-item>
        <el-descriptions-item label="内容" :span="2">{{ form.content }}</el-descriptions-item>
        <el-descriptions-item label="发送时间">{{ parseTime(form.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="form.status === 'UNREAD' ? 'warning' : 'success'">
            {{ form.status === 'UNREAD' ? '未读' : '已读' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button 
            type="primary" 
            @click="handleRead(form)" 
            v-if="form.status === 'UNREAD' && !isAdmin"
          >标记已读</el-button>
          <el-button @click="open = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { listMessage, readMessage, readAllMessage } from "@/api/talent/message";
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import auth from '@/plugins/auth';

const isAdmin = ref(auth.hasRole('admin'));

const loading = ref(true);
const total = ref(0);
const open = ref(false);
const title = ref("");
const showSearch = ref(true);
const messageList = ref([]);
const multiple = ref(true);
const ids = ref([]);
const router = useRouter();

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  type: undefined,
  status: undefined
});

const form = reactive({
  id: undefined,
  title: undefined,
  content: undefined,
  type: undefined,
  status: undefined,
  createTime: undefined,
  relatedId: undefined
});

/** 查询消息列表 */
const getList = async () => {
  loading.value = true;
  try {
    const response = await listMessage(queryParams);
    // 后端直接返回数组，我们需要手动计算总数
    messageList.value = response.data || [];
    total.value = messageList.value.length;
  } catch (error) {
    console.error("获取消息列表失败:", error);
  }
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.type = undefined;
  queryParams.status = undefined;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  multiple.value = !selection.length;
};

/** 查看按钮操作 */
const handleView = async (row) => {
  try {
    Object.assign(form, row);
    open.value = true;
    title.value = "查看消息详情";
    
    // 如果是未读消息，自动标记为已读
    // if (row.status === 'UNREAD') {
    //   await handleRead(row);
    // }
  } catch (error) {
    console.error("获取消息详情失败:", error);
  }
};

/** 标记已读按钮操作 */
const handleRead = async (row) => {
  try {
    await readMessage(row.id);
    ElMessage.success("已标记为已读");
    // 更新列表中的状态
    // const index = messageList.value.findIndex(item => item.id === row.id);
    // if (index !== -1) {
    //   messageList.value[index].status = 'READ';
    // }
    // // 如果是在详情对话框中，更新表单状态
    // if (form.id === row.id) {
    //   form.status = 'READ';
    // }
  } catch (error) {
    console.error("标记已读失败:", error);
    ElMessage.error("标记已读失败");
  }
};

/** 全部标记已读按钮操作 */
const handleReadAll = async () => {
  try {
    await readAllMessage();
    ElMessage.success("已全部标记为已读");
    // 更新列表中所有消息的状态
    messageList.value.forEach(item => {
      item.status = 'READ';
    });
  } catch (error) {
    console.error("标记全部已读失败:", error);
    ElMessage.error("标记全部已读失败");
  }
};

/** 获取消息类型标签样式 */
const getMessageTypeTag = (type) => {
  const typeMap = {
    'SYSTEM': 'info',
    'RESUME': 'primary',
    'INTERVIEW': 'success'
  };
  return typeMap[type] || 'info';
};

/** 获取消息类型文本 */
const getMessageTypeText = (type) => {
  const typeMap = {
    'SYSTEM': '系统通知',
    'RESUME': '简历状态',
    'INTERVIEW': '面试通知'
  };
  return typeMap[type] || type;
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
