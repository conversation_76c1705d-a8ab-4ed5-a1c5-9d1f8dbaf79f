<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ResumePositionMapper">
    
    <resultMap type="ResumePosition" id="PositionResult">
        <id     property="id"          column="id"          />
        <result property="resumeId"    column="resume_id"   />
        <result property="currentPosition" column="current_position" />
        <result property="proposedPosition" column="proposed_position" />
        <result property="proposedRemoval" column="proposed_removal" />
        <result property="createTime"  column="created_at"  />
        <result property="updateTime"  column="updated_at"  />
    </resultMap>

    <select id="selectByResumeId" parameterType="String" resultMap="PositionResult">
        select * from resume_position 
        where resume_id = #{resumeId}
        limit 1
    </select>

    <insert id="insert" parameterType="ResumePosition">
        insert into resume_position (
            id, resume_id, current_position, proposed_position, proposed_removal, created_at
        ) values (
            #{id}, #{resumeId}, #{currentPosition}, #{proposedPosition}, #{proposedRemoval}, sysdate()
        )
    </insert>

    <update id="update" parameterType="ResumePosition">
        update resume_position
        <set>
            <if test="currentPosition != null">current_position = #{currentPosition},</if>
            <if test="proposedPosition != null">proposed_position = #{proposedPosition},</if>
            <if test="proposedRemoval != null">proposed_removal = #{proposedRemoval},</if>
            updated_at = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="String">
        delete from resume_position where id = #{id}
    </delete>

    <delete id="deleteByResumeId" parameterType="String">
        delete from resume_position where resume_id = #{resumeId}
    </delete>
</mapper> 