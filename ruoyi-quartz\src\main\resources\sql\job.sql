-- 创建职位表
create table talent_job (
    id              bigint(20)      not null auto_increment    comment '职位ID',
    company_id      bigint(20)      not null                   comment '公司ID',
    title           varchar(100)    not null                   comment '职位标题',
    salary          varchar(50)     default ''                 comment '薪资范围',
    company         varchar(100)    not null                   comment '公司名称',
    company_logo    varchar(255)    default ''                 comment '公司Logo',
    company_desc    varchar(500)    default ''                 comment '公司描述',
    location        varchar(100)    default ''                 comment '工作地点',
    experience      varchar(50)     default ''                 comment '工作经验',
    education       varchar(50)     default ''                 comment '学历要求',
    description     text                                       comment '职位描述',
    requirements    text                                       comment '岗位要求',
    views           int(11)         default 0                  comment '浏览量',
    applications    int(11)         default 0                  comment '投递数量',
    tags            varchar(255)    default ''                 comment '职位标签',
    status          char(1)         default '0'                comment '状态（0正常 1停用）',
    create_by       varchar(64)     default ''                 comment '创建者',
    create_time     datetime                                   comment '创建时间',
    update_by       varchar(64)     default ''                 comment '更新者',
    update_time     datetime                                   comment '更新时间',
    department      varchar(100)    default ''                 comment '部门',
    other           varchar(500)    default ''                 comment '其他信息',
    headcount       int(11)         default 1                  comment '所需人数',
    target_range    varchar(100)    default ''                 comment '目标范围',
    demand_type     varchar(50)     default ''                 comment '需求形式',
    delivery_count  int(11)         default 0                  comment '投递数量',
    primary key (id)
) engine=innodb auto_increment=100 comment = '职位表'; 