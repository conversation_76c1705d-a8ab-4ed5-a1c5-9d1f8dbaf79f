package com.ruoyi.system.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import java.util.Date;

@Data
public class TalentMessage extends BaseEntity {
    private String id;
    private Long userId;        // 接收者ID
    private String userName;    // 接收者名称
    private Long senderId;      // 发送者ID
    private String senderName;  // 发送者名称
    private String title;
    private String content;
    private String type;  // INTERVIEW-面试通知, RESUME-简历状态, SYSTEM-系统通知
    private String status;  // UNREAD-未读, READ-已读
    private String relatedId;  // 改为String类型，用于存储UUID
    private Date readTime;
} 