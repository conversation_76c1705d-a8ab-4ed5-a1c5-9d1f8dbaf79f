package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.Company;

public interface ICompanyService {
    /**
     * 查询公司列表
     * 
     * @param company 公司信息
     * @return 公司集合
     */
    public List<Company> selectCompanyList(Company company);

    /**
     * 查询公司信息
     * 
     * @param id 公司ID
     * @return 公司信息
     */
    public Company selectCompanyById(Long id);

    /**
     * 新增公司
     * 
     * @param company 公司信息
     * @return 结果
     */
    public int insertCompany(Company company);

    /**
     * 修改公司
     * 
     * @param company 公司信息
     * @return 结果
     */
    public int updateCompany(Company company);

    /**
     * 删除公司信息
     * 
     * @param id 公司ID
     * @return 结果
     */
    public int deleteCompanyById(Long id);

    /**
     * 批量删除公司
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteCompanyByIds(String[] ids);

    /**
     * 获取公司总数
     * 
     * @return 公司总数
     */
    public int countTotalCompanies();
} 