<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JobViewMapper">
    
    <resultMap type="JobView" id="JobViewResult">
        <id     property="id"        column="id"         />
        <result property="jobId"     column="job_id"     />
        <result property="userId"    column="user_id"    />
        <result property="ipAddress" column="ip_address" />
        <result property="userAgent" column="user_agent" />
        <result property="viewTime"  column="view_time"  />
    </resultMap>
    
    <insert id="insertJobView" parameterType="JobView">
        insert into talent_job_view (
            job_id,
            user_id,
            ip_address,
            user_agent
        ) values (
            #{jobId},
            #{userId},
            #{ipAddress},
            #{userAgent}
        )
    </insert>
    
    <select id="selectJobViewList" parameterType="JobView" resultMap="JobViewResult">
        select * from talent_job_view
        <where>
            <if test="jobId != null">
                AND job_id = #{jobId}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
        </where>
        order by view_time desc
    </select>
    
    <select id="selectViewCountByJobId" parameterType="Long" resultType="Integer">
        select count(*) from talent_job_view where job_id = #{jobId}
    </select>
</mapper> 