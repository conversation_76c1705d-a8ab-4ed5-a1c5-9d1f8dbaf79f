<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talent.mapper.CompanyAdminMapper">
    
    <resultMap type="CompanyAdmin" id="CompanyAdminResult">
        <result property="companyId"    column="company_id"    />
        <result property="userId"       column="user_id"       />
        <result property="userName"     column="user_name"     />
        <result property="phonenumber"  column="phonenumber"   />
        <result property="email"        column="email"         />
        <result property="createBy"     column="create_by"     />
        <result property="createTime"   column="create_time"   />
    </resultMap>

    <sql id="selectCompanyAdminVo">
        select ca.company_id, ca.user_id, u.user_name, u.phonenumber, u.email, ca.create_by, ca.create_time
        from talent_company_admin ca
        inner join sys_user u on u.user_id = ca.user_id
        where u.del_flag = '0'
    </sql>

    <select id="selectCompanyAdminList" parameterType="Long" resultMap="CompanyAdminResult">
        <include refid="selectCompanyAdminVo"/>
        and ca.company_id = #{companyId}
    </select>

    <insert id="batchInsertCompanyAdmin">
        insert into talent_company_admin(company_id, user_id, create_by, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.companyId}, #{item.userId}, #{item.createBy}, sysdate())
        </foreach>
    </insert>

    <delete id="deleteCompanyAdmin">
        delete from talent_company_admin
        where company_id = #{companyId} and user_id = #{userId}
    </delete>

    <select id="checkIsCompanyAdmin" resultType="Integer">
        select count(1) from talent_company_admin
        where company_id = #{companyId} and user_id = #{userId}
    </select>

    <select id="selectAdminCompanyId" resultType="Long">
        select company_id from talent_company_admin
        where user_id = #{userId}
    </select>

</mapper> 