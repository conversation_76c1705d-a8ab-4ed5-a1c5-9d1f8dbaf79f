<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ResumeMapper">
    
    <resultMap type="Resume" id="ResumeResult">
        <id     property="id"          column="id"           />
        <result property="userId"      column="user_id"      />
        <result property="resumeTitle" column="resume_title" />
        <result property="name"        column="name"         />
        <result property="avatar"      column="avatar"       />
        <result property="gender"      column="gender"       />
        <result property="birthday"    column="birthday"     />
        <result property="phone"       column="phone"        />
        <result property="email"       column="email"        />
        <result property="location"    column="location"     />
        <result property="isDefault"   column="is_default"   />
        <result property="completeness" column="completeness" />
        <result property="views"       column="views"        />
        <result property="status"      column="status"       />
        <result property="createTime"  column="created_at"   />
        <result property="updateTime"  column="updated_at"   />
        
        <!-- 新增字段映射 -->
        <result property="age"         column="age"          />
        <result property="nation"      column="nation"       />
        <result property="nativePlace" column="native_place" />
        <result property="politicalStatus" column="political_status" />
        <result property="technicalPosition" column="technical_position" />
        <result property="currentPosition" column="current_position" />
        
        <!-- 教育经历集合 -->
        <collection property="education" ofType="ResumeEducation">
            <id     property="id"          column="edu_id"          />
            <result property="resumeId"    column="resume_id"       />
            <result property="school"      column="school"          />
            <result property="major"       column="major"           />
            <result property="degree"      column="degree"          />
            <result property="startDate"   column="edu_start_date"  />
            <result property="endDate"     column="edu_end_date"    />
            <result property="description" column="edu_description" />
            <result property="isHighest"   column="is_highest"      />
        </collection>
        
        <!-- 工作经历集合 -->
        <collection property="work" ofType="ResumeWork">
            <id     property="id"          column="work_id"         />
            <result property="resumeId"    column="resume_id"       />
            <result property="company"     column="company"         />
            <result property="position"    column="position"        />
            <result property="department"  column="department"      />
            <result property="startDate"   column="work_start_date" />
            <result property="endDate"     column="work_end_date"   />
            <result property="description" column="work_description"/>
        </collection>
        
        <!-- 项目经历集合 -->
        <collection property="project" ofType="ResumeProject">
            <id     property="id"          column="proj_id"         />
            <result property="resumeId"    column="resume_id"       />
            <result property="name"        column="proj_name"       />
            <result property="role"        column="role"            />
            <result property="startDate"   column="proj_start_date" />
            <result property="endDate"     column="proj_end_date"   />
            <result property="description" column="proj_description"/>
            <result property="achievement" column="achievement"     />
        </collection>
        
        <!-- 技能集合 -->
        <collection property="skills" ofType="ResumeSkill">
            <id     property="id"          column="skill_id"        />
            <result property="resumeId"    column="resume_id"       />
            <result property="name"        column="skill_name"      />
            <result property="level"       column="level"           />
            <result property="description" column="skill_description"/>
        </collection>
    </resultMap>

    <insert id="insertResume" parameterType="Resume">
        insert into resume (
            id, user_id, resume_title, name, avatar, gender, birthday, phone, email,
            location, is_default, completeness, views, status,
            age, nation, native_place, political_status, technical_position, current_position,
            created_at, updated_at
        ) values (
            #{id}, #{userId}, #{resumeTitle}, #{name}, #{avatar}, #{gender}, #{birthday}, #{phone}, #{email},
            #{location}, #{isDefault}, #{completeness}, #{views}, #{status},
            #{age}, #{nation}, #{nativePlace}, #{politicalStatus}, #{technicalPosition}, #{currentPosition},
            sysdate(), sysdate()
        )
    </insert>

    <select id="selectResumeList" parameterType="Resume" resultMap="ResumeResult">
        select r.id, r.user_id, r.resume_title, r.name, r.avatar, r.gender, 
               r.birthday, r.phone, r.email, r.location, r.is_default, 
               r.completeness, r.views, r.status, r.created_at, r.updated_at,
               r.age, r.nation, r.native_place, r.political_status, r.technical_position, r.current_position
        from resume r
        <where>
            AND r.status != 'deleted'
            <if test="userId != null and userId != ''">
                AND r.user_id = #{userId}
            </if>
            <if test="resumeTitle != null and resumeTitle != ''">
                AND r.resume_title like concat('%', #{resumeTitle}, '%')
            </if>
            <if test="name != null and name != ''">
                AND r.name like concat('%', #{name}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND r.phone like concat('%', #{phone}, '%')
            </if>
            <if test="email != null and email != ''">
                AND r.email like concat('%', #{email}, '%')
            </if>
            <if test="status != null and status != ''">
                AND r.status = #{status}
            </if>
            <if test="nation != null and nation != ''">
                AND r.nation = #{nation}
            </if>
            <if test="politicalStatus != null and politicalStatus != ''">
                AND r.political_status = #{politicalStatus}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND date_format(r.created_at,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND date_format(r.created_at,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by r.created_at desc
    </select>

    <select id="selectResumeById" parameterType="String" resultType="Resume">
        SELECT id, user_id AS userId, resume_title AS resumeTitle, 
               name, avatar, gender, birthday, phone, email,
               location, is_default AS isDefault, completeness, views, status,
               age, nation, native_place AS nativePlace, 
               political_status AS politicalStatus, technical_position AS technicalPosition,
               current_position AS currentPosition,
               created_at AS createTime, updated_at AS updateTime
        FROM resume 
        WHERE id = #{id}
    </select>

    <select id="selectEducationByResumeId" parameterType="String" resultType="ResumeEducation">
        select id, resume_id as resumeId, school, major, degree, 
               start_date as startDate, end_date as endDate, description,
               is_highest as isHighest
        from resume_education 
        where resume_id = #{resumeId}
        order by start_date desc
    </select>

    <select id="selectWorkByResumeId" parameterType="String" resultType="ResumeWork">
        select id, resume_id as resumeId, company, position, department,
               start_date as startDate, end_date as endDate, description
        from resume_work 
        where resume_id = #{resumeId}
        order by start_date desc
    </select>

    <select id="selectProjectByResumeId" parameterType="String" resultType="ResumeProject">
        select id, resume_id as resumeId, name, role, 
               start_date as startDate, end_date as endDate, 
               description, achievement
        from resume_project 
        where resume_id = #{resumeId}
        order by start_date desc
    </select>

    <select id="selectSkillByResumeId" parameterType="String" resultType="ResumeSkill">
        select id, resume_id as resumeId, name, level, description
        from resume_skill 
        where resume_id = #{resumeId}
    </select>

    <select id="selectDefaultResume" parameterType="String" resultMap="ResumeResult">
        select r.*, rd.* 
        from resume r
        left join resume_detail rd on r.id = rd.resume_id
        where r.user_id = #{userId} and r.is_default = true
        limit 1
    </select>

    <update id="updateResume" parameterType="Resume">
        update resume
        <set>
            <if test="resumeTitle != null">resume_title = #{resumeTitle},</if>
            <if test="name != null">name = #{name},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="location != null">location = #{location},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="status != null">status = #{status},</if>
            <if test="completeness != null">completeness = #{completeness},</if>
            <if test="age != null">age = #{age},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="nativePlace != null">native_place = #{nativePlace},</if>
            <if test="politicalStatus != null">political_status = #{politicalStatus},</if>
            <if test="technicalPosition != null">technical_position = #{technicalPosition},</if>
            <if test="currentPosition != null">current_position = #{currentPosition},</if>
            updated_at = sysdate()
        </set>
        where id = #{id}
    </update>

    <update id="deleteResumeById" parameterType="String">
        update resume set status = 'deleted', updated_at = sysdate() where id = #{id}
    </update>

    <update id="clearDefaultStatus" parameterType="String">
        update resume set is_default = false 
        where user_id = #{userId}
    </update>

    <update id="setDefaultStatus" parameterType="String">
        update resume set is_default = true 
        where id = #{id}
    </update>

    <update id="updateViews" parameterType="String">
        update resume set views = views + 1 
        where id = #{id}
    </update>

    <!-- 插入简历附件 -->
    <insert id="insertAttachment" parameterType="ResumeAttachment">
        insert into resume_attachment (
            id, resume_id, file_name, file_url, file_size,
            file_type, created_at
        ) values (
            #{id}, #{resumeId}, #{fileName}, #{fileUrl}, #{fileSize},
            #{fileType}, #{createdAt}
        )
    </insert>

    <select id="selectDeliveryByJobAndUser" resultType="JobDelivery">
        SELECT * FROM job_delivery 
        WHERE job_id = #{jobId} AND user_id = #{userId}
    </select>

    <!-- 插入教育经历 -->
    <insert id="insertEducation" parameterType="ResumeEducation">
        insert into resume_education (
            id, resume_id, school, major, degree, start_date, end_date, description, is_highest
        ) values (
            #{id}, #{resumeId}, #{school}, #{major}, #{degree}, #{startDate}, #{endDate}, #{description}, #{isHighest}
        )
    </insert>

    <!-- 插入工作经历 -->
    <insert id="insertWork" parameterType="ResumeWork">
        insert into resume_work (
            id, resume_id, company, position, department, start_date, end_date, description
        ) values (
            #{id}, #{resumeId}, #{company}, #{position}, #{department}, #{startDate}, #{endDate}, #{description}
        )
    </insert>

    <!-- 插入项目经历 -->
    <insert id="insertProject" parameterType="ResumeProject">
        insert into resume_project (
            id, resume_id, name, role, start_date, end_date, description, achievement
        ) values (
            #{id}, #{resumeId}, #{name}, #{role}, #{startDate}, #{endDate}, #{description}, #{achievement}
        )
    </insert>

    <!-- 插入技能 -->
    <insert id="insertSkill" parameterType="ResumeSkill">
        insert into resume_skill (
            id, resume_id, name, level, description
        ) values (
            #{id}, #{resumeId}, #{name}, #{level}, #{description}
        )
    </insert>

    <!-- 删除教育经历 -->
    <delete id="deleteEducationByResumeId" parameterType="String">
        delete from resume_education where resume_id = #{resumeId}
    </delete>

    <!-- 删除工作经历 -->
    <delete id="deleteWorkByResumeId" parameterType="String">
        delete from resume_work where resume_id = #{resumeId}
    </delete>

    <!-- 删除项目经历 -->
    <delete id="deleteProjectByResumeId" parameterType="String">
        delete from resume_project where resume_id = #{resumeId}
    </delete>

    <!-- 删除技能 -->
    <delete id="deleteSkillByResumeId" parameterType="String">
        delete from resume_skill where resume_id = #{resumeId}
    </delete>

    <!-- 统计简历总数 -->
    <select id="countTotalResumes" resultType="int">
        select count(*) from resume where status = '0'
    </select>

</mapper> 