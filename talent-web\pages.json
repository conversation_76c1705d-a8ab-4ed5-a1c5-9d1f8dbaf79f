{
    "pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
        {
            "path": "pages/talent-market/index",
            "style": {
                "navigationBarTitleText": "人才市场",
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/talent-market/message",
            "style": {
                "navigationBarTitleText": "消息"
            }
        },
        {
            "path": "pages/talent-market/my",
            "style": {
                "navigationBarTitleText": "我的"
            }
        },
        {
            "path": "pages/talent-market/detail",
            "style": {
                "navigationBarTitleText": "职位详情"
            }
        },
        {
            "path": "pages/index/index",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "中国水务企微平台"
            }
        },
        {
            "path": "pages/search/search",
            "style": {
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/humanresources/humanresources",
            "style": {
                "navigationBarTitleText": "人力资源驾驶舱(测试)"
            }
        },
        {
            "path": "pages/finance/finance",
            "style": {
                "navigationBarTitleText": "财务数据驾驶舱(测试)"
            }
        },
        {
            "path": "pages/security/security",
            "style": {
                "navigationBarTitleText": "安全生产驾驶舱(测试)"
            }
        },
        {
            "path": "pages/investment/investment",
            "style": {
                "navigationBarTitleText": "投资管理驾驶舱(测试)"
            }
        },
        {
            "path": "pages/salary/salary",
            "style": {
                "navigationBarTitleText": "工资查询(测试)"
            }
        },
        {
            "path": "pages/holiday/holiday",
            "style": {
                "navigationBarTitleText": "假期查询(测试)"
            }
        },
        {
            "path": "pages/training/training",
            "style": {
                "navigationBarTitleText": "培训学时(测试展示)"
            }
        },
        {
            "path": "pages/permissions/permissions",
            "style": {
                "navigationBarTitleText": "权限查询(测试)"
            }
        },
        {
            "path": "pages/login/login",
            "style": {
                "navigationBarTitleText": "授权登陆"
            }
        },
        {
            "path": "pages/nopermissions/nopermissions",
            "style": {
                "navigationStyle": "custom", // 取消本页面的导航栏
                "navigationBarTitleText": "",
                "app-plus": {
                    "animationType": "fade-in", // 设置fade-in淡入动画，为最合理的动画类型
                    "background": "transparent", // 背景透明
                    "backgroundColor": "rgba(0,0,0,0)", // 背景透明
                    "popGesture": "none", // 关闭IOS屏幕左边滑动关闭当前页面的功能
                    "titleNView": { //  隐藏当前页面的返回按钮
                        "titleSize": "18px",
                        "autoBackButton": false // 禁用原生导航栏
                    }
                }
            }
        },
        {
            "path": "pages/talent-market/resume-edit",
            "style": {
                "navigationBarTitleText": "简历编辑"
            }
        },
        {
            "path": "pages/talent-market/delivery-record",
            "style": {
                "navigationBarTitleText": "投递记录",
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/talent-market/talent-list",
            "style": {
                "navigationBarTitleText": "职位列表"
            }
        },
		{
		  "path": "pages/talent-market/resume-fav",
		  "style": {
		    "navigationBarTitleText": "简历收藏"
		  }
		}
    ],
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarTitleText": "人才市场",
        "navigationBarBackgroundColor": "#F8F8F8",
        "backgroundColor": "#F8F8F8"
    },
    "uniIdRouter": {},
    "tabBar": {
        "color": "#999999",
        "selectedColor": "#007AFF",
        "backgroundColor": "#ffffff",
        "borderStyle": "black",
        "list": [{
                "pagePath": "pages/talent-market/index",
                "text": "首页",
                "iconPath": "static/tabbar/home.png",
                "selectedIconPath": "static/tabbar/home-active.png"
            },
            {
                "pagePath": "pages/talent-market/message",
                "text": "消息",
                "iconPath": "static/tabbar/message.png",
                "selectedIconPath": "static/tabbar/message-active.png"
            },
            {
                "pagePath": "pages/talent-market/my",
                "text": "我的",
                "iconPath": "static/tabbar/my.png",
                "selectedIconPath": "static/tabbar/my-active.png"
            }
        ]
    }
}