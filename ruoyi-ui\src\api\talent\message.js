import request from '@/utils/request'

// 查询消息列表
export function listMessage(query) {
    return request({
        url: '/talent/message/list',
        method: 'get',
        params: query
    })
}

// 标记消息为已读
export function readMessage(id) {
    return request({
        url: '/talent/message/read/' + id,
        method: 'put'
    })
}

// 标记所有消息为已读
export function readAllMessage() {
    return request({
        url: '/talent/message/read/all',
        method: 'put'
    })
}

// 发送消息
export function sendMessage(data) {
    return request({
        url: '/talent/message/send',
        method: 'post',
        data: data
    })
}