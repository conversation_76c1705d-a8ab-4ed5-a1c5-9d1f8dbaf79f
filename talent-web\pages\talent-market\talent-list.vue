<template>
  <div class="talent-market">

    <!-- 职位搜索栏 -->
    <section class="search-section" :class="{ 'fixed': isSearchFixed }">
      <div class="search-bar">
        <input 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索职位名称、单位等"
          @keyup.enter="handleSearch"
        >
        <button @click="handleSearch" size="mini">搜索</button>
      </div>
     
    </section>

    <!-- 占位元素，用于固定定位时的空间补偿 -->
    <div v-if="isSearchFixed" class="search-placeholder"></div>

    <!-- 职位列表 -->
    <section class="job-list">
      <div v-if="jobs.length === 0" class="no-data">
        暂无职位信息
      </div>
      <div 
        v-else
        v-for="job in jobs" 
        :key="job.id" 
        class="job-card"
        @click="viewJobDetail(job.id)">
        <div class="job-header">
          <h3 class="job-title">{{ job.title }}</h3>
        </div>
        <!-- 职位基本信息区域 -->
        <div class="job-info-section">
          <!-- 部门信息 -->
          <div class="department-info" v-if="job.companyName || job.department">
            <uni-icons type="location" size="14" color="#409eff"></uni-icons>
            <span class="department-name">{{ formatDepartmentInfo(job) }}</span>
          </div>

          <!-- 职位要求信息 -->
          <div class="requirements-section">
            <div class="requirements-grid">
              <div class="requirement-item location" v-if="job.location">
                <div class="requirement-icon">
                  <uni-icons type="map-pin-ellipse" size="14" color="#67c23a"></uni-icons>
                </div>
                <span class="requirement-text">{{ job.location }}</span>
              </div>
              <div class="requirement-item experience" v-if="job.experience">
                <div class="requirement-icon">
                  <uni-icons type="calendar" size="14" color="#e6a23c"></uni-icons>
                </div>
                <span class="requirement-text">{{ job.experience }}</span>
              </div>
              <div class="requirement-item education" v-if="job.education">
                <div class="requirement-icon">
                  <uni-icons type="staff" size="14" color="#f56c6c"></uni-icons>
                </div>
                <span class="requirement-text">{{ job.education }}</span>
              </div>
            </div>
          </div>

          <!-- 招聘标签 -->
          <div class="recruitment-tags" v-if="job.demandType || job.company">
            <span class="recruitment-tag" :class="'type-' + job.demandType" v-if="job.demandType">
              <uni-icons type="flag" size="12"></uni-icons>
              {{ job.demandType }}
            </span>
            <span class="recruitment-tag company-tag" v-if="job.company">
              <uni-icons type="home-filled" size="12" color="#2c3e50"></uni-icons>
              {{ job.company }}
            </span>
          </div>
        </div>

        <!-- 职位统计信息区域 -->
        <div class="job-stats-section">
          <div class="stats-left">
            <div class="publish-time">
              <uni-icons type="clock" size="12" color="#909399"></uni-icons>
              <span>{{ formatTime(job.createTime) }}</span>
            </div>
          </div>
          <div class="stats-right">
            <span class="stat-item">
              <uni-icons type="eye" size="12" color="#409eff"></uni-icons>
              {{ job.views || 0 }}浏览
            </span>
            <span class="stat-item">
              <uni-icons type="person" size="12" color="#67c23a"></uni-icons>
              {{ job.applications || 0 }}投递
            </span>
          </div>
        </div>
      </div>
      <!-- 添加加载状态提示 -->
      <div v-if="isLoading" class="loading-more">
        <uni-icons type="spinner-cycle" size="20" color="#409EFF"></uni-icons>
        <span>加载中...</span>
      </div>
      <div v-if="!hasMore && jobs.length > 0" class="no-more">
        没有更多数据了
      </div>
    </section>

  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, onUnmounted } from 'vue'
import { getJobList, getJobDashboard, getCompanyList, applyJob, getDefaultResume, getUnreadMessageCount } from '@/utils/talent-market'
import {getUserInfo} from "@/utils/wx.js";
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'

const {
	proxy,
	appContext: {
		config: {
			globalProperties: global
		}
	}
} = getCurrentInstance();
// 响应式数据
const currentDate = ref(new Date().toLocaleDateString('zh-CN'))
const totalJobs = ref(0)
const todayNewJobs = ref(0)
const todayApplications = ref(0)
const totalDeliveries = ref(0)
const totalViews = ref(0)
const searchKeyword = ref('')
const jobs = ref([])
const companies = ref([])
const selectedCompanyId = ref(null)

// 筛选标签
const filterTags = ref([
  { id: 1, name: '招聘单位', active: true }
])

// 默认简历数据
const defaultResume = ref(null)

// 未读消息数
const unreadCount = ref(1)

// 添加分页相关变量
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const isLoading = ref(false)

// 搜索相关变量
const searchParams = ref({
  keyword: '',
  pageNum: 1,
  pageSize: 10
})

// 添加搜索框固定状态
const isSearchFixed = ref(false)

// 添加招聘单位弹窗引用
const companyPopup = ref(null)

// 添加滚动监听
const handleScroll = () => {
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  isSearchFixed.value = scrollTop > 200 // 当滚动超过200px时固定搜索框
}

// 获取职位列表
const fetchJobs = async () => {
  try {
    const token = uni.getStorageSync('token')
    if (!token) return

    // 获取URL参数
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const companyId = currentPage.$page?.options?.companyId
    const urlKeyword = currentPage.$page?.options?.keyword
    
    // 如果URL中有keyword参数，则使用它更新搜索框
    if (urlKeyword) {
      searchKeyword.value = decodeURIComponent(urlKeyword)
    }

    const result = await getJobList(token, {
      companyId: companyId,
      keyword: searchKeyword.value,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    })
    
    if (result && result.code === 200) {
      if (pageNum.value === 1) {
        jobs.value = result.rows || []
      } else {
        jobs.value = [...jobs.value, ...(result.rows || [])]
      }
      hasMore.value = result.total > jobs.value.length
      
      // 如果没有搜索结果，显示提示
      if (pageNum.value === 1 && jobs.value.length === 0) {
        uni.showToast({
          title: '暂无搜索结果',
          icon: 'none'
        })
      }
    }
  } catch (error) {
    console.error('获取职位列表失败:', error)
    uni.showToast({
      title: '获取职位列表失败',
      icon: 'none'
    })
  }
}

// 获取单位列表
const fetchCompanies = async () => {
  try {
    const token = uni.getStorageSync('token')
    if (!token) return

    const result = await getCompanyList(token)
    if (result && result.code === 200) {
      companies.value = result.rows || []
      // 如果有单位数据，默认选中第一个
      if (companies.value.length > 0) {
        selectedCompanyId.value = companies.value[0].id
        await fetchJobs()
      }
    }
  } catch (error) {
    console.error('获取单位列表失败:', error)
    uni.showToast({
      title: '获取单位列表失败',
      icon: 'none'
    })
  }
}

// 修改初始化方法
const initData = async () => {
  try {
    const token = uni.getStorageSync('token')
    if (!token) return

    // 获取统计信息
    const dashboardResult = await getJobDashboard(token)
    if (dashboardResult && dashboardResult.data) {
      const data = dashboardResult.data
      totalJobs.value = data.totalJobs || 0
      todayNewJobs.value = data.todayNewJobs || 0
      totalDeliveries.value = data.totalDeliveries || 0
      todayApplications.value = data.todayApplications || 0
      totalViews.value = data.totalViews || 0
    }

    // 获取职位列表
    await fetchJobs()

    // 获取其他数据
    await Promise.all([
      fetchDefaultResume(),
      fetchUnreadMessageCount()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

// 修改搜索方法
const handleSearch = async () => {
  try {
    // 重置分页参数
    pageNum.value = 1
    hasMore.value = true
    
    const token = uni.getStorageSync('token')
    if (!token) return
    
    // 获取URL参数中的companyId
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const companyId = currentPage.$page?.options?.companyId
    
    const result = await getJobList(token, {
      keyword: searchKeyword.value,
      companyId: companyId,
      pageNum: 1,
      pageSize: pageSize.value
    })
    
    if (result && result.code === 200) {
      jobs.value = result.rows || []
      hasMore.value = result.total > jobs.value.length
      
      // 如果没有搜索结果，显示提示
      if (jobs.value.length === 0) {
        uni.showToast({
          title: '暂无搜索结果',
          icon: 'none'
        })
      }
    }
  } catch (error) {
    console.error('搜索失败:', error)
    uni.showToast({
      title: '搜索失败',
      icon: 'error'
    })
  }
}

// 修改加载更多方法
const handleLoadMore = async () => {
  if (!hasMore.value || isLoading.value) return
  
  isLoading.value = true
  
  try {
    const token = uni.getStorageSync('token')
    if (!token) return
    
    // 获取URL参数中的companyId
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const companyId = currentPage.$page?.options?.companyId
    
    const result = await getJobList(token, {
      keyword: searchKeyword.value,
      companyId: companyId,
      pageNum: pageNum.value + 1,
      pageSize: pageSize.value
    })
    
    if (result && result.code === 200) {
      const newJobs = result.rows || []
      
      if (newJobs.length > 0) {
        jobs.value = [...jobs.value, ...newJobs]
        pageNum.value++
        hasMore.value = result.total > jobs.value.length
      } else {
        hasMore.value = false
        uni.showToast({
          title: '没有更多数据了',
          icon: 'none'
        })
      }
    }
  } catch (error) {
    console.error('加载更多失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

// 修改下拉刷新方法
const handleRefresh = async () => {
  try {
    const token = uni.getStorageSync('token')
    if (!token) {
      uni.stopPullDownRefresh()
      return
    }

    // 重置分页状态
    pageNum.value = 1
    hasMore.value = true
    
    // 获取统计信息
    const dashboardResult = await getJobDashboard(token)
    if (dashboardResult && dashboardResult.data) {
      const data = dashboardResult.data
      totalJobs.value = data.totalJobs || 0
      todayNewJobs.value = data.todayNewJobs || 0
      totalDeliveries.value = data.totalDeliveries || 0
      todayApplications.value = data.todayApplications || 0
      totalViews.value = data.totalViews || 0
    }

    // 刷新单位列表
    await fetchCompanies()
    
    // 获取其他数据
    await Promise.all([
      fetchDefaultResume(),
      fetchUnreadMessageCount()
    ])
    
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('刷新失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  } finally {
    uni.stopPullDownRefresh()
  }
}

// 获取默认简历
const fetchDefaultResume = async () => {
  try {
    const token = uni.getStorageSync('token')
    const result = await getDefaultResume(token)
    if (result && result.data) {
      defaultResume.value = result.data
    }
  } catch (error) {
    console.error('获取默认简历失败:', error)
  }
}

// 跳转到简历页面
const navigateToResume = () => {
  uni.navigateTo({
    url: '/pages/talent-market/resume'
  })
}

// 跳转到消息页面
const navigateToMessage = () => {
  uni.navigateTo({
    url: '/pages/talent-market/message'
  })
}

// 修改重置搜索方法
const resetSearch = () => {
  searchKeyword.value = ''
  handleSearch()
}

// 修改筛选标签点击事件
const toggleFilter = (tag) => {
  if (tag.id === 1) { // 招聘单位标签
    showCompanyPopup()
  } else {
    filterTags.value.forEach(t => t.active = t.id === tag.id)
  }
}

// 显示招聘单位弹窗
const showCompanyPopup = () => {
  companyPopup.value.open()
}

// 关闭招聘单位弹窗
const closeCompanyPopup = () => {
  companyPopup.value.close()
}

// 选择招聘单位
const selectCompany = async (company) => {
  selectedCompanyId.value = company.id
  closeCompanyPopup()
  // 重置分页并加载该公司的职位
  pageNum.value = 1
  hasMore.value = true
  await fetchJobs()
}

const viewJobDetail = (jobId) => {
  uni.navigateTo({
    url: `/pages/talent-market/detail?id=${jobId}`
  })
}

const handleApply = async (jobId) => {
  try {
    await applyForJob(jobId)
    uni.showToast({
      title: '投递成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '投递失败',
      icon: 'error'
    })
  }
}

// 获取未读消息数
const fetchUnreadMessageCount = async () => {
  try {
    const token = uni.getStorageSync('token')
    const result = await getUnreadMessageCount(token)
    if (result && result.code === 200) {
      unreadCount.value = result.data
    }
  } catch (error) {
    console.error('获取未读消息数失败:', error)
  }
}

// 格式化部门信息
const formatDepartmentInfo = (job) => {
  const parts = []

  // 如果有具体的公司名称（三四级单位），优先显示
  if (job.companyName && job.companyName.trim()) {
    parts.push(job.companyName.trim())
  }

  // 如果有部门信息，添加部门
  if (job.department && job.department.trim()) {
    parts.push(job.department.trim())
  }

  // 如果都没有，返回空字符串
  if (parts.length === 0) {
    return ''
  }

  // 用 " · " 连接各部分
  return parts.join(' · ')
}

// 添加时间格式化函数
const formatTime = (timestamp) => {
  if (!timestamp) return '';
  
  const now = new Date();
  const date = new Date(timestamp);
  const diff = now - date;
  
  // 小于1小时，显示"xx分钟前"
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `${minutes}分钟前`;
  }
  
  // 小于24小时，显示"xx小时前"
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  }
  
  // 小于7天，显示"xx天前"
  if (diff < *********) {
    const days = Math.floor(diff / 86400000);
    return `${days}天前`;
  }
  
  // 超过7天，显示具体日期
  return date.toLocaleDateString('zh-CN');
}

// 使用生命周期函数
onPullDownRefresh(() => {
  handleRefresh()
})

// 生命周期
onMounted(async () => {
  try {
    const { proxy } = getCurrentInstance()
    // 等待应用初始化完成
    await proxy.$onLaunched

    const token = uni.getStorageSync('token')
    if (!token) {
      console.log('未检测到token，跳转到登录页')
      // 记录当前页面路径，登录后可以跳回来
      const currentPage = encodeURIComponent(window.location.href);
      uni.setStorageSync('redirectPage', currentPage);
      
      // 跳转到登录页
      uni.redirectTo({
        url: '/pages/login/login',
        success: () => {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          })
        }
      })
      return
    }

    // 检查URL参数中是否有搜索关键词
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const urlKeyword = currentPage.$page?.options?.keyword
    
    if (urlKeyword) {
      searchKeyword.value = decodeURIComponent(urlKeyword)
      console.log('从URL获取搜索关键词:', searchKeyword.value)
    }

    // 检查并获取用户信息
    const userInfo = uni.getStorageSync('userinfo')
    if (!userInfo) {
      console.log("获取用户信息中...")
      try {
        const res = await getUserInfo(token)
        if (res.data) {
          uni.setStorageSync('userinfo', res.data)
          console.log("用户信息获取成功:", res.data)
        } else {
          console.warn("获取用户信息返回数据为空")
        }
      } catch (error) {
        console.error("获取用户信息失败:", error)
      }
    }

    // 添加滚动监听
    window.addEventListener('scroll', handleScroll)
    
    // 继续原有的数据获取逻辑
    await initData()

  } catch (error) {
    console.error('初始化数据失败:', error)
    // 如果是token过期导致的错误，也跳转到登录页
    if (error.code === 401 || error.message?.includes('token')) {
      // 记录当前页面路径
      const currentPage = encodeURIComponent(window.location.href);
      uni.setStorageSync('redirectPage', currentPage);
      
      uni.redirectTo({
        url: '/pages/login/login',
        success: () => {
          uni.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          })
        }
      })
      return
    }
    
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
})

// 添加上拉加载更多事件监听
onReachBottom(() => {
  handleLoadMore()
})

// 在组件卸载时移除滚动监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.talent-market {
  padding: 10px;
  background: linear-gradient(180deg, #f5f7fa 0%, #f0f2f5 100%);
  padding-bottom: 80px;
  min-height: 100vh;
  position: relative;
}

/* 顶部区域样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.update-time {
  font-size: 14px;
  color: #666;
  background: linear-gradient(135deg, #666, #999);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 统计信息样式 */
.overview {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.stat-item {
  flex: 1;
  position: relative;
  text-align: center;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
}

.divider {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, #f0f0f0, transparent);
  margin: 0 20px;
}

.stat-badge {
  position: absolute;
  top: -12px;
  right: 5%;
  transform: translateX(50%);
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.2);
}

.stat-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.stat-value {
  font-size: 24px;
  color: #1a1a1a;
  font-weight: 600;
  background: linear-gradient(135deg, #1a1a1a, #333);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
}

.unit {
  font-size: 13px;
  color: #666;
  font-weight: normal;
  margin-left: 2px;
}

/* 搜索区域样式 */
.search-section {
  transition: all 0.3s ease;
  background: #f8f9fa;
  z-index: 10;
}

.search-section.fixed {
  position: fixed;
  top: 44px;
  left: 0;
  right: 0;
  padding: 5px;
  margin: 0;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.search-bar {
  display: flex;
  gap: 8px;
  margin-bottom: 5px;
}

.search-bar input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  font-size: 14px;
  background: #fff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

.search-bar input:focus {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.search-bar button {
  padding: 0 20px;
  background: linear-gradient(135deg, #409EFF, #3a8ee6);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  height: 44px;
  min-width: 60px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.search-bar button:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.2);
}

.filter-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  padding: 8px 16px;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 20px;
  font-size: 13px;
  color: #666;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

.tag.active {
  background: linear-gradient(135deg, #409EFF, #3a8ee6);
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

/* 职位卡片样式 */
.job-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.job-card {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.job-header {
  margin-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.job-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: #1a1a1a;
  line-height: 1.3;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #1a1a1a 0%, #2c3e50 50%, #34495e 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 职位信息区域样式 */
.job-info-section {
  margin-bottom: 10px;
}



.department-info {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
  border-radius: 16px;
  border: 1px solid #b3d8ff;
  width: fit-content;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
  margin-bottom: 10px;
}

.department-name {
  font-size: 13px;
  font-weight: 600;
  color: #409eff;
  background: linear-gradient(135deg, #409eff, #1890ff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 职位要求信息样式 */
.requirements-section {
  margin-bottom: 10px;
}

.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.requirement-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.requirement-text {
  font-size: 13px;
  color: #555;
  font-weight: 500;
  line-height: 1.2;
}

/* 招聘标签样式 */
.recruitment-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 6px;
}

.recruitment-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.company-tag {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.recruitment-tag.type-内部招聘 {
  background: #f6ffed;
  color: #52c41a;
  border-color: #b7eb8f;
}

.recruitment-tag.type-人员借调 {
  background: #fff7e6;
  color: #fa8c16;
  border-color: #ffd591;
}

.recruitment-tag.type-人才帮扶 {
  background: #f9f0ff;
  color: #722ed1;
  border-color: #d3adf7;
}

.recruitment-tag.type-劳务协作 {
  background: #e6fffb;
  color: #13c2c2;
  border-color: #87e8de;
}

.job-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.job-tags .tag {
  background: linear-gradient(135deg, #f0f2f5 0%, #e6e8eb 100%);
  color: #666;
  font-size: 12px;
  padding: 6px 12px;
  box-shadow: none;
}

.job-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #999;
  font-size: 12px;
  position: relative;
}

.views {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: auto;
  position: relative;
  padding-right: 8px;
}

.views::before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 12px;
  background: linear-gradient(to bottom, transparent, #e8e8e8, transparent);
}

.views i {
  font-size: 14px;
  color: #999;
}

.applications {
  display: flex;
  align-items: center;
  gap: 4px;
}

.applications i {
  font-size: 14px;
  color: #999;
}

.publish-time {
  color: #999;
}

/* 底部简历浮动框样式 */
.resume-float {
  position: fixed;
  left: 50%;
  bottom: 20px;
  transform: translateX(-50%);
  background: #409EFF;
  padding: 12px 32px;
  border-radius: 24px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  transition: all 0.3s ease;
}

.resume-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resume-icon {
  font-size: 20px;
  color: #fff;
}

.resume-text {
  font-size: 14px;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.resume-status {
  font-size: 12px;
  opacity: 0.9;
}

.resume-float:active {
  transform: translateX(-50%) scale(0.95);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 消息入口样式 */
.message-entry {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.message-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: #ff4d4f;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(255, 77, 79, 0.4);
}

/* 加载状态样式 */
.loading-more, .no-more {
  text-align: center;
  padding: 16px 0;
  color: #999;
  font-size: 14px;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 空状态样式 */
.no-data {
  text-align: center;
  padding: 40px 0;
  color: #999;
  font-size: 14px;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 16px;
  margin-top: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

/* 占位元素样式 */
.search-placeholder {
  height: 100px;
  margin-bottom: 16px;
}

/* 招聘单位弹窗样式 */
.company-popup {
  background: #fff;
  border-radius: 16px 16px 0 0;
  padding: 16px;
  max-height: 70vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.popup-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.company-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 8px;
  overflow-y: auto;
  flex: 1;
}

.company-grid-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.company-grid-item.active {
  background: #e6f7ff;
  border: 1px solid #409EFF;
}

.company-grid-item:active {
  transform: scale(0.98);
}

.company-name {
  font-size: 16px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.company-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.location {
  display: flex;
  align-items: center;
  gap: 4px;
}

.job-count {
  color: #409EFF;
}

/* 职位统计信息区域样式 */
.job-stats-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 6px;
}

.stats-left {
  display: flex;
  align-items: center;
}

.publish-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 8px;
}

.stats-right {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: nowrap;
}

.stat-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.stat-item:nth-child(1) {
  background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
  border-color: #b3d8ff;
}

.stat-item:nth-child(2) {
  background: linear-gradient(135deg, #f6ffed, #f0f9ff);
  border-color: #b7eb8f;
}

/* 底部空白容器样式 */
.popup-bottom-padding {
  height: 50px; /* 与tabbar高度相同 */
  width: 100%;
  flex-shrink: 0;
}
</style>
