package com.ruoyi.talent.mapper;

import java.util.List;
import com.ruoyi.talent.domain.CompanyAdmin;
import org.apache.ibatis.annotations.Param;

/**
 * 企业管理员关联Mapper接口
 * 
 * <AUTHOR>
 */
public interface CompanyAdminMapper 
{
    /**
     * 查询企业的管理员列表
     * 
     * @param companyId 企业ID
     * @return 管理员列表
     */
    public List<CompanyAdmin> selectCompanyAdminList(Long companyId);

    /**
     * 批量新增企业管理员关联
     * 
     * @param companyAdminList 企业管理员关联列表
     * @return 结果
     */
    public int batchInsertCompanyAdmin(List<CompanyAdmin> companyAdminList);

    /**
     * 删除企业管理员关联
     * 
     * @param companyId 企业ID
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteCompanyAdmin(@Param("companyId") Long companyId, @Param("userId") Long userId);

    /**
     * 检查用户是否是企业管理员
     * 
     * @param companyId 企业ID
     * @param userId 用户ID
     * @return 结果
     */
    public int checkIsCompanyAdmin(@Param("companyId") Long companyId, @Param("userId") Long userId);

    /**
     * 获取用户管理的企业ID
     * 
     * @param userId 用户ID
     * @return 企业ID
     */
    public Long selectAdminCompanyId(@Param("userId") Long userId);
} 