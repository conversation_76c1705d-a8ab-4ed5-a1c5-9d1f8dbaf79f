import request from '@/utils/request'

// 查询公司列表
export function listCompany(query) {
    return request({
        url: '/talent/company/list',
        method: 'get',
        params: query
    })
}

// 查询公司详细
export function getCompany(id) {
    return request({
        url: '/talent/company/' + id,
        method: 'get'
    })
}

// 新增公司
export function addCompany(data) {
    return request({
        url: '/talent/company',
        method: 'post',
        data: data
    })
}

// 修改公司
export function updateCompany(data) {
    return request({
        url: '/talent/company',
        method: 'put',
        data: data
    })
}

// 删除公司
export function delCompany(id) {
    return request({
        url: '/talent/company/' + id,
        method: 'delete'
    })
}

// 获取所有公司（用于下拉选择）
export function listAllCompanies() {
    return request({
        url: '/talent/company/listAll',
        method: 'get'
    })
}

// 查询企业管理员列表
export function getCompanyAdmins(companyId) {
    return request({
        url: '/talent/company-admin/list/' + companyId,
        method: 'get'
    })
}

// 搜索用户列表
export function searchUserList(query) {
    return request({
        url: '/talent/company-admin/search-users',
        method: 'get',
        params: query
    })
}

// 绑定企业管理员
export function bindCompanyAdmins(data) {
    return request({
        url: '/talent/company-admin/bind',
        method: 'post',
        data: data
    })
}

// 解绑企业管理员
export function unbindCompanyAdmin(data) {
    return request({
        url: '/talent/company-admin/unbind',
        method: 'delete',
        params: data
    })
}

// 检查用户是否是企业管理员
export function checkIsCompanyAdmin(query) {
    return request({
        url: '/talent/company-admin/check',
        method: 'get',
        params: query
    })
}