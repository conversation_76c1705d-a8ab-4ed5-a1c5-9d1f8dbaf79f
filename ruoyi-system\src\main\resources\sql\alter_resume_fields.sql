-- 添加字段到resume表
ALTER TABLE resume 
ADD COLUMN age INT NULL COMMENT '年龄',
ADD COLUMN nation VARCHAR(50) NULL COMMENT '民族',
ADD COLUMN native_place VARCHAR(100) NULL COMMENT '籍贯',
ADD COLUMN political_status VARCHAR(50) NULL COMMENT '政治面貌',
ADD COLUMN technical_position VARCHAR(100) NULL COMMENT '专业技术职务';

-- 添加字段到resume_education表
ALTER TABLE resume_education
ADD COLUMN is_highest TINYINT(1) DEFAULT 0 COMMENT '是否为最高学历';

-- 更新已有数据的民族默认值
UPDATE resume SET nation = '汉族' WHERE nation IS NULL;

-- 更新已有数据的政治面貌默认值
UPDATE resume SET political_status = '群众' WHERE political_status IS NULL;

-- 根据出生日期计算年龄
UPDATE resume 
SET age = TIMESTAMPDIFF(YEAR, birthday, CURDATE())
WHERE birthday IS NOT NULL AND age IS NULL;

-- 更新教育经历表，为每个简历的第一条教育经历设置为最高学历
UPDATE resume_education e1
JOIN (
    SELECT resume_id, MIN(id) as first_edu_id
    FROM resume_education
    GROUP BY resume_id
) e2 ON e1.resume_id = e2.resume_id AND e1.id = e2.first_edu_id
SET e1.is_highest = 1; 