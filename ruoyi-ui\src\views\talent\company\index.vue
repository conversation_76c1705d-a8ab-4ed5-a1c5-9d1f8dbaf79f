<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司地址" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入公司地址"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司规模" prop="scale">
        <el-select v-model="queryParams.scale" placeholder="请选择公司规模" clearable>
          <el-option
            v-for="dict in scaleOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="公司行业" prop="industry">
        <el-select v-model="queryParams.industry" placeholder="请选择公司行业" clearable>
          <el-option
            v-for="dict in industryOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="公司性质" prop="nature">
        <el-select v-model="queryParams.nature" placeholder="请选择公司性质" clearable>
          <el-option
            v-for="dict in natureOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['talent:company:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['talent:company:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['talent:company:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="companyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="公司ID" align="center" prop="id" />
      <el-table-column label="所属单位" align="center" prop="parentName" :show-overflow-tooltip="true" />
      <el-table-column label="公司名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="公司Logo" align="center" prop="logo">
        <template #default="scope">
          <el-image 
            v-if="scope.row.logo" 
            style="width: 50px; height: 50px"
            :src="scope.row.logo" 
            :preview-src-list="[scope.row.logo]">
          </el-image>
          <el-avatar v-else :size="50" icon="UserFilled"></el-avatar>
        </template>
      </el-table-column>
      <el-table-column label="公司地址" align="center" prop="location" :show-overflow-tooltip="true" />
      <el-table-column label="公司规模" align="center" prop="scale" />
      <el-table-column label="公司行业" align="center" prop="industry" />
      <el-table-column label="公司性质" align="center" prop="nature" />
      <el-table-column label="职位数量" align="center" prop="jobCount" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
            {{ scope.row.status === '0' ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['talent:company:edit']"
          >修改</el-button>
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['talent:company:delete']"
          >删除</el-button>
          <el-button
            type="text"
            icon="User"
            @click="handleBindAdmin(scope.row)"
            v-hasPermi="['talent:company:bind']"
          >绑定管理员</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="handlePagination"
    />

    <!-- 添加或修改公司对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="companyRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="所属单位" prop="parentId">
          <el-select v-model="form.parentId" placeholder="请选择所属单位" clearable filterable>
            <el-option
              v-for="item in parentOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公司名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="公司Logo" prop="logo">
          <el-upload
            class="avatar-uploader"
            action="/dev-api/common/upload"
            :headers="headers"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload">
            <img v-if="form.logo" :src="form.logo" class="avatar">
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="公司描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入公司描述" />
        </el-form-item>
        <el-form-item label="公司地址" prop="location">
          <el-input v-model="form.location" placeholder="请输入公司地址" />
        </el-form-item>
        <el-form-item label="公司规模" prop="scale">
          <el-select v-model="form.scale" placeholder="请选择公司规模">
            <el-option
              v-for="dict in scaleOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公司行业" prop="industry">
          <el-select v-model="form.industry" placeholder="请选择公司行业">
            <el-option
              v-for="dict in industryOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公司性质" prop="nature">
          <el-select v-model="form.nature" placeholder="请选择公司性质">
            <el-option
              v-for="dict in natureOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 绑定管理员对话框 -->
    <el-dialog :title="'绑定企业管理员 - ' + bindForm.companyName" v-model="bindDialogVisible" width="600px" append-to-body>
      <el-form ref="bindFormRef" :model="bindForm" label-width="100px">
        <el-form-item label="搜索用户">
          <el-input
            v-model="userSearchKeyword"
            placeholder="输入用户名或手机号搜索"
            clearable
            @keyup.enter="handleSearchUsers"
          >
            <template #append>
              <el-button :icon="Search" @click="handleSearchUsers">搜索</el-button>
            </template>
          </el-input>
        </el-form-item>

        <!-- 用户列表 -->
        <el-table
          v-loading="userTableLoading"
          :data="userList"
          height="300"
          @selection-change="handleUserSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="用户名" prop="userName" />
          <el-table-column label="手机号" prop="phonenumber" />
          <el-table-column label="邮箱" prop="email" />
          <el-table-column label="状态" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
                {{ scope.row.status === '0' ? '正常' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 已绑定的管理员列表 -->
        <div class="mt20">
          <div class="sub-title">已绑定的管理员</div>
          <el-table
            v-loading="adminTableLoading"
            :data="currentAdmins"
            height="200"
          >
            <el-table-column label="用户名" prop="userName" />
            <el-table-column label="手机号" prop="phonenumber" />
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button
                  type="text"
                  icon="Delete"
                  @click="handleUnbindAdmin(scope.row)"
                >解绑</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBindForm">确 定</el-button>
          <el-button @click="cancelBind">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue';
import { listCompany, getCompany, delCompany, addCompany, updateCompany, listAllCompanies, searchUserList, getCompanyAdmins, bindCompanyAdmins, unbindCompanyAdmin } from "@/api/talent/company";
import { Plus, UserFilled, User, Search } from '@element-plus/icons-vue';
import { getToken } from "@/utils/auth";

// 组件声明
const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 公司表格数据
const companyList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 公司状态选项
const statusOptions = ref([
  { value: "0", label: "正常" },
  { value: "1", label: "停用" }
]);

// 公司规模选项
const scaleOptions = ref([
  { value: "0-50人", label: "0-50人" },
  { value: "50-100人", label: "50-100人" },
  { value: "100-500人", label: "100-500人" },
  { value: "500-1000人", label: "500-1000人" },
  { value: "1000人以上", label: "1000人以上" }
]);

// 公司行业选项
const industryOptions = ref([
  { value: "互联网/IT", label: "互联网/IT" },
  { value: "金融", label: "金融" },
  { value: "房地产", label: "房地产" },
  { value: "教育", label: "教育" },
  { value: "医疗健康", label: "医疗健康" },
  { value: "制造业", label: "制造业" },
  { value: "其他", label: "其他" }
]);

// 公司性质选项
const natureOptions = ref([
  { value: "国企", label: "国企" },
  { value: "外企", label: "外企" },
  { value: "合资", label: "合资" },
  { value: "民营", label: "民营" },
  { value: "创业公司", label: "创业公司" },
  { value: "其他", label: "其他" }
]);

// 表单参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 20, // 修改每页显示20条记录，原来是10
  name: undefined,
  location: undefined,
  scale: undefined,
  industry: undefined,
  nature: undefined,
  status: undefined
});

// 表单校验
const rules = ref({
  name: [{ required: true, message: "公司名称不能为空", trigger: "blur" }],
  location: [{ required: true, message: "公司地址不能为空", trigger: "blur" }],
  scale: [{ required: true, message: "公司规模不能为空", trigger: "change" }],
  industry: [{ required: true, message: "公司行业不能为空", trigger: "change" }],
  nature: [{ required: true, message: "公司性质不能为空", trigger: "change" }],
  status: [{ required: true, message: "状态不能为空", trigger: "change" }]
});

// 表单数据对象
const form = ref({
  id: undefined,
  parentId: undefined,
  name: undefined,
  logo: undefined,
  description: undefined,
  location: undefined,
  scale: undefined,
  industry: undefined,
  nature: undefined,
  status: "0"
});

// 父级单位选项
const parentOptions = ref([]);

// 文件上传headers
const headers = ref({
  Authorization: "Bearer " + getToken()
});

// 绑定管理员相关数据
const bindDialogVisible = ref(false);
const userTableLoading = ref(false);
const adminTableLoading = ref(false);
const userSearchKeyword = ref('');
const userList = ref([]);
const currentAdmins = ref([]);
const selectedUsers = ref([]);

const bindForm = ref({
  companyId: undefined,
  companyName: '',
  adminIds: []
});

/** 查询公司列表 */
function getList() {
  loading.value = true;
  listCompany(queryParams.value).then(response => {
    companyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询父级单位列表 */
function getParentCompanies() {
  listAllCompanies().then(response => {
    let companies = response.data || [];
    // 如果是编辑模式，需要过滤掉自己和所有子单位，防止循环引用
    if (form.value.id) {
      // 过滤掉当前编辑的单位自身
      companies = companies.filter(item => item.id !== form.value.id);
      
      // 递归查找所有子单位ID
      const findChildIds = (parentId) => {
        const childIds = [];
        companies.forEach(item => {
          if (item.parentId === parentId) {
            childIds.push(item.id);
            childIds.push(...findChildIds(item.id));
          }
        });
        return childIds;
      };
      
      // 过滤掉所有子单位
      const childIds = findChildIds(form.value.id);
      companies = companies.filter(item => !childIds.includes(item.id));
    }
    
    parentOptions.value = companies;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    parentId: undefined,
    name: undefined,
    logo: undefined,
    description: undefined,
    location: undefined,
    scale: undefined,
    industry: undefined,
    nature: undefined,
    status: "0"
  };
  proxy.resetForm("companyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getParentCompanies();
  open.value = true;
  title.value = "添加公司";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value[0];
  getParentCompanies();
  getCompany(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改公司";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["companyRef"].validate(valid => {
    if (valid) {
      if (form.value.id) {
        updateCompany(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCompany(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除公司编号为"' + _ids + '"的数据项？').then(function() {
    return delCompany(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 处理头像上传成功 */
function handleAvatarSuccess(res, file) {
  if (res.code === 200) {
    form.value.logo = res.url;
  } else {
    proxy.$modal.msgError(res.msg);
  }
}

/** 上传前检查文件 */
function beforeAvatarUpload(file) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    proxy.$modal.msgError('上传头像图片只能是 JPG 或 PNG 格式!');
    return false;
  }
  if (!isLt2M) {
    proxy.$modal.msgError('上传头像图片大小不能超过 2MB!');
    return false;
  }
  return true;
}

/** 加载公司当前管理员 */
async function loadCompanyAdmins(companyId) {
  adminTableLoading.value = true;
  try {
    console.log('Loading admins for company:', companyId);
    const res = await getCompanyAdmins(companyId);
    console.log('Company admins response:', res);
    currentAdmins.value = res.data;
    bindDialogVisible.value = true;
  } catch (error) {
    console.error('获取管理员列表失败:', error);
    proxy.$modal.msgError("获取管理员列表失败");
  } finally {
    adminTableLoading.value = false;
  }
}

/** 打开绑定管理员弹窗 */
function handleBindAdmin(row) {
  bindForm.value.companyId = row.id;
  bindForm.value.companyName = row.name;
  // 加载当前公司的管理员列表
  loadCompanyAdmins(row.id);
}

/** 搜索用户 */
async function handleSearchUsers() {
  if (!userSearchKeyword.value) {
    proxy.$modal.msgError("请输入搜索关键词");
    return;
  }
  userTableLoading.value = true;
  try {
    const res = await searchUserList({
      userName: userSearchKeyword.value
    });
    userList.value = res.rows;
  } catch (error) {
    console.error('搜索用户失败:', error);
    proxy.$modal.msgError("搜索用户失败");
  } finally {
    userTableLoading.value = false;
  }
}

/** 用户选择变化 */
function handleUserSelectionChange(selection) {
  selectedUsers.value = selection;
}

/** 解绑管理员 */
async function handleUnbindAdmin(admin) {
  try {
    await proxy.$modal.confirm('确认解除该用户的企业管理员身份吗？');
    await unbindCompanyAdmin({
      companyId: bindForm.value.companyId,
      userId: admin.userId
    });
    proxy.$modal.msgSuccess("解绑成功");
    loadCompanyAdmins(bindForm.value.companyId);
  } catch (error) {
    console.log(error);
  }
}

/** 提交绑定 */
async function submitBindForm() {
  if (selectedUsers.value.length === 0) {
    proxy.$modal.msgError("请选择要绑定的用户");
    return;
  }

  try {
    await bindCompanyAdmins({
      companyId: bindForm.value.companyId,
      userIds: selectedUsers.value.map(user => user.userId)
    });
    proxy.$modal.msgSuccess("绑定成功");
    // 重新加载管理员列表
    await loadCompanyAdmins(bindForm.value.companyId);
    // 清空选择的用户
    userList.value = [];
    selectedUsers.value = [];
  } catch (error) {
    proxy.$modal.msgError("绑定失败");
  }
}

/** 取消绑定 */
function cancelBind() {
  bindDialogVisible.value = false;
  userSearchKeyword.value = '';
  userList.value = [];
  selectedUsers.value = [];
  bindForm.value = {
    companyId: undefined,
    companyName: '',
    adminIds: []
  };
}

/** 处理分页变更 */
function handlePagination(val) {
  queryParams.value.pageNum = val.page;
  queryParams.value.pageSize = val.limit;
  getList();
}

// 初始调用
onMounted(() => {
  getList();
});
</script>

<style scoped>
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.mt20 {
  margin-top: 20px;
}

.sub-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
  font-weight: bold;
}
</style> 