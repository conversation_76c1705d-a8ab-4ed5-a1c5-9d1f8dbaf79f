package com.ruoyi.system.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class Resume extends BaseEntity {
    private String id;
    private String userId;
    private String resumeTitle;
    private String name;
    private String avatar;
    private String gender;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;
    
    private String phone;
    private String email;
    private String location;
    private Boolean isDefault;
    private Integer completeness;
    private Integer views;
    private String status;
    
    // 新增字段
    private Integer age;
    private String nation;
    private String nativePlace;
    private String politicalStatus;
    private String technicalPosition;
    
    // 现任职务
    private String currentPosition;
    
    // 使用专门的实体类来接收关联数据，而不是Map
    private List<ResumeEducation> education;
    private List<ResumeWork> work;
    private List<ResumeProject> project;
    private List<ResumeSkill> skills;

    /** 搜索值 */
    @JsonIgnore
    private String searchValue;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 备注 */
    private String remark;
} 