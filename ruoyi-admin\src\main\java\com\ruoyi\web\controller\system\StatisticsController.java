package com.ruoyi.web.controller.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.service.IStatisticsService;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/system/statistics")
public class StatisticsController extends BaseController {

    @Autowired
    private IStatisticsService statisticsService;

    /**
     * 获取最新统计数据
     */
    @GetMapping("/latest")
//    @PreAuthorize("@ss.hasPermi('system:statistics:list')")
    public AjaxResult getLatestStatistics() {
        return success(statisticsService.getLatestStatistics());
    }

    /**
     * 根据日期获取统计数据
     */
    @GetMapping("/date/{statisticsDate}")
//    @PreAuthorize("@ss.hasPermi('system:statistics:list')")
    public AjaxResult getStatisticsByDate(@PathVariable("statisticsDate") String statisticsDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = sdf.parse(statisticsDate);
            return success(statisticsService.getStatisticsByDate(date));
        } catch (ParseException e) {
            return error("日期格式错误，请使用yyyy-MM-dd格式");
        }
    }
}