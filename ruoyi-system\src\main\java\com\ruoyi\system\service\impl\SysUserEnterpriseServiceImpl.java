package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SysUserEnterprise;
import com.ruoyi.system.mapper.SysUserEnterpriseMapper;
import com.ruoyi.system.service.ISysUserEnterpriseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysUserEnterpriseServiceImpl implements ISysUserEnterpriseService {

    @Autowired
    private SysUserEnterpriseMapper userEnterpriseMapper;

    @Override
    public SysUserEnterprise selectByEnterpriseUserIdAndCorpId(String enterpriseUserId, String enterpriseCorpId) {
        return userEnterpriseMapper.selectByEnterpriseUserIdAndCorpId(enterpriseUserId, enterpriseCorpId);
    }

    @Override
    public List<SysUserEnterprise> selectByUserId(Long userId) {
        return null;
    }

    @Override
    public List<SysUserEnterprise> selectByEnterpriseId(Long enterpriseId) {
        return null;
    }

    @Override
    public int insertSysUserEnterprise(SysUserEnterprise sysUserEnterprise) {
        return 0;
    }

    @Override
    public int updateSysUserEnterprise(SysUserEnterprise sysUserEnterprise) {
        return 0;
    }

    @Override
    public int deleteSysUserEnterprise(Long userId, Long enterpriseId) {
        return 0;
    }

    @Override
    public int deleteSysUserEnterpriseByUserIds(Long[] userIds) {
        return 0;
    }

    @Override
    public int deleteSysUserEnterpriseByEnterpriseIds(Long[] enterpriseIds) {
        return 0;
    }
}