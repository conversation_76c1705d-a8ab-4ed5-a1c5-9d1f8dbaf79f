package com.ruoyi.framework.security.qiwei;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

/**
 * <AUTHOR>
 * @PackageName:com.ruoyi.framework.security.wx
 * @ClassName:EnterpriseAuthenticationProvider
 * @Description: 企业微信登陆鉴权 Provider，要求实现 AuthenticationProvider 接口
 * @date 2024/9/7 11:25
 */
public class EnterpriseAuthenticationProvider implements AuthenticationProvider {

    private UserDetailsService userDetailsService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        EnterpriseAuthenticationToken authenticationToken = (EnterpriseAuthenticationToken) authentication;

        String enterpriseUserId = (String) authenticationToken.getPrincipal();

        UserDetails userDetails = userDetailsService.loadUserByUsername(enterpriseUserId);

        // 此时鉴权成功后，应当重新 new 一个拥有鉴权的 authenticationResult 返回
        EnterpriseAuthenticationToken authenticationResult = new EnterpriseAuthenticationToken(userDetails, userDetails.getAuthorities());

        authenticationResult.setDetails(authenticationToken.getDetails());

        return authenticationResult;
    }


    @Override
    public boolean supports(Class<?> authentication) {
        // 判断 authentication 是不是 WxCodeAuthenticationToken 的子类或子接口
        return EnterpriseAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public UserDetailsService getUserDetailsService() {
        return userDetailsService;
    }

    public void setUserDetailsService(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }
}
